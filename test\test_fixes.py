#!/usr/bin/env python3
"""
测试修复效果的脚本

测试内容：
1. current_table_name 属性是否正确初始化
2. 字段映射是否正确应用
3. 全局排序功能是否正常工作
4. 异常处理是否完善
"""

import sys
import os
import unittest
from unittest.mock import Mock, patch, MagicMock

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.gui.prototype.prototype_main_window import PrototypeMainWindow
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from src.modules.config.config_manager import ConfigManager
from src.modules.database.database_manager import DatabaseManager


class TestFixes(unittest.TestCase):
    """测试修复效果"""
    
    def setUp(self):
        """设置测试环境"""
        # 创建模拟对象
        self.mock_config_manager = Mock(spec=ConfigManager)
        self.mock_db_manager = Mock(spec=DatabaseManager)
        self.mock_table_manager = Mock(spec=DynamicTableManager)
        
        # 创建主窗口实例
        self.main_window = PrototypeMainWindow(
            config_manager=self.mock_config_manager,
            db_manager=self.mock_db_manager,
            dynamic_table_manager=self.mock_table_manager
        )
    
    def test_current_table_name_initialization(self):
        """测试 current_table_name 属性是否正确初始化"""
        # 检查属性是否存在
        self.assertTrue(hasattr(self.main_window, 'current_table_name'))
        
        # 检查初始值
        self.assertEqual(self.main_window.current_table_name, "")
        
        # 测试设置值
        test_table_name = "salary_data_2025_07_active_employees"
        self.main_window.current_table_name = test_table_name
        self.assertEqual(self.main_window.current_table_name, test_table_name)
        
        print("✅ current_table_name 属性初始化测试通过")
    
    def test_field_mapping_application(self):
        """测试字段映射应用"""
        import pandas as pd
        
        # 创建测试数据
        test_df = pd.DataFrame({
            'employee_id': ['001', '002'],
            'employee_name': ['张三', '李四'],
            'position_salary_2025': [5000.0, 6000.0]
        })
        
        # 模拟字段映射配置
        mock_mapping = {
            'employee_id': '工号',
            'employee_name': '姓名',
            'position_salary_2025': '2025年岗位工资'
        }
        
        # 模拟配置同步管理器
        mock_config_sync = Mock()
        mock_config_sync.load_mapping.return_value = mock_mapping
        self.main_window.config_sync_manager = mock_config_sync
        
        # 测试字段映射应用
        result_df = self.main_window._apply_field_mapping_to_dataframe(
            test_df, 
            "salary_data_2025_07_active_employees"
        )
        
        # 验证字段名是否正确映射
        expected_columns = ['工号', '姓名', '2025年岗位工资']
        self.assertEqual(list(result_df.columns), expected_columns)
        
        print("✅ 字段映射应用测试通过")
    
    def test_sort_column_conversion(self):
        """测试排序列转换"""
        # 设置测试表名
        test_table_name = "salary_data_2025_07_active_employees"
        self.main_window.current_table_name = test_table_name
        
        # 模拟字段映射
        mock_reverse_mapping = {
            '工号': 'employee_id',
            '姓名': 'employee_name',
            '2025年岗位工资': 'position_salary_2025'
        }
        
        # 模拟 _get_reverse_field_mapping 方法
        self.main_window._get_reverse_field_mapping = Mock(return_value=mock_reverse_mapping)
        
        # 测试列名转换
        chinese_column = '2025年岗位工资'
        english_column = self.main_window._convert_column_name_to_db_field(
            chinese_column, 
            test_table_name
        )
        
        self.assertEqual(english_column, 'position_salary_2025')
        
        print("✅ 排序列转换测试通过")
    
    def test_exception_handling(self):
        """测试异常处理"""
        # 测试空表名的异常处理
        self.main_window.current_table_name = ""
        
        # 这应该不会抛出异常
        try:
            self.main_window._reload_current_page_with_sort()
            print("✅ 异常处理测试通过")
        except Exception as e:
            self.fail(f"异常处理失败: {e}")
    
    def test_global_sort_functionality(self):
        """测试全局排序功能"""
        # 设置测试环境
        test_table_name = "salary_data_2025_07_active_employees"
        self.main_window.current_table_name = test_table_name
        
        # 模拟事件总线
        mock_event_bus = Mock()
        self.main_window.event_bus = mock_event_bus
        
        # 模拟字段映射
        mock_reverse_mapping = {'2025年岗位工资': 'position_salary_2025'}
        self.main_window._get_reverse_field_mapping = Mock(return_value=mock_reverse_mapping)
        
        # 测试排序请求发布
        sort_columns = [{'column_name': '2025年岗位工资', 'order': 'ascending'}]
        
        try:
            self.main_window._publish_sort_request_event(test_table_name, sort_columns, 1)
            
            # 验证事件是否被发布
            self.assertTrue(mock_event_bus.publish.called)
            print("✅ 全局排序功能测试通过")
        except Exception as e:
            self.fail(f"全局排序功能测试失败: {e}")


def run_tests():
    """运行所有测试"""
    print("🔧 开始测试修复效果...")
    print("=" * 50)
    
    # 创建测试套件
    suite = unittest.TestLoader().loadTestsFromTestCase(TestFixes)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    print("=" * 50)
    if result.wasSuccessful():
        print("🎉 所有测试通过！修复效果良好。")
    else:
        print("❌ 部分测试失败，需要进一步修复。")
        for failure in result.failures:
            print(f"失败: {failure[0]}")
            print(f"原因: {failure[1]}")
        for error in result.errors:
            print(f"错误: {error[0]}")
            print(f"原因: {error[1]}")
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
