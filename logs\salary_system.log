2025-07-15 16:50:16.788 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-07-15 16:50:16.788 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-07-15 16:50:16.788 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-07-15 16:50:16.788 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-07-15 16:50:16.788 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-07-15 16:50:16.788 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-07-15 16:50:18.303 | INFO     | __main__:setup_app_logging:239 | 月度工资异动处理系统 v2.0.0-refactored 启动
2025-07-15 16:50:18.303 | INFO     | __main__:main:303 | 初始化核心管理器...
2025-07-15 16:50:18.303 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-07-15 16:50:18.303 | INFO     | src.modules.system_config.config_manager:load_config:338 | 正在加载配置文件: C:\test\salary_changes\salary_changes\config.json
2025-07-15 16:50:18.303 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:252 | 配置文件验证通过
2025-07-15 16:50:18.303 | INFO     | src.modules.system_config.config_manager:load_config:350 | 配置文件加载成功
2025-07-15 16:50:18.303 | INFO     | src.modules.data_storage.database_manager:_initialize_database:101 | 开始初始化数据库...
2025-07-15 16:50:18.335 | INFO     | src.modules.data_storage.database_manager:_initialize_database:156 | 数据库初始化完成
2025-07-15 16:50:18.335 | INFO     | src.modules.data_storage.database_manager:__init__:97 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-07-15 16:50:18.335 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-07-15 16:50:18.335 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:104 | 动态表管理器初始化完成
2025-07-15 16:50:18.335 | INFO     | __main__:main:308 | 核心管理器初始化完成。
2025-07-15 16:50:18.335 | INFO     | src.core.data_source_unification_manager:__init__:128 | 数据源统一管理器初始化完成
2025-07-15 16:50:18.335 | INFO     | src.core.table_sort_state_manager:__init__:174 | 表级排序状态管理器初始化完成
2025-07-15 16:50:18.366 | INFO     | src.core.architecture_factory:__init__:62 | 架构重构工厂初始化完成
2025-07-15 16:50:18.366 | INFO     | src.core.architecture_factory:initialize_architecture:72 | 开始初始化架构重构系统...
2025-07-15 16:50:18.474 | WARNING  | src.modules.data_import.config_sync_manager:_do_config_initialization:129 | 创建了最小默认配置，可能需要重新导入数据以生成字段映射
2025-07-15 16:50:18.474 | INFO     | src.modules.data_import.config_sync_manager:__init__:70 | 配置同步管理器初始化完成
2025-07-15 16:50:18.474 | INFO     | src.core.event_bus:__init__:227 | 事件总线初始化完成
2025-07-15 16:50:18.474 | INFO     | src.core.unified_state_manager:_load_state:435 | 状态文件不存在，使用默认状态
2025-07-15 16:50:18.474 | INFO     | src.core.unified_state_manager:__init__:177 | 统一状态管理器初始化完成
2025-07-15 16:50:18.474 | INFO     | src.core.unified_data_request_manager:__init__:164 | 统一数据请求管理器初始化完成
2025-07-15 16:50:18.474 | INFO     | src.core.unified_data_request_manager:__init__:164 | 统一数据请求管理器初始化完成
2025-07-15 16:50:18.474 | INFO     | src.core.unified_state_manager:_load_state:435 | 状态文件不存在，使用默认状态
2025-07-15 16:50:18.474 | INFO     | src.core.unified_state_manager:__init__:177 | 统一状态管理器初始化完成
2025-07-15 16:50:18.474 | INFO     | src.services.table_data_service:__init__:68 | 表格数据服务初始化完成
2025-07-15 16:50:18.474 | INFO     | src.core.architecture_factory:initialize_architecture:103 | 🎉 架构重构系统初始化成功！耗时: 108.1ms
2025-07-15 16:50:18.474 | INFO     | src.gui.prototype.prototype_main_window:__init__:2823 | ✅ 新架构集成成功！
2025-07-15 16:50:18.474 | INFO     | src.gui.prototype.prototype_main_window:_inject_config_sync_manager_to_existing_tables:2938 | 🔧 [修复标识] ConfigSyncManager重新注入完成，已更新0个表格实例
2025-07-15 16:50:18.474 | INFO     | src.gui.prototype.prototype_main_window:_setup_new_architecture_listeners:2904 | ✅ 新架构事件监听器设置完成
2025-07-15 16:50:18.474 | INFO     | src.gui.widgets.pagination_cache_manager:__init__:107 | 分页缓存管理器初始化完成 - 最大条目数: 50, 最大内存: 50MB
2025-07-15 16:50:18.474 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:121 | 响应式布局管理器初始化完成
2025-07-15 16:50:18.474 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初始化完成
2025-07-15 16:50:18.709 | INFO     | src.gui.prototype.prototype_main_window:_create_menu_bar:2110 | 菜单栏创建完成
2025-07-15 16:50:18.709 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-07-15 16:50:18.709 | INFO     | src.modules.system_config.user_preferences:load_preferences:183 | 正在加载偏好设置: C:\test\salary_changes\salary_changes\user_preferences.json
2025-07-15 16:50:18.726 | INFO     | src.modules.system_config.user_preferences:load_preferences:193 | 偏好设置加载成功
2025-07-15 16:50:18.726 | INFO     | src.gui.prototype.prototype_main_window:__init__:2086 | 菜单栏管理器初始化完成
2025-07-15 16:50:18.726 | INFO     | src.gui.table_header_manager:__init__:68 | 表头管理器初始化完成
2025-07-15 16:50:18.726 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:3567 | 管理器设置完成，包含增强版表头管理器
2025-07-15 16:50:18.726 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-07-15 16:50:18.726 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:148 | 智能树形展开算法初始化完成
2025-07-15 16:50:18.740 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:1042 | 开始从元数据动态加载工资数据...
2025-07-15 16:50:18.740 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:1049 | 检测到首次启动，将延迟加载导航数据确保数据库就绪
2025-07-15 16:50:18.742 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:680 | 导航面板已重构：移除功能性导航，专注数据导航
2025-07-15 16:50:18.748 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:715 | 恢复导航状态: 0个展开项
2025-07-15 16:50:18.749 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表', '工资表 > 2025年']
2025-07-15 16:50:18.750 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:521 | 增强导航面板初始化完成
2025-07-15 16:50:18.770 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1312 | 快捷键注册完成: 18/18 个
2025-07-15 16:50:18.772 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1523 | 拖拽排序管理器初始化完成
2025-07-15 16:50:18.776 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1892 | ConfigSyncManager未通过依赖注入提供，使用降级方案
2025-07-15 16:50:18.880 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:133 | 使用已存在的配置文件: state/data/field_mappings.json
2025-07-15 16:50:18.883 | INFO     | src.modules.data_import.config_sync_manager:__init__:70 | 配置同步管理器初始化完成
2025-07-15 16:50:18.885 | INFO     | src.modules.data_import.header_edit_manager:__init__:81 | 表头编辑管理器初始化完成
2025-07-15 16:50:18.888 | INFO     | src.gui.multi_column_sort_manager:__init__:103 | 多列排序管理器初始化完成，最大排序列数: 3
2025-07-15 16:50:18.888 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1917 | 多列排序管理器初始化完成
2025-07-15 16:50:18.890 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1934 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数: 1000
2025-07-15 16:50:18.891 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:3589 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-07-15 16:50:18.892 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:424 | 表格数据已设置: 0 行, 7 列
2025-07-15 16:50:18.903 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2174 | 🔧 [性能分析] 表格数据设置完成: 0 行, 最大可见行数: 1000, 总耗时: 11.46ms
2025-07-15 16:50:18.904 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 初始化和状态: 0.00ms (0.0%)
2025-07-15 16:50:18.904 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 字段映射: 1.04ms (9.0%)
2025-07-15 16:50:18.905 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 数据模型更新: 5.10ms (44.5%)
2025-07-15 16:50:18.913 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 表头设置: 2.74ms (23.9%)
2025-07-15 16:50:18.914 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 设置行数: 0.00ms (0.0%)
2025-07-15 16:50:18.915 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 填充可见数据: 0.00ms (0.0%)
2025-07-15 16:50:18.915 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 调整列宽: 0.00ms (0.0%)
2025-07-15 16:50:18.916 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 设置行号: 2.59ms (22.6%)
2025-07-15 16:50:18.917 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2182 | 🔧 [性能分析] 最耗时步骤: 数据模型更新 (5.10ms)
2025-07-15 16:50:18.918 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1693 | 表格已初始化为空白状态，等待用户导入数据
2025-07-15 16:50:18.924 | INFO     | src.gui.widgets.pagination_widget:__init__:165 | 分页组件初始化完成
2025-07-15 16:50:18.943 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:512 | 控制面板按钮信号连接完成
2025-07-15 16:50:18.961 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-07-15 16:50:18.963 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-07-15 16:50:18.963 | INFO     | src.gui.prototype.prototype_main_window:_setup_shortcuts:3540 | 快捷键设置完成
2025-07-15 16:50:18.964 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:3529 | 主窗口UI设置完成。
2025-07-15 16:50:18.965 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:3578 | 🔧 [全局排序] 全局排序开关连接成功
2025-07-15 16:50:18.966 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:3613 | ✅ 已连接排序指示器变化信号到新架构
2025-07-15 16:50:18.968 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:3634 | ✅ 已连接分页组件事件到新架构
2025-07-15 16:50:18.968 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:3636 | 信号连接设置完成
2025-07-15 16:50:18.970 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:4315 | 已加载字段映射信息，共0个表的映射
2025-07-15 16:50:18.977 | WARNING  | src.gui.prototype.prototype_main_window:set_data:595 | 尝试设置空数据，将显示提示信息。
2025-07-15 16:50:18.978 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:3589 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-07-15 16:50:18.979 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:424 | 表格数据已设置: 0 行, 7 列
2025-07-15 16:50:18.980 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2174 | 🔧 [性能分析] 表格数据设置完成: 0 行, 最大可见行数: 1000, 总耗时: 2.06ms
2025-07-15 16:50:18.980 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 初始化和状态: 0.00ms (0.0%)
2025-07-15 16:50:18.981 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 字段映射: 1.02ms (49.6%)
2025-07-15 16:50:18.982 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 数据模型更新: 0.00ms (0.0%)
2025-07-15 16:50:18.983 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 表头设置: 1.04ms (50.4%)
2025-07-15 16:50:18.983 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 设置行数: 0.00ms (0.0%)
2025-07-15 16:50:18.985 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 填充可见数据: 0.00ms (0.0%)
2025-07-15 16:50:18.985 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 调整列宽: 0.00ms (0.0%)
2025-07-15 16:50:18.986 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 设置行号: 0.00ms (0.0%)
2025-07-15 16:50:18.998 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2182 | 🔧 [性能分析] 最耗时步骤: 表头设置 (1.04ms)
2025-07-15 16:50:18.999 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1693 | 表格已初始化为空白状态，等待用户导入数据
2025-07-15 16:50:18.999 | INFO     | src.gui.widgets.pagination_widget:reset:354 | 分页状态已重置
2025-07-15 16:50:19.000 | WARNING  | src.gui.prototype.prototype_main_window:set_data:595 | 尝试设置空数据，将显示提示信息。
2025-07-15 16:50:19.001 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:3589 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-07-15 16:50:19.002 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:424 | 表格数据已设置: 0 行, 7 列
2025-07-15 16:50:19.003 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2174 | 🔧 [性能分析] 表格数据设置完成: 0 行, 最大可见行数: 1000, 总耗时: 2.63ms
2025-07-15 16:50:19.003 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 初始化和状态: 1.06ms (40.4%)
2025-07-15 16:50:19.004 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 字段映射: 0.53ms (20.3%)
2025-07-15 16:50:19.013 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 数据模型更新: 0.00ms (0.0%)
2025-07-15 16:50:19.014 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 表头设置: 1.03ms (39.3%)
2025-07-15 16:50:19.015 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 设置行数: 0.00ms (0.0%)
2025-07-15 16:50:19.018 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 填充可见数据: 0.00ms (0.0%)
2025-07-15 16:50:19.034 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 调整列宽: 0.00ms (0.0%)
2025-07-15 16:50:19.038 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 设置行号: 0.00ms (0.0%)
2025-07-15 16:50:19.039 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2182 | 🔧 [性能分析] 最耗时步骤: 初始化和状态 (1.06ms)
2025-07-15 16:50:19.040 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1693 | 表格已初始化为空白状态，等待用户导入数据
2025-07-15 16:50:19.040 | INFO     | src.gui.widgets.pagination_widget:reset:354 | 分页状态已重置
2025-07-15 16:50:19.041 | INFO     | src.gui.prototype.prototype_main_window:_setup_legacy_compatibility:6060 | ✅ 老架构兼容性接口设置完成
2025-07-15 16:50:19.042 | INFO     | src.gui.prototype.prototype_main_window:__init__:2878 | 原型主窗口初始化完成
2025-07-15 16:50:19.297 | INFO     | __main__:main:330 | 应用程序启动成功
2025-07-15 16:50:19.304 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: sm (宽度: 1266px)
2025-07-15 16:50:19.305 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:1841 | MainWorkspaceArea 响应式适配: sm
2025-07-15 16:50:19.308 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1003 | 执行延迟的自动选择最新数据...
2025-07-15 16:50:19.309 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1014 | 导航树即将刷新，跳过当前自动选择，将在刷新后执行
2025-07-15 16:50:19.541 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_load_salary_data:1064 | 执行延迟的工资数据加载...
2025-07-15 16:50:19.541 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1230 | 开始强制刷新工资数据导航... (尝试 1/3)
2025-07-15 16:50:19.541 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1238 | 找到工资表节点: 📊 工资表
2025-07-15 16:50:19.541 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_table_list:873 | 首次查询发现表数量异常: 0 个表，期望 1 个，可能需要重试
2025-07-15 16:50:19.654 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:881 | 找到 0 个匹配类型 'salary_data' 的表
2025-07-15 16:50:19.654 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_navigation_tree_data:972 | 在 table_metadata 中未找到任何 'salary_data' 类型的表
2025-07-15 16:50:19.654 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:884 | 找到 3 个总表
2025-07-15 16:50:19.654 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1290 | 检测到数据库中没有工资数据表，直接使用兜底数据
2025-07-15 16:50:19.654 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_load_fallback_salary_data:1187 | 使用兜底数据加载导航
2025-07-15 16:50:19.855 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_post_refresh_auto_select:1093 | 导航树刷新完成，重新执行自动选择...
2025-07-15 16:50:19.855 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1032 | 开始获取最新工资数据路径...
2025-07-15 16:50:19.855 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_table_list:873 | 首次查询发现表数量异常: 0 个表，期望 1 个，可能需要重试
2025-07-15 16:50:19.960 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:881 | 找到 0 个匹配类型 'salary_data' 的表
2025-07-15 16:50:19.960 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1037 | 未找到任何工资数据表
2025-07-15 16:50:19.960 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_post_refresh_auto_select:1098 | 未找到最新工资数据路径
2025-07-15 16:50:33.538 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data:529 | 数据导入功能被触发，发出 import_requested 信号。
2025-07-15 16:50:33.538 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:3868 | 接收到数据导入请求，推断的目标路径: 工资表 > 2025年 > 07月 > 全部在职人员。打开导入对话框。
2025-07-15 16:50:33.538 | INFO     | src.modules.data_import.auto_field_mapping_generator:__init__:60 | 自动字段映射生成器初始化完成
2025-07-15 16:50:33.652 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:133 | 使用已存在的配置文件: state/data/field_mappings.json
2025-07-15 16:50:33.652 | INFO     | src.modules.data_import.config_sync_manager:__init__:70 | 配置同步管理器初始化完成
2025-07-15 16:50:33.652 | INFO     | src.modules.data_import.multi_sheet_importer:__init__:70 | 多Sheet导入器初始化完成
2025-07-15 16:50:33.652 | INFO     | src.gui.widgets.target_selection_widget:_save_config:285 | 配置保存成功
2025-07-15 16:50:33.652 | INFO     | src.gui.widgets.target_selection_widget:set_target_from_path:505 | 从路径设置目标: 工资表 > 2025年 > 07月 > 全部在职人员
2025-07-15 16:50:33.652 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_table_list:873 | 首次查询发现表数量异常: 0 个表，期望 1 个，可能需要重试
2025-07-15 16:50:33.769 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:881 | 找到 0 个匹配类型 'salary_data' 的表
2025-07-15 16:50:33.784 | INFO     | src.gui.main_dialogs:_get_template_fields:1884 | 使用字段模板: 全部在职人员工资表
2025-07-15 16:50:33.816 | INFO     | src.modules.data_import.import_defaults_manager:load_settings:86 | 未找到用户设置文件，使用默认设置
2025-07-15 16:50:33.816 | INFO     | src.gui.main_dialogs:_apply_default_settings:2206 | 已应用默认设置: {'start_row': 1, 'import_mode': 'multi_sheet', 'auto_match_sheet': True, 'include_header': True, 'skip_empty_rows': True, 'create_table_mode': 'sheet_name', 'import_strategy': 'separate_tables', 'table_template': 'salary_data'}
2025-07-15 16:50:33.831 | INFO     | src.gui.main_dialogs:_setup_tooltips:2461 | 工具提示设置完成
2025-07-15 16:50:33.847 | INFO     | src.gui.main_dialogs:_setup_shortcuts:2500 | 快捷键设置完成
2025-07-15 16:50:33.847 | INFO     | src.gui.main_dialogs:__init__:77 | 数据导入对话框初始化完成。
2025-07-15 16:50:41.956 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-07-15 16:50:43.931 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-07-15 16:50:43.933 | INFO     | src.modules.data_import.smart_sheet_matcher:record_user_choice:372 | 记录用户选择: 全部在职人员 -> 全部在职人员工资表
2025-07-15 16:50:43.935 | INFO     | src.gui.main_dialogs:_auto_select_sheet_by_category:2241 | 根据人员类别 '全部在职人员' 自动选择工作表: 全部在职人员工资表 (匹配类型: fuzzy, 得分: 0.60)
2025-07-15 16:50:52.713 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-07-15 16:50:52.901 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-07-15 16:50:52.917 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-07-15 16:50:52.917 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:200 | 开始导入多Sheet Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-07-15 16:50:52.917 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-07-15 16:50:53.120 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-07-15 16:50:53.120 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:211 | 检测到 4 个工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-07-15 16:50:53.135 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-07-15 16:50:53.135 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-07-15 16:50:53.135 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-07-15 16:50:53.245 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:320 | 🔧 [修复标识] Excel读取完成: 16列 (列过滤: 否)
2025-07-15 16:50:53.245 | INFO     | src.modules.data_import.excel_importer:_clean_data:427 | 🔧 [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-07-15 16:50:53.245 | INFO     | src.modules.data_import.excel_importer:_clean_data:438 | 数据清理完成: 保留所有 16 列 (原始 16 列)
2025-07-15 16:50:53.245 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:339 | 导入完成: 3行 x 16列
2025-07-15 16:50:53.245 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:567 | 数据导入过滤: 发现1条姓名为空的记录
2025-07-15 16:50:53.245 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:569 | 过滤记录[行3]: 姓名字段(姓名)为空, 数据: {'序号': '总计', '合计': np.float64(34405.100000000006)}
2025-07-15 16:50:53.245 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:607 | 数据过滤完成: 原始3条记录，过滤1条无效记录，有效记录2条
2025-07-15 16:50:53.245 | INFO     | src.modules.data_import.excel_importer:import_data:286 | 🔧 [修复标识] 数据导入最终完成: 2行 × 16列
2025-07-15 16:50:53.261 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:445 | 未找到工作表 离休人员工资表 的配置，使用智能默认处理
2025-07-15 16:50:53.261 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:726 | 为Sheet '离休人员工资表' 生成智能默认配置: 1 个必需字段
2025-07-15 16:50:53.261 | INFO     | src.modules.data_import.specialized_field_mapping_generator:generate_mapping:139 | 为模板 retired_employees 生成了 21 个字段映射
2025-07-15 16:50:53.261 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:462 | 使用专用模板 retired_employees 生成字段映射: 21 个字段
2025-07-15 16:50:53.261 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:605 | 完整字段映射保存成功: salary_data_2025_07_retired_employees
2025-07-15 16:50:53.261 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:504 | 为表 salary_data_2025_07_retired_employees 生成标准化字段映射: 21 个字段
2025-07-15 16:50:53.261 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:514 | Sheet 离休人员工资表 存在 1 个验证错误
2025-07-15 16:50:53.276 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:523 | Sheet 离休人员工资表 数据处理完成: 2 行
2025-07-15 16:50:53.276 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:360 | Sheet '离休人员工资表' 检测到模板类型: retired_employees
2025-07-15 16:50:53.292 | INFO     | src.modules.data_storage.dynamic_table_manager:_create_table_from_schema:692 | 成功创建表: salary_data_2025_07_retired_employees
2025-07-15 16:50:53.307 | INFO     | src.modules.data_storage.dynamic_table_manager:create_specialized_salary_table:360 | 专用工资数据表创建成功: salary_data_2025_07_retired_employees (模板: retired_employees)
2025-07-15 16:50:53.354 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1131 | 🔧 [修复标识] 导入字段映射加载完成: 28 个映射规则
2025-07-15 16:50:53.354 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1139 | 🔧 [修复标识] 保留未映射列: ['data_source', 'import_time']
2025-07-15 16:50:53.354 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1148 | 🔧 [修复标识] 导入列名映射成功: 18 个字段已映射
2025-07-15 16:50:53.370 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1285 | 成功向表 salary_data_2025_07_retired_employees 保存 2 条数据。
2025-07-15 16:50:53.417 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-07-15 16:50:53.417 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-07-15 16:50:53.417 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-07-15 16:50:53.526 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:320 | 🔧 [修复标识] Excel读取完成: 27列 (列过滤: 否)
2025-07-15 16:50:53.542 | INFO     | src.modules.data_import.excel_importer:_clean_data:427 | 🔧 [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-07-15 16:50:53.542 | INFO     | src.modules.data_import.excel_importer:_clean_data:438 | 数据清理完成: 保留所有 27 列 (原始 27 列)
2025-07-15 16:50:53.542 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:339 | 导入完成: 14行 x 27列
2025-07-15 16:50:53.542 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:567 | 数据导入过滤: 发现1条姓名为空的记录
2025-07-15 16:50:53.542 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:569 | 过滤记录[行14]: 姓名字段(姓名)为空, 数据: {'序号': '总计', '应发工资': np.float64(33607.14625)}
2025-07-15 16:50:53.542 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:607 | 数据过滤完成: 原始14条记录，过滤1条无效记录，有效记录13条
2025-07-15 16:50:53.542 | INFO     | src.modules.data_import.excel_importer:import_data:286 | 🔧 [修复标识] 数据导入最终完成: 13行 × 27列
2025-07-15 16:50:53.542 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:445 | 未找到工作表 退休人员工资表 的配置，使用智能默认处理
2025-07-15 16:50:53.542 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:726 | 为Sheet '退休人员工资表' 生成智能默认配置: 1 个必需字段
2025-07-15 16:50:53.542 | INFO     | src.modules.data_import.specialized_field_mapping_generator:generate_mapping:139 | 为模板 pension_employees 生成了 32 个字段映射
2025-07-15 16:50:53.542 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:462 | 使用专用模板 pension_employees 生成字段映射: 32 个字段
2025-07-15 16:50:53.557 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:605 | 完整字段映射保存成功: salary_data_2025_07_pension_employees
2025-07-15 16:50:53.557 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:504 | 为表 salary_data_2025_07_pension_employees 生成标准化字段映射: 32 个字段
2025-07-15 16:50:53.557 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:514 | Sheet 退休人员工资表 存在 1 个验证错误
2025-07-15 16:50:53.573 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:523 | Sheet 退休人员工资表 数据处理完成: 13 行
2025-07-15 16:50:53.573 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:360 | Sheet '退休人员工资表' 检测到模板类型: pension_employees
2025-07-15 16:50:53.573 | INFO     | src.modules.data_storage.dynamic_table_manager:_create_table_from_schema:692 | 成功创建表: salary_data_2025_07_pension_employees
2025-07-15 16:50:53.573 | INFO     | src.modules.data_storage.dynamic_table_manager:create_specialized_salary_table:360 | 专用工资数据表创建成功: salary_data_2025_07_pension_employees (模板: pension_employees)
2025-07-15 16:50:53.605 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1131 | 🔧 [修复标识] 导入字段映射加载完成: 32 个映射规则
2025-07-15 16:50:53.605 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1139 | 🔧 [修复标识] 保留未映射列: ['data_source', 'import_time']
2025-07-15 16:50:53.605 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1148 | 🔧 [修复标识] 导入列名映射成功: 29 个字段已映射
2025-07-15 16:50:53.620 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1285 | 成功向表 salary_data_2025_07_pension_employees 保存 13 条数据。
2025-07-15 16:50:53.635 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-07-15 16:50:53.635 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-07-15 16:50:53.651 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-07-15 16:50:53.776 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:320 | 🔧 [修复标识] Excel读取完成: 23列 (列过滤: 否)
2025-07-15 16:50:53.792 | INFO     | src.modules.data_import.excel_importer:_clean_data:427 | 🔧 [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-07-15 16:50:53.792 | INFO     | src.modules.data_import.excel_importer:_clean_data:438 | 数据清理完成: 保留所有 23 列 (原始 23 列)
2025-07-15 16:50:53.792 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:339 | 导入完成: 1397行 x 23列
2025-07-15 16:50:53.792 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:567 | 数据导入过滤: 发现1条姓名为空的记录
2025-07-15 16:50:53.792 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:569 | 过滤记录[行1397]: 姓名字段(姓名)为空, 数据: {'2025年岗位工资': np.float64(3971045.71), '2025年薪级工资': np.int64(3138129), '津贴': np.float64(94436.73000000001), '结余津贴': np.int64(78008), '2025年基础性绩效': np.int64(4706933), '卫生费': np.float64(14240.0), '交通补贴': np.float64(306460.0), '物业补贴': np.float64(305860.0), '住房补贴': np.float64(337019.8710385144), '车补': np.float64(12870.0), '通讯补贴': np.float64(10250.0), '2025年奖励性绩效预发': np.int64(2262100), '补发': np.float64(2528.0), '借支': np.float64(122740.12055172413), '应发工资': np.float64(15117140.190486807), '2025公积金': np.int64(2390358), '代扣代存养老保险': np.float64(1967911.5299999986)}
2025-07-15 16:50:53.792 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:607 | 数据过滤完成: 原始1397条记录，过滤1条无效记录，有效记录1396条
2025-07-15 16:50:53.792 | INFO     | src.modules.data_import.excel_importer:import_data:286 | 🔧 [修复标识] 数据导入最终完成: 1396行 × 23列
2025-07-15 16:50:53.807 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:445 | 未找到工作表 全部在职人员工资表 的配置，使用智能默认处理
2025-07-15 16:50:53.807 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:726 | 为Sheet '全部在职人员工资表' 生成智能默认配置: 2 个必需字段
2025-07-15 16:50:53.807 | INFO     | src.modules.data_import.specialized_field_mapping_generator:generate_mapping:139 | 为模板 active_employees 生成了 28 个字段映射
2025-07-15 16:50:53.807 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:462 | 使用专用模板 active_employees 生成字段映射: 28 个字段
2025-07-15 16:50:53.823 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:605 | 完整字段映射保存成功: salary_data_2025_07_active_employees
2025-07-15 16:50:53.823 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:504 | 为表 salary_data_2025_07_active_employees 生成标准化字段映射: 28 个字段
2025-07-15 16:50:53.823 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:514 | Sheet 全部在职人员工资表 存在 2 个验证错误
2025-07-15 16:50:53.823 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:523 | Sheet 全部在职人员工资表 数据处理完成: 1396 行
2025-07-15 16:50:53.823 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:360 | Sheet '全部在职人员工资表' 检测到模板类型: active_employees
2025-07-15 16:50:53.839 | INFO     | src.modules.data_storage.dynamic_table_manager:_create_table_from_schema:692 | 成功创建表: salary_data_2025_07_active_employees
2025-07-15 16:50:53.854 | INFO     | src.modules.data_storage.dynamic_table_manager:create_specialized_salary_table:360 | 专用工资数据表创建成功: salary_data_2025_07_active_employees (模板: active_employees)
2025-07-15 16:50:53.854 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1131 | 🔧 [修复标识] 导入字段映射加载完成: 28 个映射规则
2025-07-15 16:50:53.854 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1139 | 🔧 [修复标识] 保留未映射列: ['data_source', 'import_time']
2025-07-15 16:50:53.870 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1148 | 🔧 [修复标识] 导入列名映射成功: 25 个字段已映射
2025-07-15 16:50:53.964 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1285 | 成功向表 salary_data_2025_07_active_employees 保存 1396 条数据。
2025-07-15 16:50:53.964 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-07-15 16:50:53.964 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-07-15 16:50:53.964 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-07-15 16:50:54.073 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:320 | 🔧 [修复标识] Excel读取完成: 21列 (列过滤: 否)
2025-07-15 16:50:54.089 | INFO     | src.modules.data_import.excel_importer:_clean_data:427 | 🔧 [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-07-15 16:50:54.089 | INFO     | src.modules.data_import.excel_importer:_clean_data:438 | 数据清理完成: 保留所有 21 列 (原始 21 列)
2025-07-15 16:50:54.089 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:339 | 导入完成: 63行 x 21列
2025-07-15 16:50:54.089 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:567 | 数据导入过滤: 发现1条姓名为空的记录
2025-07-15 16:50:54.089 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:569 | 过滤记录[行63]: 姓名字段(姓名)为空, 数据: {'应发工资': np.float64(471980.9)}
2025-07-15 16:50:54.089 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:607 | 数据过滤完成: 原始63条记录，过滤1条无效记录，有效记录62条
2025-07-15 16:50:54.104 | INFO     | src.modules.data_import.excel_importer:import_data:286 | 🔧 [修复标识] 数据导入最终完成: 62行 × 21列
2025-07-15 16:50:54.104 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:445 | 未找到工作表 A岗职工 的配置，使用智能默认处理
2025-07-15 16:50:54.104 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:726 | 为Sheet 'A岗职工' 生成智能默认配置: 2 个必需字段
2025-07-15 16:50:54.104 | INFO     | src.modules.data_import.specialized_field_mapping_generator:generate_mapping:139 | 为模板 a_grade_employees 生成了 26 个字段映射
2025-07-15 16:50:54.104 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:462 | 使用专用模板 a_grade_employees 生成字段映射: 26 个字段
2025-07-15 16:50:54.104 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:605 | 完整字段映射保存成功: salary_data_2025_07_a_grade_employees
2025-07-15 16:50:54.104 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:504 | 为表 salary_data_2025_07_a_grade_employees 生成标准化字段映射: 26 个字段
2025-07-15 16:50:54.121 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:514 | Sheet A岗职工 存在 2 个验证错误
2025-07-15 16:50:54.121 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:523 | Sheet A岗职工 数据处理完成: 62 行
2025-07-15 16:50:54.121 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:360 | Sheet 'A岗职工' 检测到模板类型: a_grade_employees
2025-07-15 16:50:54.139 | INFO     | src.modules.data_storage.dynamic_table_manager:_create_table_from_schema:692 | 成功创建表: salary_data_2025_07_a_grade_employees
2025-07-15 16:50:54.139 | INFO     | src.modules.data_storage.dynamic_table_manager:create_specialized_salary_table:360 | 专用工资数据表创建成功: salary_data_2025_07_a_grade_employees (模板: a_grade_employees)
2025-07-15 16:50:54.152 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1131 | 🔧 [修复标识] 导入字段映射加载完成: 26 个映射规则
2025-07-15 16:50:54.152 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1139 | 🔧 [修复标识] 保留未映射列: ['data_source', 'import_time']
2025-07-15 16:50:54.152 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1148 | 🔧 [修复标识] 导入列名映射成功: 23 个字段已映射
2025-07-15 16:50:54.168 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1285 | 成功向表 salary_data_2025_07_a_grade_employees 保存 62 条数据。
2025-07-15 16:50:54.168 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:230 | 多Sheet导入完成: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_07_retired_employees 保存 2 条数据。', 'table_name': 'salary_data_2025_07_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_07_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2025_07_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_07_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2025_07_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2025_07_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2025_07_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 6}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']}
2025-07-15 16:50:54.168 | INFO     | src.gui.main_dialogs:_execute_multi_sheet_import:1479 | 多Sheet导入成功: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_07_retired_employees 保存 2 条数据。', 'table_name': 'salary_data_2025_07_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_07_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2025_07_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_07_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2025_07_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2025_07_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2025_07_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 6}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工'], 'import_mode': 'multi_sheet', 'strategy': 'separate_tables', 'data_period': '2025-07', 'data_description': '2025年7月工资数据', 'file_path': 'C:/test/salary_changes/salary_changes/data/工资表/2025年5月份正式工工资（报财务)  最终版.xls', 'target_path': '工资表 > 2025年 > 07月 > 全部在职人员'}
2025-07-15 16:50:54.185 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:3881 | 开始处理导入结果: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_07_retired_employees 保存 2 条数据。', 'table_name': 'salary_data_2025_07_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_07_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2025_07_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_07_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2025_07_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2025_07_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2025_07_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 6}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工'], 'import_mode': 'multi_sheet', 'strategy': 'separate_tables', 'data_period': '2025-07', 'data_description': '2025年7月工资数据', 'file_path': 'C:/test/salary_changes/salary_changes/data/工资表/2025年5月份正式工工资（报财务)  最终版.xls', 'target_path': '工资表 > 2025年 > 07月 > 全部在职人员'}
2025-07-15 16:50:54.185 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:3892 | 导入模式: multi_sheet, 目标路径: '工资表 > 2025年 > 07月 > 全部在职人员'
2025-07-15 16:50:54.185 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:3910 | 接收到导入数据, 来源: 未知来源, 目标路径: 工资表 > 2025年 > 07月 > 全部在职人员
2025-07-15 16:50:54.986 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:3998 | 检查是否需要更新导航面板: ['工资表', '2025年', '07月', '全部在职人员']
2025-07-15 16:50:54.986 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:4002 | 检测到工资数据导入，开始刷新导航面板
2025-07-15 16:50:54.986 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:4006 | 使用强制刷新方法
2025-07-15 16:50:54.986 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1230 | 开始强制刷新工资数据导航... (尝试 1/3)
2025-07-15 16:50:54.986 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1238 | 找到工资表节点: 📊 工资表
2025-07-15 16:50:55.001 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:881 | 找到 4 个匹配类型 'salary_data' 的表
2025-07-15 16:50:55.002 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2025年，包含 1 个月份
2025-07-15 16:50:55.002 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1343 | 工资数据导航已强制刷新: 1 个年份, 1 个月份
2025-07-15 16:50:55.002 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1346 | 默认路径: 工资表 > 2025年 > 7月 > 全部在职人员
2025-07-15 16:50:55.002 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1385 | force_refresh_salary_data 执行完成
2025-07-15 16:50:55.002 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:4011 | 将在1500ms后导航到: 工资表 > 2025年 > 07月 > 全部在职人员
2025-07-15 16:50:56.502 | INFO     | src.gui.prototype.prototype_main_window:_navigate_to_imported_path:4050 | 尝试导航到新导入的路径: 工资表 > 2025年 > 07月 > 全部在职人员
2025-07-15 16:50:56.502 | INFO     | src.gui.prototype.prototype_main_window:_navigate_to_imported_path:4055 | 已成功导航到新导入的路径: 工资表 > 2025年 > 07月 > 全部在职人员
2025-07-15 16:50:57.018 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:5254 | 🔧 [表名生成] 从完整路径生成表名: ['工资表', '2025年', '07月', '全部在职人员'] -> salary_data_2025_07_active_employees
2025-07-15 16:50:57.019 | INFO     | src.gui.prototype.prototype_main_window:_refresh_current_data_display:4134 | 开始刷新当前数据显示: salary_data_2025_07_active_employees
2025-07-15 16:50:57.022 | INFO     | src.gui.prototype.prototype_main_window:_refresh_current_data_display:4141 | 分页模式刷新: 第1页，每页50条
2025-07-15 16:50:57.023 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:4900 | 使用分页模式加载 salary_data_2025_07_active_employees，第1页，每页50条
2025-07-15 16:50:57.024 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:4943 | 缓存未命中，从数据库加载: salary_data_2025_07_active_employees 第1页
2025-07-15 16:50:57.025 | INFO     | src.gui.prototype.prototype_main_window:run:128 | 开始加载表 salary_data_2025_07_active_employees 第1页数据，每页50条
2025-07-15 16:50:57.027 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:439 | 正在从表 salary_data_2025_07_active_employees 分页获取数据: 第1页, 每页50条
2025-07-15 16:50:57.030 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:462 | 成功从表 salary_data_2025_07_active_employees 获取第1页数据: 50 行，总计1396行
2025-07-15 16:50:57.034 | INFO     | src.gui.prototype.prototype_main_window:run:166 | 原始数据: 50行, 28列
2025-07-15 16:50:57.034 | INFO     | src.gui.prototype.prototype_main_window:run:173 | 开始应用字段映射
2025-07-15 16:50:57.036 | INFO     | src.gui.prototype.prototype_main_window:_apply_unified_field_processing:4454 | 🔧 [修复标识] 开始统一字段处理: salary_data_2025_07_active_employees, 原始列数: 28
2025-07-15 16:50:57.080 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:4635 | 🔧 [修复标识] 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-15 16:50:57.082 | INFO     | src.gui.prototype.prototype_main_window:_apply_unified_field_processing:4499 | 🔧 [字段处理] 统一字段处理完成并缓存: 24个字段
2025-07-15 16:50:57.084 | INFO     | src.gui.prototype.prototype_main_window:run:183 | 🔧 [修复标识] PaginationWorker - 字段映射成功: 28 -> 24列
2025-07-15 16:50:57.085 | INFO     | src.gui.prototype.prototype_main_window:run:197 | 字段映射成功: 24列
2025-07-15 16:50:57.087 | INFO     | src.gui.prototype.prototype_main_window:run:207 | 开始应用数据格式化处理
2025-07-15 16:50:57.091 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:238 | 开始格式化工资表数据 - 表类型: salary_data_2025_07_active_employees
2025-07-15 16:50:57.091 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:251 | 原始数据: 50行, 24列
2025-07-15 16:50:57.092 | INFO     | src.modules.data_storage.salary_data_formatter:_extract_table_type_from_name:335 | 🔧 [表名映射] 通过关键词 'active_employees' 匹配到表类型: active_employees
2025-07-15 16:50:57.092 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:255 | 🔧 [表名映射] 输入表名: salary_data_2025_07_active_employees, 提取的表类型: active_employees
2025-07-15 16:50:57.093 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:259 | 使用表类型配置: active_employees, 配置字段: ['id_fields', 'string_fields', 'integer_fields', 'float_fields', 'special_fields']
2025-07-15 16:50:57.095 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:263 | 🔧 [格式化调试] 开始处理ID字段: ['employee_id', 'employee_type_code', '工号', '人员类别代码']
2025-07-15 16:50:57.096 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:268 | 🔧 [格式化调试] 处理前 工号 字段样例: ['19990089.0', '20161565.0', '20191782.0']
2025-07-15 16:50:57.097 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:268 | 🔧 [格式化调试] 处理前 人员类别代码 字段样例: ['1.0', '1.0', '17.0']
2025-07-15 16:50:57.097 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_id_fields:461 | 🔧 [警告] ID字段 employee_id 不存在于数据框中
2025-07-15 16:50:57.098 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_id_fields:461 | 🔧 [警告] ID字段 employee_type_code 不存在于数据框中
2025-07-15 16:50:57.099 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:377 | 🔧 [强化调试] 处理ID字段 工号: 原始样例=['19990089.0', '20161565.0', '20191782.0'], 原始类型=object
2025-07-15 16:50:57.121 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:426 | 🔧 [强化调试] 清理后ID字段 工号: 样例=['19990089', '20161565', '20191782']
2025-07-15 16:50:57.123 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:443 | 🔧 [强化调试] 最终ID字段 工号: 样例=['19990089', '20161565', '20191782']
2025-07-15 16:50:57.124 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:454 | 🔧 [验证成功] ID字段 工号 格式化完成，无小数点问题
2025-07-15 16:50:57.124 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:377 | 🔧 [强化调试] 处理ID字段 人员类别代码: 原始样例=['1.0', '1.0', '17.0'], 原始类型=object
2025-07-15 16:50:57.126 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:426 | 🔧 [强化调试] 清理后ID字段 人员类别代码: 样例=['1', '1', '17']
2025-07-15 16:50:57.128 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:443 | 🔧 [强化调试] 最终ID字段 人员类别代码: 样例=['01', '01', '17']
2025-07-15 16:50:57.169 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:454 | 🔧 [验证成功] ID字段 人员类别代码 格式化完成，无小数点问题
2025-07-15 16:50:57.170 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:274 | 🔧 [格式化调试] 处理后 工号 字段样例: ['19990089', '20161565', '20191782']
2025-07-15 16:50:57.171 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:274 | 🔧 [格式化调试] 处理后 人员类别代码 字段样例: ['01', '01', '17']
2025-07-15 16:50:57.172 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:472 | 🔧 [强化调试] 开始格式化字符串字段: ['employee_id', 'employee_name', 'department', 'employee_type_code', 'employee_type', '工号', '姓名', '部门名称', '人员类别代码', '人员类别']
2025-07-15 16:50:57.173 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 employee_id 不存在于数据框中
2025-07-15 16:50:57.174 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 employee_name 不存在于数据框中
2025-07-15 16:50:57.175 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 department 不存在于数据框中
2025-07-15 16:50:57.175 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 employee_type_code 不存在于数据框中
2025-07-15 16:50:57.176 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 employee_type 不存在于数据框中
2025-07-15 16:50:57.182 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 工号: 原始样例=['19990089', '20161565', '20191782'], 原始类型=object
2025-07-15 16:50:57.184 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:504 | 🔧 [修复] 工号字段 工号 已特殊处理，去除小数点格式
2025-07-15 16:50:57.187 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 工号: 样例=['19990089', '20161565', '20191782'], 类型=object
2025-07-15 16:50:57.187 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 工号 格式化完成，无小数点问题
2025-07-15 16:50:57.188 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 姓名: 原始样例=['杨胜', '胡四平', '肖啸'], 原始类型=object
2025-07-15 16:50:57.191 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 姓名: 样例=['杨胜', '胡四平', '肖啸'], 类型=object
2025-07-15 16:50:57.209 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 姓名 格式化完成，无小数点问题
2025-07-15 16:50:57.210 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 部门名称: 原始样例=['自动化学院', '自动化学院', '自动化学院'], 原始类型=object
2025-07-15 16:50:57.213 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 部门名称: 样例=['自动化学院', '自动化学院', '自动化学院'], 类型=object
2025-07-15 16:50:57.214 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 部门名称 格式化完成，无小数点问题
2025-07-15 16:50:57.214 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 人员类别代码: 原始样例=['01', '01', '17'], 原始类型=object
2025-07-15 16:50:57.217 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 人员类别代码: 样例=['01', '01', '17'], 类型=object
2025-07-15 16:50:57.222 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 人员类别代码 格式化完成，无小数点问题
2025-07-15 16:50:57.224 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 人员类别: 原始样例=['教学院其它人员', '教学单位专技人员', '科研单位人员'], 原始类型=object
2025-07-15 16:50:57.256 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 人员类别: 样例=['教学院其它人员', '教学单位专技人员', '科研单位人员'], 类型=object
2025-07-15 16:50:57.295 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 人员类别 格式化完成，无小数点问题
2025-07-15 16:50:57.297 | INFO     | src.modules.data_storage.salary_data_formatter:_format_integer_fields:604 | 🔧 [强化调试] 开始格式化整型字段: []
2025-07-15 16:50:57.297 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:655 | 🔧 [强化调试] 开始格式化浮点型字段: ['position_salary_2025', 'grade_salary_2025', 'allowance', 'balance_allowance', 'basic_performance_2025', 'health_fee', 'transport_allowance', 'property_allowance', 'communication_allowance', 'performance_bonus_2025', 'provident_fund_2025', 'housing_allowance', 'car_allowance', 'supplement', 'advance', 'total_salary', 'pension_insurance', '2025年岗位工资', '2025年薪级工资', '津贴', '结余津贴', '2025年基础性绩效', '卫生费', '交通补贴', '物业补贴', '通讯补贴', '2025年奖励性绩效预发', '2025公积金', '住房补贴', '车补', '补发', '借支', '应发工资', '代扣代存养老保险']
2025-07-15 16:50:57.299 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 position_salary_2025 不存在于数据框中
2025-07-15 16:50:57.304 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 grade_salary_2025 不存在于数据框中
2025-07-15 16:50:57.306 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 allowance 不存在于数据框中
2025-07-15 16:50:57.307 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 balance_allowance 不存在于数据框中
2025-07-15 16:50:57.308 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 basic_performance_2025 不存在于数据框中
2025-07-15 16:50:57.309 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 health_fee 不存在于数据框中
2025-07-15 16:50:57.310 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 transport_allowance 不存在于数据框中
2025-07-15 16:50:57.312 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 property_allowance 不存在于数据框中
2025-07-15 16:50:57.319 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 communication_allowance 不存在于数据框中
2025-07-15 16:50:57.320 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 performance_bonus_2025 不存在于数据框中
2025-07-15 16:50:57.321 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 provident_fund_2025 不存在于数据框中
2025-07-15 16:50:57.322 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 housing_allowance 不存在于数据框中
2025-07-15 16:50:57.323 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 car_allowance 不存在于数据框中
2025-07-15 16:50:57.324 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 supplement 不存在于数据框中
2025-07-15 16:50:57.325 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 advance 不存在于数据框中
2025-07-15 16:50:57.326 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 total_salary 不存在于数据框中
2025-07-15 16:50:57.333 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 pension_insurance 不存在于数据框中
2025-07-15 16:50:57.334 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025年岗位工资: 原始样例=[2880.0, 3030.0, 2185.0], 原始类型=float64
2025-07-15 16:50:57.336 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025年岗位工资: 样例=[2880.0, 3030.0, 2185.0], 类型=float64
2025-07-15 16:50:57.336 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025年岗位工资 格式化完成，保留两位小数
2025-07-15 16:50:57.337 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025年薪级工资: 原始样例=[2375.0, 1696.0, 1427.0], 原始类型=float64
2025-07-15 16:50:57.339 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025年薪级工资: 样例=[2375.0, 1696.0, 1427.0], 类型=float64
2025-07-15 16:50:57.339 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025年薪级工资 格式化完成，保留两位小数
2025-07-15 16:50:57.340 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 津贴: 原始样例=[102.0, 0.0, 0.0], 原始类型=float64
2025-07-15 16:50:57.341 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 津贴: 样例=[102.0, 0.0, 0.0], 类型=float64
2025-07-15 16:50:57.350 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 津贴 格式化完成，保留两位小数
2025-07-15 16:50:57.351 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 结余津贴: 原始样例=[56.0, 56.0, 56.0], 原始类型=float64
2025-07-15 16:50:57.353 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 结余津贴: 样例=[56.0, 56.0, 56.0], 类型=float64
2025-07-15 16:50:57.354 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 结余津贴 格式化完成，保留两位小数
2025-07-15 16:50:57.355 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025年基础性绩效: 原始样例=[3594.0, 3466.0, 2978.0], 原始类型=float64
2025-07-15 16:50:57.356 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025年基础性绩效: 样例=[3594.0, 3466.0, 2978.0], 类型=float64
2025-07-15 16:50:57.357 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025年基础性绩效 格式化完成，保留两位小数
2025-07-15 16:50:57.358 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 卫生费: 原始样例=[0.0, 0.0, 0.0], 原始类型=float64
2025-07-15 16:50:57.360 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 卫生费: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-15 16:50:57.365 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 卫生费 格式化完成，保留两位小数
2025-07-15 16:50:57.366 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 交通补贴: 原始样例=[220.0, 220.0, 220.0], 原始类型=float64
2025-07-15 16:50:57.368 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 交通补贴: 样例=[220.0, 220.0, 220.0], 类型=float64
2025-07-15 16:50:57.369 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 交通补贴 格式化完成，保留两位小数
2025-07-15 16:50:57.370 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 物业补贴: 原始样例=[240.0, 240.0, 200.0], 原始类型=float64
2025-07-15 16:50:57.372 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 物业补贴: 样例=[240.0, 240.0, 200.0], 类型=float64
2025-07-15 16:50:57.372 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 物业补贴 格式化完成，保留两位小数
2025-07-15 16:50:57.382 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 通讯补贴: 原始样例=[50.0, nan, nan], 原始类型=float64
2025-07-15 16:50:57.384 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 通讯补贴: 样例=[50.0, 0.0, 0.0], 类型=float64
2025-07-15 16:50:57.384 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 通讯补贴 格式化完成，保留两位小数
2025-07-15 16:50:57.386 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025年奖励性绩效预发: 原始样例=[2500.0, 1000.0, 1000.0], 原始类型=float64
2025-07-15 16:50:57.388 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025年奖励性绩效预发: 样例=[2500.0, 1000.0, 1000.0], 类型=float64
2025-07-15 16:50:57.388 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025年奖励性绩效预发 格式化完成，保留两位小数
2025-07-15 16:50:57.389 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025公积金: 原始样例=[2097.0, 1860.0, 1984.0], 原始类型=float64
2025-07-15 16:50:57.397 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025公积金: 样例=[2097.0, 1860.0, 1984.0], 类型=float64
2025-07-15 16:50:57.398 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025公积金 格式化完成，保留两位小数
2025-07-15 16:50:57.399 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 住房补贴: 原始样例=[271.9745083294993, 189.0, 174.16354166666667], 原始类型=float64
2025-07-15 16:50:57.401 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 住房补贴: 样例=[271.97, 189.0, 174.16], 类型=float64
2025-07-15 16:50:57.402 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 住房补贴 格式化完成，保留两位小数
2025-07-15 16:50:57.403 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 车补: 原始样例=[None, None, None], 原始类型=object
2025-07-15 16:50:57.405 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 车补: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-15 16:50:57.477 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 车补 格式化完成，保留两位小数
2025-07-15 16:50:57.479 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 补发: 原始样例=[None, None, None], 原始类型=object
2025-07-15 16:50:57.480 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 补发: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-15 16:50:57.481 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 补发 格式化完成，保留两位小数
2025-07-15 16:50:57.482 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 借支: 原始样例=[nan, nan, 2000.0], 原始类型=float64
2025-07-15 16:50:57.483 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 借支: 样例=[0.0, 0.0, 2000.0], 类型=float64
2025-07-15 16:50:57.484 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 借支 格式化完成，保留两位小数
2025-07-15 16:50:57.484 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 应发工资: 原始样例=[12288.9745083295, 9897.0, 6240.163541666667], 原始类型=float64
2025-07-15 16:50:57.486 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 应发工资: 样例=[12288.97, 9897.0, 6240.16], 类型=float64
2025-07-15 16:50:57.492 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 应发工资 格式化完成，保留两位小数
2025-07-15 16:50:57.493 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 代扣代存养老保险: 原始样例=[1525.8000000000002, 1140.53, 1113.75], 原始类型=float64
2025-07-15 16:50:57.494 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 代扣代存养老保险: 样例=[1525.8, 1140.53, 1113.75], 类型=float64
2025-07-15 16:50:57.495 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 代扣代存养老保险 格式化完成，保留两位小数
2025-07-15 16:50:57.496 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:711 | 🔧 [特殊字段] 开始处理表类型 active_employees 的月份、年份字段
2025-07-15 16:50:57.497 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:866 | 🔧 [特殊字段] 月份、年份字段处理完成
2025-07-15 16:50:57.501 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:291 | 数据格式化完成: 50行, 24列
2025-07-15 16:50:57.506 | INFO     | src.gui.prototype.prototype_main_window:run:215 | 数据格式化成功: 50行, 24列
2025-07-15 16:50:57.507 | INFO     | src.gui.prototype.prototype_main_window:run:239 | 最终数据: 50行, 24列, 总记录数: 1396
2025-07-15 16:50:57.508 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:4973 | 分页数据加载成功（数据库）: 50条数据，第1页，总计1396条
2025-07-15 16:50:57.513 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:439 | 正在从表 salary_data_2025_07_active_employees 分页获取数据: 第2页, 每页50条
2025-07-15 16:50:57.515 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:5006 | 🔧 [异步分页] 开始应用数据格式化处理
2025-07-15 16:50:57.518 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:238 | 开始格式化工资表数据 - 表类型: salary_data_2025_07_active_employees
2025-07-15 16:50:57.520 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:251 | 原始数据: 50行, 24列
2025-07-15 16:50:57.521 | INFO     | src.modules.data_storage.salary_data_formatter:_extract_table_type_from_name:335 | 🔧 [表名映射] 通过关键词 'active_employees' 匹配到表类型: active_employees
2025-07-15 16:50:57.523 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:462 | 成功从表 salary_data_2025_07_active_employees 获取第2页数据: 50 行，总计1396行
2025-07-15 16:50:57.523 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:255 | 🔧 [表名映射] 输入表名: salary_data_2025_07_active_employees, 提取的表类型: active_employees
2025-07-15 16:50:57.532 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:259 | 使用表类型配置: active_employees, 配置字段: ['id_fields', 'string_fields', 'integer_fields', 'float_fields', 'special_fields']
2025-07-15 16:50:57.535 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:263 | 🔧 [格式化调试] 开始处理ID字段: ['employee_id', 'employee_type_code', '工号', '人员类别代码']
2025-07-15 16:50:57.536 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:268 | 🔧 [格式化调试] 处理前 工号 字段样例: ['19990089', '20161565', '20191782']
2025-07-15 16:50:57.537 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:268 | 🔧 [格式化调试] 处理前 人员类别代码 字段样例: ['01', '01', '17']
2025-07-15 16:50:57.537 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_id_fields:461 | 🔧 [警告] ID字段 employee_id 不存在于数据框中
2025-07-15 16:50:57.538 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_id_fields:461 | 🔧 [警告] ID字段 employee_type_code 不存在于数据框中
2025-07-15 16:50:57.539 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:377 | 🔧 [强化调试] 处理ID字段 工号: 原始样例=['19990089', '20161565', '20191782'], 原始类型=object
2025-07-15 16:50:57.540 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:426 | 🔧 [强化调试] 清理后ID字段 工号: 样例=['19990089', '20161565', '20191782']
2025-07-15 16:50:57.541 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:443 | 🔧 [强化调试] 最终ID字段 工号: 样例=['19990089', '20161565', '20191782']
2025-07-15 16:50:57.542 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:454 | 🔧 [验证成功] ID字段 工号 格式化完成，无小数点问题
2025-07-15 16:50:57.542 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:377 | 🔧 [强化调试] 处理ID字段 人员类别代码: 原始样例=['01', '01', '17'], 原始类型=object
2025-07-15 16:50:57.544 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:426 | 🔧 [强化调试] 清理后ID字段 人员类别代码: 样例=['01', '01', '17']
2025-07-15 16:50:57.551 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:443 | 🔧 [强化调试] 最终ID字段 人员类别代码: 样例=['01', '01', '17']
2025-07-15 16:50:57.552 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:454 | 🔧 [验证成功] ID字段 人员类别代码 格式化完成，无小数点问题
2025-07-15 16:50:57.553 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:274 | 🔧 [格式化调试] 处理后 工号 字段样例: ['19990089', '20161565', '20191782']
2025-07-15 16:50:57.553 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:274 | 🔧 [格式化调试] 处理后 人员类别代码 字段样例: ['01', '01', '17']
2025-07-15 16:50:57.554 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:472 | 🔧 [强化调试] 开始格式化字符串字段: ['employee_id', 'employee_name', 'department', 'employee_type_code', 'employee_type', '工号', '姓名', '部门名称', '人员类别代码', '人员类别']
2025-07-15 16:50:57.555 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 employee_id 不存在于数据框中
2025-07-15 16:50:57.557 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 employee_name 不存在于数据框中
2025-07-15 16:50:57.557 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 department 不存在于数据框中
2025-07-15 16:50:57.558 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 employee_type_code 不存在于数据框中
2025-07-15 16:50:57.562 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 employee_type 不存在于数据框中
2025-07-15 16:50:57.568 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 工号: 原始样例=['19990089', '20161565', '20191782'], 原始类型=object
2025-07-15 16:50:57.570 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:504 | 🔧 [修复] 工号字段 工号 已特殊处理，去除小数点格式
2025-07-15 16:50:57.571 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 工号: 样例=['19990089', '20161565', '20191782'], 类型=object
2025-07-15 16:50:57.572 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 工号 格式化完成，无小数点问题
2025-07-15 16:50:57.573 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 姓名: 原始样例=['杨胜', '胡四平', '肖啸'], 原始类型=object
2025-07-15 16:50:57.576 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 姓名: 样例=['杨胜', '胡四平', '肖啸'], 类型=object
2025-07-15 16:50:57.580 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 姓名 格式化完成，无小数点问题
2025-07-15 16:50:57.582 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 部门名称: 原始样例=['自动化学院', '自动化学院', '自动化学院'], 原始类型=object
2025-07-15 16:50:57.584 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 部门名称: 样例=['自动化学院', '自动化学院', '自动化学院'], 类型=object
2025-07-15 16:50:57.584 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 部门名称 格式化完成，无小数点问题
2025-07-15 16:50:57.585 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 人员类别代码: 原始样例=['01', '01', '17'], 原始类型=object
2025-07-15 16:50:57.588 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 人员类别代码: 样例=['01', '01', '17'], 类型=object
2025-07-15 16:50:57.588 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 人员类别代码 格式化完成，无小数点问题
2025-07-15 16:50:57.590 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 人员类别: 原始样例=['教学院其它人员', '教学单位专技人员', '科研单位人员'], 原始类型=object
2025-07-15 16:50:57.598 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 人员类别: 样例=['教学院其它人员', '教学单位专技人员', '科研单位人员'], 类型=object
2025-07-15 16:50:57.598 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 人员类别 格式化完成，无小数点问题
2025-07-15 16:50:57.599 | INFO     | src.modules.data_storage.salary_data_formatter:_format_integer_fields:604 | 🔧 [强化调试] 开始格式化整型字段: []
2025-07-15 16:50:57.601 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:655 | 🔧 [强化调试] 开始格式化浮点型字段: ['position_salary_2025', 'grade_salary_2025', 'allowance', 'balance_allowance', 'basic_performance_2025', 'health_fee', 'transport_allowance', 'property_allowance', 'communication_allowance', 'performance_bonus_2025', 'provident_fund_2025', 'housing_allowance', 'car_allowance', 'supplement', 'advance', 'total_salary', 'pension_insurance', '2025年岗位工资', '2025年薪级工资', '津贴', '结余津贴', '2025年基础性绩效', '卫生费', '交通补贴', '物业补贴', '通讯补贴', '2025年奖励性绩效预发', '2025公积金', '住房补贴', '车补', '补发', '借支', '应发工资', '代扣代存养老保险']
2025-07-15 16:50:57.602 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 position_salary_2025 不存在于数据框中
2025-07-15 16:50:57.603 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 grade_salary_2025 不存在于数据框中
2025-07-15 16:50:57.604 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 allowance 不存在于数据框中
2025-07-15 16:50:57.605 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 balance_allowance 不存在于数据框中
2025-07-15 16:50:57.612 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 basic_performance_2025 不存在于数据框中
2025-07-15 16:50:57.613 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 health_fee 不存在于数据框中
2025-07-15 16:50:57.614 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 transport_allowance 不存在于数据框中
2025-07-15 16:50:57.615 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 property_allowance 不存在于数据框中
2025-07-15 16:50:57.615 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 communication_allowance 不存在于数据框中
2025-07-15 16:50:57.616 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 performance_bonus_2025 不存在于数据框中
2025-07-15 16:50:57.617 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 provident_fund_2025 不存在于数据框中
2025-07-15 16:50:57.618 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 housing_allowance 不存在于数据框中
2025-07-15 16:50:57.621 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 car_allowance 不存在于数据框中
2025-07-15 16:50:57.628 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 supplement 不存在于数据框中
2025-07-15 16:50:57.628 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 advance 不存在于数据框中
2025-07-15 16:50:57.629 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 total_salary 不存在于数据框中
2025-07-15 16:50:57.630 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 pension_insurance 不存在于数据框中
2025-07-15 16:50:57.632 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025年岗位工资: 原始样例=[2880.0, 3030.0, 2185.0], 原始类型=float64
2025-07-15 16:50:57.633 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025年岗位工资: 样例=[2880.0, 3030.0, 2185.0], 类型=float64
2025-07-15 16:50:57.634 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025年岗位工资 格式化完成，保留两位小数
2025-07-15 16:50:57.635 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025年薪级工资: 原始样例=[2375.0, 1696.0, 1427.0], 原始类型=float64
2025-07-15 16:50:57.636 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025年薪级工资: 样例=[2375.0, 1696.0, 1427.0], 类型=float64
2025-07-15 16:50:57.641 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025年薪级工资 格式化完成，保留两位小数
2025-07-15 16:50:57.642 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 津贴: 原始样例=[102.0, 0.0, 0.0], 原始类型=float64
2025-07-15 16:50:57.644 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 津贴: 样例=[102.0, 0.0, 0.0], 类型=float64
2025-07-15 16:50:57.644 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 津贴 格式化完成，保留两位小数
2025-07-15 16:50:57.645 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 结余津贴: 原始样例=[56.0, 56.0, 56.0], 原始类型=float64
2025-07-15 16:50:57.647 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 结余津贴: 样例=[56.0, 56.0, 56.0], 类型=float64
2025-07-15 16:50:57.648 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 结余津贴 格式化完成，保留两位小数
2025-07-15 16:50:57.649 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025年基础性绩效: 原始样例=[3594.0, 3466.0, 2978.0], 原始类型=float64
2025-07-15 16:50:57.658 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025年基础性绩效: 样例=[3594.0, 3466.0, 2978.0], 类型=float64
2025-07-15 16:50:57.659 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025年基础性绩效 格式化完成，保留两位小数
2025-07-15 16:50:57.660 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 卫生费: 原始样例=[0.0, 0.0, 0.0], 原始类型=float64
2025-07-15 16:50:57.662 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 卫生费: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-15 16:50:57.662 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 卫生费 格式化完成，保留两位小数
2025-07-15 16:50:57.663 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 交通补贴: 原始样例=[220.0, 220.0, 220.0], 原始类型=float64
2025-07-15 16:50:57.664 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 交通补贴: 样例=[220.0, 220.0, 220.0], 类型=float64
2025-07-15 16:50:57.665 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 交通补贴 格式化完成，保留两位小数
2025-07-15 16:50:57.666 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 物业补贴: 原始样例=[240.0, 240.0, 200.0], 原始类型=float64
2025-07-15 16:50:57.668 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 物业补贴: 样例=[240.0, 240.0, 200.0], 类型=float64
2025-07-15 16:50:57.674 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 物业补贴 格式化完成，保留两位小数
2025-07-15 16:50:57.675 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 通讯补贴: 原始样例=[50.0, 0.0, 0.0], 原始类型=float64
2025-07-15 16:50:57.676 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 通讯补贴: 样例=[50.0, 0.0, 0.0], 类型=float64
2025-07-15 16:50:57.676 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 通讯补贴 格式化完成，保留两位小数
2025-07-15 16:50:57.676 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025年奖励性绩效预发: 原始样例=[2500.0, 1000.0, 1000.0], 原始类型=float64
2025-07-15 16:50:57.678 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025年奖励性绩效预发: 样例=[2500.0, 1000.0, 1000.0], 类型=float64
2025-07-15 16:50:57.678 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025年奖励性绩效预发 格式化完成，保留两位小数
2025-07-15 16:50:57.679 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025公积金: 原始样例=[2097.0, 1860.0, 1984.0], 原始类型=float64
2025-07-15 16:50:57.683 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025公积金: 样例=[2097.0, 1860.0, 1984.0], 类型=float64
2025-07-15 16:50:57.690 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025公积金 格式化完成，保留两位小数
2025-07-15 16:50:57.691 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 住房补贴: 原始样例=[271.97, 189.0, 174.16], 原始类型=float64
2025-07-15 16:50:57.693 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 住房补贴: 样例=[271.97, 189.0, 174.16], 类型=float64
2025-07-15 16:50:57.693 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 住房补贴 格式化完成，保留两位小数
2025-07-15 16:50:57.694 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 车补: 原始样例=[0.0, 0.0, 0.0], 原始类型=float64
2025-07-15 16:50:57.695 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 车补: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-15 16:50:57.696 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 车补 格式化完成，保留两位小数
2025-07-15 16:50:57.697 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 补发: 原始样例=[0.0, 0.0, 0.0], 原始类型=float64
2025-07-15 16:50:57.699 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 补发: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-15 16:50:57.704 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 补发 格式化完成，保留两位小数
2025-07-15 16:50:57.705 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 借支: 原始样例=[0.0, 0.0, 2000.0], 原始类型=float64
2025-07-15 16:50:57.706 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 借支: 样例=[0.0, 0.0, 2000.0], 类型=float64
2025-07-15 16:50:57.707 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 借支 格式化完成，保留两位小数
2025-07-15 16:50:57.708 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 应发工资: 原始样例=[12288.97, 9897.0, 6240.16], 原始类型=float64
2025-07-15 16:50:57.709 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 应发工资: 样例=[12288.97, 9897.0, 6240.16], 类型=float64
2025-07-15 16:50:57.710 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 应发工资 格式化完成，保留两位小数
2025-07-15 16:50:57.711 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 代扣代存养老保险: 原始样例=[1525.8, 1140.53, 1113.75], 原始类型=float64
2025-07-15 16:50:57.714 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 代扣代存养老保险: 样例=[1525.8, 1140.53, 1113.75], 类型=float64
2025-07-15 16:50:57.720 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 代扣代存养老保险 格式化完成，保留两位小数
2025-07-15 16:50:57.722 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:711 | 🔧 [特殊字段] 开始处理表类型 active_employees 的月份、年份字段
2025-07-15 16:50:57.722 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:866 | 🔧 [特殊字段] 月份、年份字段处理完成
2025-07-15 16:50:57.728 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:291 | 数据格式化完成: 50行, 24列
2025-07-15 16:50:57.728 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:5008 | 🔧 [异步分页] 数据格式化成功: 50行, 24列
2025-07-15 16:50:57.733 | INFO     | src.gui.prototype.prototype_main_window:set_data:635 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-07-15 16:50:57.839 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:133 | 使用已存在的配置文件: state/data/field_mappings.json
2025-07-15 16:50:57.841 | INFO     | src.modules.data_import.config_sync_manager:__init__:70 | 配置同步管理器初始化完成
2025-07-15 16:50:57.843 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:1146 | 🔧 [修复] 应用字段映射: 24个字段
2025-07-15 16:50:57.950 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:133 | 使用已存在的配置文件: state/data/field_mappings.json
2025-07-15 16:50:57.950 | INFO     | src.modules.data_import.config_sync_manager:__init__:70 | 配置同步管理器初始化完成
2025-07-15 16:50:57.950 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:1220 | 🔧 [修复] 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-15 16:50:57.967 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3560 | 最大可见行数已更新: 1000 -> 50
2025-07-15 16:50:57.967 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:3613 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-15 16:50:57.967 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第0行数据工号: 19990089
2025-07-15 16:50:57.967 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第1行数据工号: 20161565
2025-07-15 16:50:57.967 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第2行数据工号: 20191782
2025-07-15 16:50:57.967 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第3行数据工号: 20151515
2025-07-15 16:50:57.967 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第4行数据工号: 20181640
2025-07-15 16:50:57.967 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:457 | 🔧 [排序修复] 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-15 16:50:57.967 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:424 | 表格数据已设置: 50 行, 24 列
2025-07-15 16:50:57.995 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2174 | 🔧 [性能分析] 表格数据设置完成: 50 行, 最大可见行数: 50, 总耗时: 44.16ms
2025-07-15 16:50:57.995 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 初始化和状态: 16.22ms (36.7%)
2025-07-15 16:50:57.995 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 字段映射: 0.00ms (0.0%)
2025-07-15 16:50:57.995 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 数据模型更新: 0.00ms (0.0%)
2025-07-15 16:50:57.995 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 表头设置: 0.00ms (0.0%)
2025-07-15 16:50:57.995 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 设置行数: 0.00ms (0.0%)
2025-07-15 16:50:57.995 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 填充可见数据: 27.94ms (63.3%)
2025-07-15 16:50:57.995 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 调整列宽: 0.00ms (0.0%)
2025-07-15 16:50:58.010 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 设置行号: 0.00ms (0.0%)
2025-07-15 16:50:58.010 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2182 | 🔧 [性能分析] 最耗时步骤: 填充可见数据 (27.94ms)
2025-07-15 16:50:58.010 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 50
2025-07-15 16:50:58.010 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-15 16:51:13.151 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_changed:5401 | 排序变化: 1 列
2025-07-15 16:51:13.151 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_changed:5413 | 🔧 [调试] 数据重载状态正常，继续处理排序
2025-07-15 16:51:13.151 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_changed:5417 | 🔧 [调试] 开始更新排序指示器
2025-07-15 16:51:13.151 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_sort_indicators:5797 | 🔧 [排序指示器修复] 设置排序指示器: 列6, 顺序ascending
2025-07-15 16:51:13.151 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_changed:5419 | 🔧 [调试] 排序指示器更新完成
2025-07-15 16:51:13.151 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_changed:5425 | 🔧 [调试] 准备调用_save_and_apply_sort_state
2025-07-15 16:51:13.151 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5441 | 🔧 [调试] 开始保存排序状态: 1 列
2025-07-15 16:51:13.151 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5457 | 🔧 [调试] 已获取主窗口: PrototypeMainWindow
2025-07-15 16:51:13.151 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5463 | 🔧 [调试] 表名: , 表类型: 
2025-07-15 16:51:13.151 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5466 | 🔧 [调试] 无法获取当前表名，跳过全表排序
2025-07-15 16:51:13.151 | INFO     | src.gui.multi_column_sort_manager:add_sort_column:274 | 添加排序列: 6(grade_salary_2025) -> ascending, 当前排序列数: 1
2025-07-15 16:51:13.151 | INFO     | src.gui.multi_column_sort_manager:_on_sort_indicator_changed:211 | 排序指示器变化: 列6(grade_salary_2025) -> ascending
2025-07-15 16:51:13.151 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_header_sort_changed:5890 | 🔧 [表格排序] 表头排序变化: 列6, 顺序ASC
2025-07-15 16:51:13.151 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_changed:5401 | 排序变化: 1 列
2025-07-15 16:51:13.151 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_changed:5413 | 🔧 [调试] 数据重载状态正常，继续处理排序
2025-07-15 16:51:13.167 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_changed:5417 | 🔧 [调试] 开始更新排序指示器
2025-07-15 16:51:13.167 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_sort_indicators:5797 | 🔧 [排序指示器修复] 设置排序指示器: 列6, 顺序ascending
2025-07-15 16:51:13.167 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_changed:5419 | 🔧 [调试] 排序指示器更新完成
2025-07-15 16:51:13.167 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_changed:5425 | 🔧 [调试] 准备调用_save_and_apply_sort_state
2025-07-15 16:51:13.167 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5441 | 🔧 [调试] 开始保存排序状态: 1 列
2025-07-15 16:51:13.167 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5457 | 🔧 [调试] 已获取主窗口: PrototypeMainWindow
2025-07-15 16:51:13.167 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5463 | 🔧 [调试] 表名: , 表类型: 
2025-07-15 16:51:13.167 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5466 | 🔧 [调试] 无法获取当前表名，跳过全表排序
2025-07-15 16:51:13.167 | INFO     | src.gui.multi_column_sort_manager:add_sort_column:274 | 添加排序列: 6(grade_salary_2025) -> ascending, 当前排序列数: 1
2025-07-15 16:51:13.167 | INFO     | src.gui.multi_column_sort_manager:_on_sort_indicator_changed:211 | 排序指示器变化: 列6(grade_salary_2025) -> ascending
2025-07-15 16:51:13.167 | ERROR    | src.gui.prototype.prototype_main_window:_on_sort_indicator_changed:3149 | 🔧 [全局排序] 排序处理失败: 'PrototypeMainWindow' object has no attribute 'current_table_name'
2025-07-15 16:51:25.323 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_changed:5401 | 排序变化: 1 列
2025-07-15 16:51:25.323 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_changed:5413 | 🔧 [调试] 数据重载状态正常，继续处理排序
2025-07-15 16:51:25.339 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_changed:5417 | 🔧 [调试] 开始更新排序指示器
2025-07-15 16:51:25.339 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_sort_indicators:5797 | 🔧 [排序指示器修复] 设置排序指示器: 列6, 顺序descending
2025-07-15 16:51:25.339 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_changed:5419 | 🔧 [调试] 排序指示器更新完成
2025-07-15 16:51:25.339 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_changed:5425 | 🔧 [调试] 准备调用_save_and_apply_sort_state
2025-07-15 16:51:25.339 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5441 | 🔧 [调试] 开始保存排序状态: 1 列
2025-07-15 16:51:25.339 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5457 | 🔧 [调试] 已获取主窗口: PrototypeMainWindow
2025-07-15 16:51:25.339 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5463 | 🔧 [调试] 表名: , 表类型: 
2025-07-15 16:51:25.339 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5466 | 🔧 [调试] 无法获取当前表名，跳过全表排序
2025-07-15 16:51:25.339 | INFO     | src.gui.multi_column_sort_manager:add_sort_column:274 | 添加排序列: 6(grade_salary_2025) -> descending, 当前排序列数: 1
2025-07-15 16:51:25.339 | INFO     | src.gui.multi_column_sort_manager:_on_sort_indicator_changed:211 | 排序指示器变化: 列6(grade_salary_2025) -> descending
2025-07-15 16:51:25.339 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_header_sort_changed:5890 | 🔧 [表格排序] 表头排序变化: 列6, 顺序DESC
2025-07-15 16:51:25.339 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_changed:5401 | 排序变化: 1 列
2025-07-15 16:51:25.339 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_changed:5413 | 🔧 [调试] 数据重载状态正常，继续处理排序
2025-07-15 16:51:25.339 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_changed:5417 | 🔧 [调试] 开始更新排序指示器
2025-07-15 16:51:25.339 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_sort_indicators:5797 | 🔧 [排序指示器修复] 设置排序指示器: 列6, 顺序descending
2025-07-15 16:51:25.339 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_changed:5419 | 🔧 [调试] 排序指示器更新完成
2025-07-15 16:51:25.339 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_changed:5425 | 🔧 [调试] 准备调用_save_and_apply_sort_state
2025-07-15 16:51:25.339 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5441 | 🔧 [调试] 开始保存排序状态: 1 列
2025-07-15 16:51:25.355 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5457 | 🔧 [调试] 已获取主窗口: PrototypeMainWindow
2025-07-15 16:51:25.355 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5463 | 🔧 [调试] 表名: , 表类型: 
2025-07-15 16:51:25.355 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5466 | 🔧 [调试] 无法获取当前表名，跳过全表排序
2025-07-15 16:51:25.355 | INFO     | src.gui.multi_column_sort_manager:add_sort_column:274 | 添加排序列: 6(grade_salary_2025) -> descending, 当前排序列数: 1
2025-07-15 16:51:25.355 | INFO     | src.gui.multi_column_sort_manager:_on_sort_indicator_changed:211 | 排序指示器变化: 列6(grade_salary_2025) -> descending
2025-07-15 16:51:25.355 | ERROR    | src.gui.prototype.prototype_main_window:_on_sort_indicator_changed:3149 | 🔧 [全局排序] 排序处理失败: 'PrototypeMainWindow' object has no attribute 'current_table_name'
2025-07-15 16:51:50.589 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:964 | 🔧 [紧急修复] 开始分页处理: 第2页, 表名: salary_data_2025_07_active_employees
2025-07-15 16:51:50.589 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:439 | 正在从表 salary_data_2025_07_active_employees 分页获取数据: 第2页, 每页50条
2025-07-15 16:51:50.589 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:462 | 成功从表 salary_data_2025_07_active_employees 获取第2页数据: 50 行，总计1396行
2025-07-15 16:51:50.708 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:133 | 使用已存在的配置文件: state/data/field_mappings.json
2025-07-15 16:51:50.708 | INFO     | src.modules.data_import.config_sync_manager:__init__:70 | 配置同步管理器初始化完成
2025-07-15 16:51:50.708 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:1151 | 🔧 [修复] 没有找到匹配的字段映射
2025-07-15 16:51:50.708 | INFO     | src.gui.prototype.prototype_main_window:_apply_system_field_filtering:1183 | 🔧 [修复] 过滤了 4 个系统字段
2025-07-15 16:51:50.820 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:133 | 使用已存在的配置文件: state/data/field_mappings.json
2025-07-15 16:51:50.820 | INFO     | src.modules.data_import.config_sync_manager:__init__:70 | 配置同步管理器初始化完成
2025-07-15 16:51:50.820 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:1220 | 🔧 [修复] 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-15 16:51:50.820 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:238 | 开始格式化工资表数据 - 表类型: salary_data_2025_07_active_employees
2025-07-15 16:51:50.820 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:251 | 原始数据: 50行, 24列
2025-07-15 16:51:50.820 | INFO     | src.modules.data_storage.salary_data_formatter:_extract_table_type_from_name:335 | 🔧 [表名映射] 通过关键词 'active_employees' 匹配到表类型: active_employees
2025-07-15 16:51:50.820 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:255 | 🔧 [表名映射] 输入表名: salary_data_2025_07_active_employees, 提取的表类型: active_employees
2025-07-15 16:51:50.820 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:259 | 使用表类型配置: active_employees, 配置字段: ['id_fields', 'string_fields', 'integer_fields', 'float_fields', 'special_fields']
2025-07-15 16:51:50.820 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:263 | 🔧 [格式化调试] 开始处理ID字段: ['employee_id', 'employee_type_code', '工号', '人员类别代码']
2025-07-15 16:51:50.820 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:268 | 🔧 [格式化调试] 处理前 employee_id 字段样例: ['20171604.0', '20181638.0', '19930191.0']
2025-07-15 16:51:50.820 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:268 | 🔧 [格式化调试] 处理前 employee_type_code 字段样例: ['1.0', '1.0', '1.0']
2025-07-15 16:51:50.820 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:377 | 🔧 [强化调试] 处理ID字段 employee_id: 原始样例=['20171604.0', '20181638.0', '19930191.0'], 原始类型=object
2025-07-15 16:51:50.820 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:426 | 🔧 [强化调试] 清理后ID字段 employee_id: 样例=['20171604', '20181638', '19930191']
2025-07-15 16:51:50.820 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:443 | 🔧 [强化调试] 最终ID字段 employee_id: 样例=['20171604', '20181638', '19930191']
2025-07-15 16:51:50.820 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:454 | 🔧 [验证成功] ID字段 employee_id 格式化完成，无小数点问题
2025-07-15 16:51:50.835 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:377 | 🔧 [强化调试] 处理ID字段 employee_type_code: 原始样例=['1.0', '1.0', '1.0'], 原始类型=object
2025-07-15 16:51:50.835 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:426 | 🔧 [强化调试] 清理后ID字段 employee_type_code: 样例=['1', '1', '1']
2025-07-15 16:51:50.835 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:443 | 🔧 [强化调试] 最终ID字段 employee_type_code: 样例=['01', '01', '01']
2025-07-15 16:51:50.835 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:454 | 🔧 [验证成功] ID字段 employee_type_code 格式化完成，无小数点问题
2025-07-15 16:51:50.835 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_id_fields:461 | 🔧 [警告] ID字段 工号 不存在于数据框中
2025-07-15 16:51:50.835 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_id_fields:461 | 🔧 [警告] ID字段 人员类别代码 不存在于数据框中
2025-07-15 16:51:50.835 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:274 | 🔧 [格式化调试] 处理后 employee_id 字段样例: ['20171604', '20181638', '19930191']
2025-07-15 16:51:50.835 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:274 | 🔧 [格式化调试] 处理后 employee_type_code 字段样例: ['01', '01', '01']
2025-07-15 16:51:50.835 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:472 | 🔧 [强化调试] 开始格式化字符串字段: ['employee_id', 'employee_name', 'department', 'employee_type_code', 'employee_type', '工号', '姓名', '部门名称', '人员类别代码', '人员类别']
2025-07-15 16:51:50.835 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 employee_id: 原始样例=['20171604', '20181638', '19930191'], 原始类型=object
2025-07-15 16:51:50.835 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:504 | 🔧 [修复] 工号字段 employee_id 已特殊处理，去除小数点格式
2025-07-15 16:51:50.835 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 employee_id: 样例=['20171604', '20181638', '19930191'], 类型=object
2025-07-15 16:51:50.835 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 employee_id 格式化完成，无小数点问题
2025-07-15 16:51:50.835 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 employee_name: 原始样例=['周荔', '申子宇', '周浩'], 原始类型=object
2025-07-15 16:51:50.835 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 employee_name: 样例=['周荔', '申子宇', '周浩'], 类型=object
2025-07-15 16:51:50.857 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 employee_name 格式化完成，无小数点问题
2025-07-15 16:51:50.857 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 department: 原始样例=['资源环境科学与工程学院', '资源环境科学与工程学院', '资源环境科学与工程学院'], 原始类型=object
2025-07-15 16:51:50.867 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 department: 样例=['资源环境科学与工程学院', '资源环境科学与工程学院', '资源环境科学与工程学院'], 类型=object
2025-07-15 16:51:50.867 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 department 格式化完成，无小数点问题
2025-07-15 16:51:50.867 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 employee_type_code: 原始样例=['01', '01', '01'], 原始类型=object
2025-07-15 16:51:50.867 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 employee_type_code: 样例=['01', '01', '01'], 类型=object
2025-07-15 16:51:50.867 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 employee_type_code 格式化完成，无小数点问题
2025-07-15 16:51:50.867 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 employee_type: 原始样例=['教学单位专技人员', '教学单位专技人员', '教学院其它人员'], 原始类型=object
2025-07-15 16:51:50.867 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 employee_type: 样例=['教学单位专技人员', '教学单位专技人员', '教学院其它人员'], 类型=object
2025-07-15 16:51:50.867 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 employee_type 格式化完成，无小数点问题
2025-07-15 16:51:50.867 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 工号 不存在于数据框中
2025-07-15 16:51:50.867 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 姓名 不存在于数据框中
2025-07-15 16:51:50.867 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 部门名称 不存在于数据框中
2025-07-15 16:51:50.867 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 人员类别代码 不存在于数据框中
2025-07-15 16:51:50.867 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 人员类别 不存在于数据框中
2025-07-15 16:51:50.867 | INFO     | src.modules.data_storage.salary_data_formatter:_format_integer_fields:604 | 🔧 [强化调试] 开始格式化整型字段: []
2025-07-15 16:51:50.867 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:655 | 🔧 [强化调试] 开始格式化浮点型字段: ['position_salary_2025', 'grade_salary_2025', 'allowance', 'balance_allowance', 'basic_performance_2025', 'health_fee', 'transport_allowance', 'property_allowance', 'communication_allowance', 'performance_bonus_2025', 'provident_fund_2025', 'housing_allowance', 'car_allowance', 'supplement', 'advance', 'total_salary', 'pension_insurance', '2025年岗位工资', '2025年薪级工资', '津贴', '结余津贴', '2025年基础性绩效', '卫生费', '交通补贴', '物业补贴', '通讯补贴', '2025年奖励性绩效预发', '2025公积金', '住房补贴', '车补', '补发', '借支', '应发工资', '代扣代存养老保险']
2025-07-15 16:51:50.867 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 position_salary_2025: 原始样例=[1925.0, 3030.0, 3455.0], 原始类型=float64
2025-07-15 16:51:50.882 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 position_salary_2025: 样例=[1925.0, 3030.0, 3455.0], 类型=float64
2025-07-15 16:51:50.882 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 position_salary_2025 格式化完成，保留两位小数
2025-07-15 16:51:50.882 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 grade_salary_2025: 原始样例=[1427.0, 1515.0, 3391.0], 原始类型=float64
2025-07-15 16:51:50.882 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 grade_salary_2025: 样例=[1427.0, 1515.0, 3391.0], 类型=float64
2025-07-15 16:51:50.882 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 grade_salary_2025 格式化完成，保留两位小数
2025-07-15 16:51:50.882 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 allowance: 原始样例=[0.0, 0.0, 134.0], 原始类型=float64
2025-07-15 16:51:50.882 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 allowance: 样例=[0.0, 0.0, 134.0], 类型=float64
2025-07-15 16:51:50.882 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 allowance 格式化完成，保留两位小数
2025-07-15 16:51:50.898 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 balance_allowance: 原始样例=[56.0, 56.0, 56.0], 原始类型=float64
2025-07-15 16:51:50.898 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 balance_allowance: 样例=[56.0, 56.0, 56.0], 类型=float64
2025-07-15 16:51:50.898 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 balance_allowance 格式化完成，保留两位小数
2025-07-15 16:51:50.898 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 basic_performance_2025: 原始样例=[2824.0, 3466.0, 4108.0], 原始类型=float64
2025-07-15 16:51:50.898 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 basic_performance_2025: 样例=[2824.0, 3466.0, 4108.0], 类型=float64
2025-07-15 16:51:50.898 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 basic_performance_2025 格式化完成，保留两位小数
2025-07-15 16:51:50.898 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 health_fee: 原始样例=[0.0, nan, 0.0], 原始类型=float64
2025-07-15 16:51:50.898 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 health_fee: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-15 16:51:50.898 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 health_fee 格式化完成，保留两位小数
2025-07-15 16:51:50.898 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 transport_allowance: 原始样例=[220.0, 220.0, 220.0], 原始类型=float64
2025-07-15 16:51:50.898 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 transport_allowance: 样例=[220.0, 220.0, 220.0], 类型=float64
2025-07-15 16:51:50.898 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 transport_allowance 格式化完成，保留两位小数
2025-07-15 16:51:50.898 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 property_allowance: 原始样例=[200.0, 240.0, 240.0], 原始类型=float64
2025-07-15 16:51:50.898 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 property_allowance: 样例=[200.0, 240.0, 240.0], 类型=float64
2025-07-15 16:51:50.898 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 property_allowance 格式化完成，保留两位小数
2025-07-15 16:51:50.898 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 communication_allowance: 原始样例=[nan, nan, 50.0], 原始类型=float64
2025-07-15 16:51:50.898 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 communication_allowance: 样例=[0.0, 0.0, 50.0], 类型=float64
2025-07-15 16:51:50.898 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 communication_allowance 格式化完成，保留两位小数
2025-07-15 16:51:50.898 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 performance_bonus_2025: 原始样例=[1000.0, 0.0, 2400.0], 原始类型=float64
2025-07-15 16:51:50.914 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 performance_bonus_2025: 样例=[1000.0, 0.0, 2400.0], 类型=float64
2025-07-15 16:51:50.914 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 performance_bonus_2025 格式化完成，保留两位小数
2025-07-15 16:51:50.914 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 provident_fund_2025: 原始样例=[1428.0, 2216.0, 2274.0], 原始类型=float64
2025-07-15 16:51:50.914 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 provident_fund_2025: 样例=[1428.0, 2216.0, 2274.0], 类型=float64
2025-07-15 16:51:50.914 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 provident_fund_2025 格式化完成，保留两位小数
2025-07-15 16:51:50.914 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 housing_allowance: 原始样例=[188.76571428819443, 204.8512760416667, 315.672219058336], 原始类型=float64
2025-07-15 16:51:50.914 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 housing_allowance: 样例=[188.77, 204.85, 315.67], 类型=float64
2025-07-15 16:51:50.914 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 housing_allowance 格式化完成，保留两位小数
2025-07-15 16:51:50.930 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 car_allowance: 原始样例=[None, None, None], 原始类型=object
2025-07-15 16:51:50.930 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 car_allowance: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-15 16:51:50.930 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 car_allowance 格式化完成，保留两位小数
2025-07-15 16:51:50.930 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 supplement: 原始样例=[None, None, None], 原始类型=object
2025-07-15 16:51:50.930 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 supplement: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-15 16:51:50.930 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 supplement 格式化完成，保留两位小数
2025-07-15 16:51:50.930 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 advance: 原始样例=[nan, 2799.4, nan], 原始类型=float64
2025-07-15 16:51:50.930 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 advance: 样例=[0.0, 2799.4, 0.0], 类型=float64
2025-07-15 16:51:50.930 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 advance 格式化完成，保留两位小数
2025-07-15 16:51:50.930 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 total_salary: 原始样例=[7840.765714288194, 5932.451276041667, 14369.672219058337], 原始类型=float64
2025-07-15 16:51:50.930 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 total_salary: 样例=[7840.77, 5932.45, 14369.67], 类型=float64
2025-07-15 16:51:50.930 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 total_salary 格式化完成，保留两位小数
2025-07-15 16:51:50.930 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 pension_insurance: 原始样例=[977.64, 1416.96, 1867.56], 原始类型=float64
2025-07-15 16:51:50.930 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 pension_insurance: 样例=[977.64, 1416.96, 1867.56], 类型=float64
2025-07-15 16:51:50.930 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 pension_insurance 格式化完成，保留两位小数
2025-07-15 16:51:50.930 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 2025年岗位工资 不存在于数据框中
2025-07-15 16:51:50.930 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 2025年薪级工资 不存在于数据框中
2025-07-15 16:51:50.930 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 津贴 不存在于数据框中
2025-07-15 16:51:50.930 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 结余津贴 不存在于数据框中
2025-07-15 16:51:50.946 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 2025年基础性绩效 不存在于数据框中
2025-07-15 16:51:50.946 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 卫生费 不存在于数据框中
2025-07-15 16:51:50.946 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 交通补贴 不存在于数据框中
2025-07-15 16:51:50.946 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 物业补贴 不存在于数据框中
2025-07-15 16:51:50.946 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 通讯补贴 不存在于数据框中
2025-07-15 16:51:50.946 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 2025年奖励性绩效预发 不存在于数据框中
2025-07-15 16:51:50.946 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 2025公积金 不存在于数据框中
2025-07-15 16:51:50.961 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 住房补贴 不存在于数据框中
2025-07-15 16:51:50.961 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 车补 不存在于数据框中
2025-07-15 16:51:50.961 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 补发 不存在于数据框中
2025-07-15 16:51:50.961 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 借支 不存在于数据框中
2025-07-15 16:51:50.961 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 应发工资 不存在于数据框中
2025-07-15 16:51:50.961 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 代扣代存养老保险 不存在于数据框中
2025-07-15 16:51:50.961 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:711 | 🔧 [特殊字段] 开始处理表类型 active_employees 的月份、年份字段
2025-07-15 16:51:50.961 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:731 | 🔧 [特殊字段] 处理月份字段 month: 原始样例=['2025-07', '2025-07', '2025-07'], 原始类型=object
2025-07-15 16:51:50.961 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:774 | 🔧 [特殊字段] 处理后月份字段 month: 样例=['07', '07', '07']
2025-07-15 16:51:50.961 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:785 | 🔧 [验证成功] 月份字段 month 格式化完成，所有值为有效月份格式
2025-07-15 16:51:50.961 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:805 | 🔧 [特殊字段] 处理年份字段 year: 原始样例=[2025, 2025, 2025], 原始类型=int64
2025-07-15 16:51:50.961 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:845 | 🔧 [特殊字段] 处理后年份字段 year: 样例=['2025', '2025', '2025']
2025-07-15 16:51:50.961 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:856 | 🔧 [验证成功] 年份字段 year 格式化完成，所有值为有效年份格式
2025-07-15 16:51:50.961 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:866 | 🔧 [特殊字段] 月份、年份字段处理完成
2025-07-15 16:51:50.977 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:291 | 数据格式化完成: 50行, 24列
2025-07-15 16:51:50.977 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3560 | 最大可见行数已更新: 50 -> 50
2025-07-15 16:51:50.977 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:3613 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-15 16:51:50.977 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第0行数据工号: 20171604
2025-07-15 16:51:50.977 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第1行数据工号: 20181638
2025-07-15 16:51:50.977 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第2行数据工号: 19930191
2025-07-15 16:51:50.992 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第3行数据工号: 20181669
2025-07-15 16:51:50.992 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第4行数据工号: 20191722
2025-07-15 16:51:50.992 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:457 | 🔧 [排序修复] 可见行数据顺序: ['20171604', '20181638', '19930191', '20181669', '20191722']
2025-07-15 16:51:50.992 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:424 | 表格数据已设置: 50 行, 24 列
2025-07-15 16:51:51.007 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2174 | 🔧 [性能分析] 表格数据设置完成: 50 行, 最大可见行数: 50, 总耗时: 30.87ms
2025-07-15 16:51:51.007 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 初始化和状态: 0.00ms (0.0%)
2025-07-15 16:51:51.007 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 字段映射: 0.00ms (0.0%)
2025-07-15 16:51:51.007 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 数据模型更新: 15.19ms (49.2%)
2025-07-15 16:51:51.007 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 表头设置: 0.00ms (0.0%)
2025-07-15 16:51:51.007 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 设置行数: 0.00ms (0.0%)
2025-07-15 16:51:51.007 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 填充可见数据: 15.68ms (50.8%)
2025-07-15 16:51:51.007 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 调整列宽: 0.00ms (0.0%)
2025-07-15 16:51:51.023 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 设置行号: 0.00ms (0.0%)
2025-07-15 16:51:51.023 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2182 | 🔧 [性能分析] 最耗时步骤: 填充可见数据 (15.68ms)
2025-07-15 16:51:51.023 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:1038 | 🔧 [紧急修复] 分页处理完成: 第2页, 50条记录
2025-07-15 16:51:51.023 | ERROR    | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3449 | 处理分页变化事件失败: 'PrototypeMainWindow' object has no attribute 'current_table_name'
2025-07-15 16:51:51.023 | INFO     | src.gui.widgets.pagination_widget:set_current_page:330 | 页码切换到: 2
2025-07-15 16:52:06.992 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:964 | 🔧 [紧急修复] 开始分页处理: 第3页, 表名: salary_data_2025_07_active_employees
2025-07-15 16:52:06.992 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:439 | 正在从表 salary_data_2025_07_active_employees 分页获取数据: 第3页, 每页50条
2025-07-15 16:52:06.992 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:462 | 成功从表 salary_data_2025_07_active_employees 获取第3页数据: 50 行，总计1396行
2025-07-15 16:52:07.109 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:133 | 使用已存在的配置文件: state/data/field_mappings.json
2025-07-15 16:52:07.109 | INFO     | src.modules.data_import.config_sync_manager:__init__:70 | 配置同步管理器初始化完成
2025-07-15 16:52:07.109 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:1151 | 🔧 [修复] 没有找到匹配的字段映射
2025-07-15 16:52:07.109 | INFO     | src.gui.prototype.prototype_main_window:_apply_system_field_filtering:1183 | 🔧 [修复] 过滤了 4 个系统字段
2025-07-15 16:52:07.221 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:133 | 使用已存在的配置文件: state/data/field_mappings.json
2025-07-15 16:52:07.221 | INFO     | src.modules.data_import.config_sync_manager:__init__:70 | 配置同步管理器初始化完成
2025-07-15 16:52:07.221 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:1220 | 🔧 [修复] 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-15 16:52:07.221 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:238 | 开始格式化工资表数据 - 表类型: salary_data_2025_07_active_employees
2025-07-15 16:52:07.221 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:251 | 原始数据: 50行, 24列
2025-07-15 16:52:07.221 | INFO     | src.modules.data_storage.salary_data_formatter:_extract_table_type_from_name:335 | 🔧 [表名映射] 通过关键词 'active_employees' 匹配到表类型: active_employees
2025-07-15 16:52:07.221 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:255 | 🔧 [表名映射] 输入表名: salary_data_2025_07_active_employees, 提取的表类型: active_employees
2025-07-15 16:52:07.221 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:259 | 使用表类型配置: active_employees, 配置字段: ['id_fields', 'string_fields', 'integer_fields', 'float_fields', 'special_fields']
2025-07-15 16:52:07.221 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:263 | 🔧 [格式化调试] 开始处理ID字段: ['employee_id', 'employee_type_code', '工号', '人员类别代码']
2025-07-15 16:52:07.221 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:268 | 🔧 [格式化调试] 处理前 employee_id 字段样例: ['20181658.0', '20181671.0', '20191701.0']
2025-07-15 16:52:07.221 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:268 | 🔧 [格式化调试] 处理前 employee_type_code 字段样例: ['1.0', '1.0', '1.0']
2025-07-15 16:52:07.221 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:377 | 🔧 [强化调试] 处理ID字段 employee_id: 原始样例=['20181658.0', '20181671.0', '20191701.0'], 原始类型=object
2025-07-15 16:52:07.221 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:426 | 🔧 [强化调试] 清理后ID字段 employee_id: 样例=['20181658', '20181671', '20191701']
2025-07-15 16:52:07.221 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:443 | 🔧 [强化调试] 最终ID字段 employee_id: 样例=['20181658', '20181671', '20191701']
2025-07-15 16:52:07.237 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:454 | 🔧 [验证成功] ID字段 employee_id 格式化完成，无小数点问题
2025-07-15 16:52:07.237 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:377 | 🔧 [强化调试] 处理ID字段 employee_type_code: 原始样例=['1.0', '1.0', '1.0'], 原始类型=object
2025-07-15 16:52:07.237 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:426 | 🔧 [强化调试] 清理后ID字段 employee_type_code: 样例=['1', '1', '1']
2025-07-15 16:52:07.237 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:443 | 🔧 [强化调试] 最终ID字段 employee_type_code: 样例=['01', '01', '01']
2025-07-15 16:52:07.237 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:454 | 🔧 [验证成功] ID字段 employee_type_code 格式化完成，无小数点问题
2025-07-15 16:52:07.237 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_id_fields:461 | 🔧 [警告] ID字段 工号 不存在于数据框中
2025-07-15 16:52:07.237 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_id_fields:461 | 🔧 [警告] ID字段 人员类别代码 不存在于数据框中
2025-07-15 16:52:07.237 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:274 | 🔧 [格式化调试] 处理后 employee_id 字段样例: ['20181658', '20181671', '20191701']
2025-07-15 16:52:07.237 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:274 | 🔧 [格式化调试] 处理后 employee_type_code 字段样例: ['01', '01', '01']
2025-07-15 16:52:07.252 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:472 | 🔧 [强化调试] 开始格式化字符串字段: ['employee_id', 'employee_name', 'department', 'employee_type_code', 'employee_type', '工号', '姓名', '部门名称', '人员类别代码', '人员类别']
2025-07-15 16:52:07.252 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 employee_id: 原始样例=['20181658', '20181671', '20191701'], 原始类型=object
2025-07-15 16:52:07.252 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:504 | 🔧 [修复] 工号字段 employee_id 已特殊处理，去除小数点格式
2025-07-15 16:52:07.252 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 employee_id: 样例=['20181658', '20181671', '20191701'], 类型=object
2025-07-15 16:52:07.252 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 employee_id 格式化完成，无小数点问题
2025-07-15 16:52:07.252 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 employee_name: 原始样例=['石彬怡', '罗超', '余音'], 原始类型=object
2025-07-15 16:52:07.252 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 employee_name: 样例=['石彬怡', '罗超', '余音'], 类型=object
2025-07-15 16:52:07.252 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 employee_name 格式化完成，无小数点问题
2025-07-15 16:52:07.252 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 department: 原始样例=['音乐学院', '音乐学院', '音乐学院'], 原始类型=object
2025-07-15 16:52:07.252 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 department: 样例=['音乐学院', '音乐学院', '音乐学院'], 类型=object
2025-07-15 16:52:07.269 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 department 格式化完成，无小数点问题
2025-07-15 16:52:07.269 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 employee_type_code: 原始样例=['01', '01', '01'], 原始类型=object
2025-07-15 16:52:07.269 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 employee_type_code: 样例=['01', '01', '01'], 类型=object
2025-07-15 16:52:07.269 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 employee_type_code 格式化完成，无小数点问题
2025-07-15 16:52:07.269 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 employee_type: 原始样例=['教学单位专技人员', '教学单位专技人员', '教学单位专技人员'], 原始类型=object
2025-07-15 16:52:07.269 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 employee_type: 样例=['教学单位专技人员', '教学单位专技人员', '教学单位专技人员'], 类型=object
2025-07-15 16:52:07.269 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 employee_type 格式化完成，无小数点问题
2025-07-15 16:52:07.269 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 工号 不存在于数据框中
2025-07-15 16:52:07.269 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 姓名 不存在于数据框中
2025-07-15 16:52:07.284 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 部门名称 不存在于数据框中
2025-07-15 16:52:07.284 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 人员类别代码 不存在于数据框中
2025-07-15 16:52:07.284 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 人员类别 不存在于数据框中
2025-07-15 16:52:07.284 | INFO     | src.modules.data_storage.salary_data_formatter:_format_integer_fields:604 | 🔧 [强化调试] 开始格式化整型字段: []
2025-07-15 16:52:07.284 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:655 | 🔧 [强化调试] 开始格式化浮点型字段: ['position_salary_2025', 'grade_salary_2025', 'allowance', 'balance_allowance', 'basic_performance_2025', 'health_fee', 'transport_allowance', 'property_allowance', 'communication_allowance', 'performance_bonus_2025', 'provident_fund_2025', 'housing_allowance', 'car_allowance', 'supplement', 'advance', 'total_salary', 'pension_insurance', '2025年岗位工资', '2025年薪级工资', '津贴', '结余津贴', '2025年基础性绩效', '卫生费', '交通补贴', '物业补贴', '通讯补贴', '2025年奖励性绩效预发', '2025公积金', '住房补贴', '车补', '补发', '借支', '应发工资', '代扣代存养老保险']
2025-07-15 16:52:07.284 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 position_salary_2025: 原始样例=[2185.0, 2185.0, 2185.0], 原始类型=float64
2025-07-15 16:52:07.284 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 position_salary_2025: 样例=[2185.0, 2185.0, 2185.0], 类型=float64
2025-07-15 16:52:07.284 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 position_salary_2025 格式化完成，保留两位小数
2025-07-15 16:52:07.284 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 grade_salary_2025: 原始样例=[1251.0, 1251.0, 1427.0], 原始类型=float64
2025-07-15 16:52:07.284 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 grade_salary_2025: 样例=[1251.0, 1251.0, 1427.0], 类型=float64
2025-07-15 16:52:07.284 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 grade_salary_2025 格式化完成，保留两位小数
2025-07-15 16:52:07.284 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 allowance: 原始样例=[0.0, 0.0, 0.0], 原始类型=float64
2025-07-15 16:52:07.299 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 allowance: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-15 16:52:07.299 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 allowance 格式化完成，保留两位小数
2025-07-15 16:52:07.299 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 balance_allowance: 原始样例=[56.0, 56.0, 56.0], 原始类型=float64
2025-07-15 16:52:07.299 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 balance_allowance: 样例=[56.0, 56.0, 56.0], 类型=float64
2025-07-15 16:52:07.299 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 balance_allowance 格式化完成，保留两位小数
2025-07-15 16:52:07.299 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 basic_performance_2025: 原始样例=[2978.0, 2978.0, 2978.0], 原始类型=float64
2025-07-15 16:52:07.299 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 basic_performance_2025: 样例=[2978.0, 2978.0, 2978.0], 类型=float64
2025-07-15 16:52:07.299 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 basic_performance_2025 格式化完成，保留两位小数
2025-07-15 16:52:07.299 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 health_fee: 原始样例=[20.0, nan, 20.0], 原始类型=float64
2025-07-15 16:52:07.299 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 health_fee: 样例=[20.0, 0.0, 20.0], 类型=float64
2025-07-15 16:52:07.315 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 health_fee 格式化完成，保留两位小数
2025-07-15 16:52:07.315 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 transport_allowance: 原始样例=[220.0, 220.0, 220.0], 原始类型=float64
2025-07-15 16:52:07.315 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 transport_allowance: 样例=[220.0, 220.0, 220.0], 类型=float64
2025-07-15 16:52:07.315 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 transport_allowance 格式化完成，保留两位小数
2025-07-15 16:52:07.315 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 property_allowance: 原始样例=[200.0, 200.0, 200.0], 原始类型=float64
2025-07-15 16:52:07.315 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 property_allowance: 样例=[200.0, 200.0, 200.0], 类型=float64
2025-07-15 16:52:07.315 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 property_allowance 格式化完成，保留两位小数
2025-07-15 16:52:07.315 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 communication_allowance: 原始样例=[nan, nan, nan], 原始类型=float64
2025-07-15 16:52:07.315 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 communication_allowance: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-15 16:52:07.315 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 communication_allowance 格式化完成，保留两位小数
2025-07-15 16:52:07.315 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 performance_bonus_2025: 原始样例=[1000.0, 1500.0, 1500.0], 原始类型=float64
2025-07-15 16:52:07.331 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 performance_bonus_2025: 样例=[1000.0, 1500.0, 1500.0], 类型=float64
2025-07-15 16:52:07.331 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 performance_bonus_2025 格式化完成，保留两位小数
2025-07-15 16:52:07.331 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 provident_fund_2025: 原始样例=[1353.0, 1550.0, 1603.0], 原始类型=float64
2025-07-15 16:52:07.331 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 provident_fund_2025: 样例=[1353.0, 1550.0, 1603.0], 类型=float64
2025-07-15 16:52:07.331 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 provident_fund_2025 格式化完成，保留两位小数
2025-07-15 16:52:07.331 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 housing_allowance: 原始样例=[161.57560625, 159.740296875, 188.5505138888889], 原始类型=float64
2025-07-15 16:52:07.331 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 housing_allowance: 样例=[161.58, 159.74, 188.55], 类型=float64
2025-07-15 16:52:07.331 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 housing_allowance 格式化完成，保留两位小数
2025-07-15 16:52:07.331 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 car_allowance: 原始样例=[None, None, None], 原始类型=object
2025-07-15 16:52:07.331 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 car_allowance: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-15 16:52:07.331 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 car_allowance 格式化完成，保留两位小数
2025-07-15 16:52:07.347 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 supplement: 原始样例=[None, None, None], 原始类型=object
2025-07-15 16:52:07.347 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 supplement: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-15 16:52:07.347 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 supplement 格式化完成，保留两位小数
2025-07-15 16:52:07.347 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 advance: 原始样例=[nan, nan, 2000.0], 原始类型=float64
2025-07-15 16:52:07.347 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 advance: 样例=[0.0, 0.0, 2000.0], 类型=float64
2025-07-15 16:52:07.347 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 advance 格式化完成，保留两位小数
2025-07-15 16:52:07.347 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 total_salary: 原始样例=[8071.57560625, 8549.740296875, 6774.550513888889], 原始类型=float64
2025-07-15 16:52:07.347 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 total_salary: 样例=[8071.58, 8549.74, 6774.55], 类型=float64
2025-07-15 16:52:07.347 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 total_salary 格式化完成，保留两位小数
2025-07-15 16:52:07.347 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 pension_insurance: 原始样例=[1113.72, 1113.72, 1113.75], 原始类型=float64
2025-07-15 16:52:07.347 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 pension_insurance: 样例=[1113.72, 1113.72, 1113.75], 类型=float64
2025-07-15 16:52:07.362 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 pension_insurance 格式化完成，保留两位小数
2025-07-15 16:52:07.362 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 2025年岗位工资 不存在于数据框中
2025-07-15 16:52:07.362 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 2025年薪级工资 不存在于数据框中
2025-07-15 16:52:07.362 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 津贴 不存在于数据框中
2025-07-15 16:52:07.362 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 结余津贴 不存在于数据框中
2025-07-15 16:52:07.362 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 2025年基础性绩效 不存在于数据框中
2025-07-15 16:52:07.362 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 卫生费 不存在于数据框中
2025-07-15 16:52:07.362 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 交通补贴 不存在于数据框中
2025-07-15 16:52:07.362 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 物业补贴 不存在于数据框中
2025-07-15 16:52:07.362 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 通讯补贴 不存在于数据框中
2025-07-15 16:52:07.362 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 2025年奖励性绩效预发 不存在于数据框中
2025-07-15 16:52:07.378 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 2025公积金 不存在于数据框中
2025-07-15 16:52:07.378 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 住房补贴 不存在于数据框中
2025-07-15 16:52:07.378 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 车补 不存在于数据框中
2025-07-15 16:52:07.378 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 补发 不存在于数据框中
2025-07-15 16:52:07.378 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 借支 不存在于数据框中
2025-07-15 16:52:07.378 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 应发工资 不存在于数据框中
2025-07-15 16:52:07.378 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 代扣代存养老保险 不存在于数据框中
2025-07-15 16:52:07.378 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:711 | 🔧 [特殊字段] 开始处理表类型 active_employees 的月份、年份字段
2025-07-15 16:52:07.378 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:731 | 🔧 [特殊字段] 处理月份字段 month: 原始样例=['2025-07', '2025-07', '2025-07'], 原始类型=object
2025-07-15 16:52:07.378 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:774 | 🔧 [特殊字段] 处理后月份字段 month: 样例=['07', '07', '07']
2025-07-15 16:52:07.378 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:785 | 🔧 [验证成功] 月份字段 month 格式化完成，所有值为有效月份格式
2025-07-15 16:52:07.378 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:805 | 🔧 [特殊字段] 处理年份字段 year: 原始样例=[2025, 2025, 2025], 原始类型=int64
2025-07-15 16:52:07.378 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:845 | 🔧 [特殊字段] 处理后年份字段 year: 样例=['2025', '2025', '2025']
2025-07-15 16:52:07.394 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:856 | 🔧 [验证成功] 年份字段 year 格式化完成，所有值为有效年份格式
2025-07-15 16:52:07.394 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:866 | 🔧 [特殊字段] 月份、年份字段处理完成
2025-07-15 16:52:07.394 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:291 | 数据格式化完成: 50行, 24列
2025-07-15 16:52:07.394 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3560 | 最大可见行数已更新: 50 -> 50
2025-07-15 16:52:07.394 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:3613 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-15 16:52:07.409 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第0行数据工号: 20181658
2025-07-15 16:52:07.409 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第1行数据工号: 20181671
2025-07-15 16:52:07.409 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第2行数据工号: 20191701
2025-07-15 16:52:07.409 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第3行数据工号: 20191777
2025-07-15 16:52:07.409 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第4行数据工号: 20201018
2025-07-15 16:52:07.409 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:457 | 🔧 [排序修复] 可见行数据顺序: ['20181658', '20181671', '20191701', '20191777', '20201018']
2025-07-15 16:52:07.409 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:424 | 表格数据已设置: 50 行, 24 列
2025-07-15 16:52:07.440 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2174 | 🔧 [性能分析] 表格数据设置完成: 50 行, 最大可见行数: 50, 总耗时: 46.56ms
2025-07-15 16:52:07.440 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 初始化和状态: 15.22ms (32.7%)
2025-07-15 16:52:07.440 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 字段映射: 0.00ms (0.0%)
2025-07-15 16:52:07.440 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 数据模型更新: 0.00ms (0.0%)
2025-07-15 16:52:07.440 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 表头设置: 0.00ms (0.0%)
2025-07-15 16:52:07.440 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 设置行数: 0.00ms (0.0%)
2025-07-15 16:52:07.440 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 填充可见数据: 31.35ms (67.3%)
2025-07-15 16:52:07.440 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 调整列宽: 0.00ms (0.0%)
2025-07-15 16:52:07.440 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 设置行号: 0.00ms (0.0%)
2025-07-15 16:52:07.440 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2182 | 🔧 [性能分析] 最耗时步骤: 填充可见数据 (31.35ms)
2025-07-15 16:52:07.440 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:1038 | 🔧 [紧急修复] 分页处理完成: 第3页, 50条记录
2025-07-15 16:52:07.440 | ERROR    | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3449 | 处理分页变化事件失败: 'PrototypeMainWindow' object has no attribute 'current_table_name'
2025-07-15 16:52:07.440 | INFO     | src.gui.widgets.pagination_widget:set_current_page:330 | 页码切换到: 3
2025-07-15 16:52:13.003 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_changed:5401 | 排序变化: 1 列
2025-07-15 16:52:13.003 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_changed:5413 | 🔧 [调试] 数据重载状态正常，继续处理排序
2025-07-15 16:52:13.018 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_changed:5417 | 🔧 [调试] 开始更新排序指示器
2025-07-15 16:52:13.018 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_sort_indicators:5797 | 🔧 [排序指示器修复] 设置排序指示器: 列6, 顺序ascending
2025-07-15 16:52:13.018 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_changed:5419 | 🔧 [调试] 排序指示器更新完成
2025-07-15 16:52:13.018 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_changed:5425 | 🔧 [调试] 准备调用_save_and_apply_sort_state
2025-07-15 16:52:13.018 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5441 | 🔧 [调试] 开始保存排序状态: 1 列
2025-07-15 16:52:13.018 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5457 | 🔧 [调试] 已获取主窗口: PrototypeMainWindow
2025-07-15 16:52:13.018 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5463 | 🔧 [调试] 表名: , 表类型: 
2025-07-15 16:52:13.018 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5466 | 🔧 [调试] 无法获取当前表名，跳过全表排序
2025-07-15 16:52:13.018 | INFO     | src.gui.multi_column_sort_manager:add_sort_column:274 | 添加排序列: 6(grade_salary_2025) -> ascending, 当前排序列数: 1
2025-07-15 16:52:13.018 | INFO     | src.gui.multi_column_sort_manager:_on_sort_indicator_changed:211 | 排序指示器变化: 列6(grade_salary_2025) -> ascending
2025-07-15 16:52:13.018 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_header_sort_changed:5890 | 🔧 [表格排序] 表头排序变化: 列6, 顺序ASC
2025-07-15 16:52:13.018 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_changed:5401 | 排序变化: 1 列
2025-07-15 16:52:13.018 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_changed:5413 | 🔧 [调试] 数据重载状态正常，继续处理排序
2025-07-15 16:52:13.018 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_changed:5417 | 🔧 [调试] 开始更新排序指示器
2025-07-15 16:52:13.018 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_sort_indicators:5797 | 🔧 [排序指示器修复] 设置排序指示器: 列6, 顺序ascending
2025-07-15 16:52:13.018 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_changed:5419 | 🔧 [调试] 排序指示器更新完成
2025-07-15 16:52:13.018 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_changed:5425 | 🔧 [调试] 准备调用_save_and_apply_sort_state
2025-07-15 16:52:13.018 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5441 | 🔧 [调试] 开始保存排序状态: 1 列
2025-07-15 16:52:13.018 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5457 | 🔧 [调试] 已获取主窗口: PrototypeMainWindow
2025-07-15 16:52:13.035 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5463 | 🔧 [调试] 表名: , 表类型: 
2025-07-15 16:52:13.035 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5466 | 🔧 [调试] 无法获取当前表名，跳过全表排序
2025-07-15 16:52:13.035 | INFO     | src.gui.multi_column_sort_manager:add_sort_column:274 | 添加排序列: 6(grade_salary_2025) -> ascending, 当前排序列数: 1
2025-07-15 16:52:13.035 | INFO     | src.gui.multi_column_sort_manager:_on_sort_indicator_changed:211 | 排序指示器变化: 列6(grade_salary_2025) -> ascending
2025-07-15 16:52:13.035 | ERROR    | src.gui.prototype.prototype_main_window:_on_sort_indicator_changed:3149 | 🔧 [全局排序] 排序处理失败: 'PrototypeMainWindow' object has no attribute 'current_table_name'
2025-07-15 16:52:19.737 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:964 | 🔧 [紧急修复] 开始分页处理: 第4页, 表名: salary_data_2025_07_active_employees
2025-07-15 16:52:19.737 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:439 | 正在从表 salary_data_2025_07_active_employees 分页获取数据: 第4页, 每页50条
2025-07-15 16:52:19.737 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:462 | 成功从表 salary_data_2025_07_active_employees 获取第4页数据: 50 行，总计1396行
2025-07-15 16:52:19.852 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:133 | 使用已存在的配置文件: state/data/field_mappings.json
2025-07-15 16:52:19.852 | INFO     | src.modules.data_import.config_sync_manager:__init__:70 | 配置同步管理器初始化完成
2025-07-15 16:52:19.852 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:1151 | 🔧 [修复] 没有找到匹配的字段映射
2025-07-15 16:52:19.852 | INFO     | src.gui.prototype.prototype_main_window:_apply_system_field_filtering:1183 | 🔧 [修复] 过滤了 4 个系统字段
2025-07-15 16:52:19.965 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:133 | 使用已存在的配置文件: state/data/field_mappings.json
2025-07-15 16:52:19.965 | INFO     | src.modules.data_import.config_sync_manager:__init__:70 | 配置同步管理器初始化完成
2025-07-15 16:52:19.965 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:1220 | 🔧 [修复] 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-15 16:52:19.965 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:238 | 开始格式化工资表数据 - 表类型: salary_data_2025_07_active_employees
2025-07-15 16:52:19.965 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:251 | 原始数据: 50行, 24列
2025-07-15 16:52:19.965 | INFO     | src.modules.data_storage.salary_data_formatter:_extract_table_type_from_name:335 | 🔧 [表名映射] 通过关键词 'active_employees' 匹配到表类型: active_employees
2025-07-15 16:52:19.965 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:255 | 🔧 [表名映射] 输入表名: salary_data_2025_07_active_employees, 提取的表类型: active_employees
2025-07-15 16:52:19.965 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:259 | 使用表类型配置: active_employees, 配置字段: ['id_fields', 'string_fields', 'integer_fields', 'float_fields', 'special_fields']
2025-07-15 16:52:19.965 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:263 | 🔧 [格式化调试] 开始处理ID字段: ['employee_id', 'employee_type_code', '工号', '人员类别代码']
2025-07-15 16:52:19.965 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:268 | 🔧 [格式化调试] 处理前 employee_id 字段样例: ['20030858.0', '20030860.0', '20030861.0']
2025-07-15 16:52:19.965 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:268 | 🔧 [格式化调试] 处理前 employee_type_code 字段样例: ['1.0', '1.0', '1.0']
2025-07-15 16:52:19.965 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:377 | 🔧 [强化调试] 处理ID字段 employee_id: 原始样例=['20030858.0', '20030860.0', '20030861.0'], 原始类型=object
2025-07-15 16:52:19.965 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:426 | 🔧 [强化调试] 清理后ID字段 employee_id: 样例=['20030858', '20030860', '20030861']
2025-07-15 16:52:19.981 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:443 | 🔧 [强化调试] 最终ID字段 employee_id: 样例=['20030858', '20030860', '20030861']
2025-07-15 16:52:19.981 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:454 | 🔧 [验证成功] ID字段 employee_id 格式化完成，无小数点问题
2025-07-15 16:52:19.981 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:377 | 🔧 [强化调试] 处理ID字段 employee_type_code: 原始样例=['1.0', '1.0', '1.0'], 原始类型=object
2025-07-15 16:52:19.981 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:426 | 🔧 [强化调试] 清理后ID字段 employee_type_code: 样例=['1', '1', '1']
2025-07-15 16:52:19.981 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:443 | 🔧 [强化调试] 最终ID字段 employee_type_code: 样例=['01', '01', '01']
2025-07-15 16:52:19.981 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:454 | 🔧 [验证成功] ID字段 employee_type_code 格式化完成，无小数点问题
2025-07-15 16:52:19.981 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_id_fields:461 | 🔧 [警告] ID字段 工号 不存在于数据框中
2025-07-15 16:52:19.981 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_id_fields:461 | 🔧 [警告] ID字段 人员类别代码 不存在于数据框中
2025-07-15 16:52:19.981 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:274 | 🔧 [格式化调试] 处理后 employee_id 字段样例: ['20030858', '20030860', '20030861']
2025-07-15 16:52:19.981 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:274 | 🔧 [格式化调试] 处理后 employee_type_code 字段样例: ['01', '01', '01']
2025-07-15 16:52:19.996 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:472 | 🔧 [强化调试] 开始格式化字符串字段: ['employee_id', 'employee_name', 'department', 'employee_type_code', 'employee_type', '工号', '姓名', '部门名称', '人员类别代码', '人员类别']
2025-07-15 16:52:19.996 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 employee_id: 原始样例=['20030858', '20030860', '20030861'], 原始类型=object
2025-07-15 16:52:19.996 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:504 | 🔧 [修复] 工号字段 employee_id 已特殊处理，去除小数点格式
2025-07-15 16:52:19.996 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 employee_id: 样例=['20030858', '20030860', '20030861'], 类型=object
2025-07-15 16:52:19.996 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 employee_id 格式化完成，无小数点问题
2025-07-15 16:52:19.996 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 employee_name: 原始样例=['张晶', '马丽', '孙颖君'], 原始类型=object
2025-07-15 16:52:19.996 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 employee_name: 样例=['张晶', '马丽', '孙颖君'], 类型=object
2025-07-15 16:52:19.996 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 employee_name 格式化完成，无小数点问题
2025-07-15 16:52:19.996 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 department: 原始样例=['艺术与设计学院', '艺术与设计学院', '艺术与设计学院'], 原始类型=object
2025-07-15 16:52:20.012 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 department: 样例=['艺术与设计学院', '艺术与设计学院', '艺术与设计学院'], 类型=object
2025-07-15 16:52:20.012 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 department 格式化完成，无小数点问题
2025-07-15 16:52:20.012 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 employee_type_code: 原始样例=['01', '01', '01'], 原始类型=object
2025-07-15 16:52:20.012 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 employee_type_code: 样例=['01', '01', '01'], 类型=object
2025-07-15 16:52:20.012 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 employee_type_code 格式化完成，无小数点问题
2025-07-15 16:52:20.012 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 employee_type: 原始样例=['教学单位专技人员', '教学单位专技人员', '教学单位专技人员'], 原始类型=object
2025-07-15 16:52:20.012 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 employee_type: 样例=['教学单位专技人员', '教学单位专技人员', '教学单位专技人员'], 类型=object
2025-07-15 16:52:20.012 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 employee_type 格式化完成，无小数点问题
2025-07-15 16:52:20.012 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 工号 不存在于数据框中
2025-07-15 16:52:20.028 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 姓名 不存在于数据框中
2025-07-15 16:52:20.028 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 部门名称 不存在于数据框中
2025-07-15 16:52:20.028 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 人员类别代码 不存在于数据框中
2025-07-15 16:52:20.028 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 人员类别 不存在于数据框中
2025-07-15 16:52:20.028 | INFO     | src.modules.data_storage.salary_data_formatter:_format_integer_fields:604 | 🔧 [强化调试] 开始格式化整型字段: []
2025-07-15 16:52:20.028 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:655 | 🔧 [强化调试] 开始格式化浮点型字段: ['position_salary_2025', 'grade_salary_2025', 'allowance', 'balance_allowance', 'basic_performance_2025', 'health_fee', 'transport_allowance', 'property_allowance', 'communication_allowance', 'performance_bonus_2025', 'provident_fund_2025', 'housing_allowance', 'car_allowance', 'supplement', 'advance', 'total_salary', 'pension_insurance', '2025年岗位工资', '2025年薪级工资', '津贴', '结余津贴', '2025年基础性绩效', '卫生费', '交通补贴', '物业补贴', '通讯补贴', '2025年奖励性绩效预发', '2025公积金', '住房补贴', '车补', '补发', '借支', '应发工资', '代扣代存养老保险']
2025-07-15 16:52:20.028 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 position_salary_2025: 原始样例=[3030.0, 2185.0, 2185.0], 原始类型=float64
2025-07-15 16:52:20.028 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 position_salary_2025: 样例=[3030.0, 2185.0, 2185.0], 类型=float64
2025-07-15 16:52:20.028 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 position_salary_2025 格式化完成，保留两位小数
2025-07-15 16:52:20.028 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 grade_salary_2025: 原始样例=[2175.0, 2175.0, 2175.0], 原始类型=float64
2025-07-15 16:52:20.028 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 grade_salary_2025: 样例=[2175.0, 2175.0, 2175.0], 类型=float64
2025-07-15 16:52:20.028 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 grade_salary_2025 格式化完成，保留两位小数
2025-07-15 16:52:20.028 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 allowance: 原始样例=[102.0, 102.0, 102.0], 原始类型=float64
2025-07-15 16:52:20.043 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 allowance: 样例=[102.0, 102.0, 102.0], 类型=float64
2025-07-15 16:52:20.043 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 allowance 格式化完成，保留两位小数
2025-07-15 16:52:20.043 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 balance_allowance: 原始样例=[56.0, 56.0, 56.0], 原始类型=float64
2025-07-15 16:52:20.043 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 balance_allowance: 样例=[56.0, 56.0, 56.0], 类型=float64
2025-07-15 16:52:20.043 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 balance_allowance 格式化完成，保留两位小数
2025-07-15 16:52:20.043 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 basic_performance_2025: 原始样例=[3466.0, 2978.0, 2978.0], 原始类型=float64
2025-07-15 16:52:20.043 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 basic_performance_2025: 样例=[3466.0, 2978.0, 2978.0], 类型=float64
2025-07-15 16:52:20.043 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 basic_performance_2025 格式化完成，保留两位小数
2025-07-15 16:52:20.043 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 health_fee: 原始样例=[20.0, 20.0, 20.0], 原始类型=float64
2025-07-15 16:52:20.043 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 health_fee: 样例=[20.0, 20.0, 20.0], 类型=float64
2025-07-15 16:52:20.043 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 health_fee 格式化完成，保留两位小数
2025-07-15 16:52:20.060 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 transport_allowance: 原始样例=[220.0, 220.0, 220.0], 原始类型=float64
2025-07-15 16:52:20.060 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 transport_allowance: 样例=[220.0, 220.0, 220.0], 类型=float64
2025-07-15 16:52:20.060 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 transport_allowance 格式化完成，保留两位小数
2025-07-15 16:52:20.060 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 property_allowance: 原始样例=[240.0, 200.0, 200.0], 原始类型=float64
2025-07-15 16:52:20.060 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 property_allowance: 样例=[240.0, 200.0, 200.0], 类型=float64
2025-07-15 16:52:20.060 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 property_allowance 格式化完成，保留两位小数
2025-07-15 16:52:20.060 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 communication_allowance: 原始样例=[nan, nan, nan], 原始类型=float64
2025-07-15 16:52:20.060 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 communication_allowance: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-15 16:52:20.060 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 communication_allowance 格式化完成，保留两位小数
2025-07-15 16:52:20.060 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 performance_bonus_2025: 原始样例=[1000.0, 1000.0, 1000.0], 原始类型=float64
2025-07-15 16:52:20.075 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 performance_bonus_2025: 样例=[1000.0, 1000.0, 1000.0], 类型=float64
2025-07-15 16:52:20.075 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 performance_bonus_2025 格式化完成，保留两位小数
2025-07-15 16:52:20.075 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 provident_fund_2025: 原始样例=[1601.0, 1302.0, 1315.0], 原始类型=float64
2025-07-15 16:52:20.075 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 provident_fund_2025: 样例=[1601.0, 1302.0, 1315.0], 类型=float64
2025-07-15 16:52:20.075 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 provident_fund_2025 格式化完成，保留两位小数
2025-07-15 16:52:20.075 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 housing_allowance: 原始样例=[234.94447261080052, 230.919880537911, 229.09599134547395], 原始类型=float64
2025-07-15 16:52:20.075 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 housing_allowance: 样例=[234.94, 230.92, 229.1], 类型=float64
2025-07-15 16:52:20.075 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 housing_allowance 格式化完成，保留两位小数
2025-07-15 16:52:20.075 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 car_allowance: 原始样例=[None, None, None], 原始类型=object
2025-07-15 16:52:20.075 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 car_allowance: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-15 16:52:20.075 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 car_allowance 格式化完成，保留两位小数
2025-07-15 16:52:20.075 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 supplement: 原始样例=[nan, nan, nan], 原始类型=float64
2025-07-15 16:52:20.075 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 supplement: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-15 16:52:20.075 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 supplement 格式化完成，保留两位小数
2025-07-15 16:52:20.075 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 advance: 原始样例=[nan, nan, nan], 原始类型=float64
2025-07-15 16:52:20.075 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 advance: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-15 16:52:20.075 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 advance 格式化完成，保留两位小数
2025-07-15 16:52:20.091 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 total_salary: 原始样例=[10543.944472610801, 9166.919880537911, 9165.095991345474], 原始类型=float64
2025-07-15 16:52:20.091 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 total_salary: 样例=[10543.94, 9166.92, 9165.1], 类型=float64
2025-07-15 16:52:20.091 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 total_salary 格式化完成，保留两位小数
2025-07-15 16:52:20.091 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 pension_insurance: 原始样例=[1489.8000000000002, 1213.44, 1213.44], 原始类型=float64
2025-07-15 16:52:20.091 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 pension_insurance: 样例=[1489.8, 1213.44, 1213.44], 类型=float64
2025-07-15 16:52:20.091 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 pension_insurance 格式化完成，保留两位小数
2025-07-15 16:52:20.106 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 2025年岗位工资 不存在于数据框中
2025-07-15 16:52:20.106 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 2025年薪级工资 不存在于数据框中
2025-07-15 16:52:20.106 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 津贴 不存在于数据框中
2025-07-15 16:52:20.106 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 结余津贴 不存在于数据框中
2025-07-15 16:52:20.106 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 2025年基础性绩效 不存在于数据框中
2025-07-15 16:52:20.106 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 卫生费 不存在于数据框中
2025-07-15 16:52:20.106 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 交通补贴 不存在于数据框中
2025-07-15 16:52:20.106 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 物业补贴 不存在于数据框中
2025-07-15 16:52:20.106 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 通讯补贴 不存在于数据框中
2025-07-15 16:52:20.106 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 2025年奖励性绩效预发 不存在于数据框中
2025-07-15 16:52:20.106 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 2025公积金 不存在于数据框中
2025-07-15 16:52:20.106 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 住房补贴 不存在于数据框中
2025-07-15 16:52:20.106 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 车补 不存在于数据框中
2025-07-15 16:52:20.106 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 补发 不存在于数据框中
2025-07-15 16:52:20.106 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 借支 不存在于数据框中
2025-07-15 16:52:20.106 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 应发工资 不存在于数据框中
2025-07-15 16:52:20.106 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 代扣代存养老保险 不存在于数据框中
2025-07-15 16:52:20.106 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:711 | 🔧 [特殊字段] 开始处理表类型 active_employees 的月份、年份字段
2025-07-15 16:52:20.122 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:731 | 🔧 [特殊字段] 处理月份字段 month: 原始样例=['2025-07', '2025-07', '2025-07'], 原始类型=object
2025-07-15 16:52:20.122 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:774 | 🔧 [特殊字段] 处理后月份字段 month: 样例=['07', '07', '07']
2025-07-15 16:52:20.122 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:785 | 🔧 [验证成功] 月份字段 month 格式化完成，所有值为有效月份格式
2025-07-15 16:52:20.122 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:805 | 🔧 [特殊字段] 处理年份字段 year: 原始样例=[2025, 2025, 2025], 原始类型=int64
2025-07-15 16:52:20.122 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:845 | 🔧 [特殊字段] 处理后年份字段 year: 样例=['2025', '2025', '2025']
2025-07-15 16:52:20.122 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:856 | 🔧 [验证成功] 年份字段 year 格式化完成，所有值为有效年份格式
2025-07-15 16:52:20.122 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:866 | 🔧 [特殊字段] 月份、年份字段处理完成
2025-07-15 16:52:20.137 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:291 | 数据格式化完成: 50行, 24列
2025-07-15 16:52:20.137 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3560 | 最大可见行数已更新: 50 -> 50
2025-07-15 16:52:20.137 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:3613 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-15 16:52:20.137 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第0行数据工号: 20030858
2025-07-15 16:52:20.137 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第1行数据工号: 20030860
2025-07-15 16:52:20.137 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第2行数据工号: 20030861
2025-07-15 16:52:20.137 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第3行数据工号: 20040864
2025-07-15 16:52:20.137 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第4行数据工号: 20040865
2025-07-15 16:52:20.137 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:457 | 🔧 [排序修复] 可见行数据顺序: ['20030858', '20030860', '20030861', '20040864', '20040865']
2025-07-15 16:52:20.137 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:424 | 表格数据已设置: 50 行, 24 列
2025-07-15 16:52:20.168 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2174 | 🔧 [性能分析] 表格数据设置完成: 50 行, 最大可见行数: 50, 总耗时: 31.35ms
2025-07-15 16:52:20.168 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 初始化和状态: 0.00ms (0.0%)
2025-07-15 16:52:20.168 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 字段映射: 0.00ms (0.0%)
2025-07-15 16:52:20.168 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 数据模型更新: 0.00ms (0.0%)
2025-07-15 16:52:20.168 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 表头设置: 0.00ms (0.0%)
2025-07-15 16:52:20.168 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 设置行数: 0.00ms (0.0%)
2025-07-15 16:52:20.168 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 填充可见数据: 31.35ms (100.0%)
2025-07-15 16:52:20.168 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 调整列宽: 0.00ms (0.0%)
2025-07-15 16:52:20.168 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 设置行号: 0.00ms (0.0%)
2025-07-15 16:52:20.168 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2182 | 🔧 [性能分析] 最耗时步骤: 填充可见数据 (31.35ms)
2025-07-15 16:52:20.168 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:1038 | 🔧 [紧急修复] 分页处理完成: 第4页, 50条记录
2025-07-15 16:52:20.168 | ERROR    | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3449 | 处理分页变化事件失败: 'PrototypeMainWindow' object has no attribute 'current_table_name'
2025-07-15 16:52:20.168 | INFO     | src.gui.widgets.pagination_widget:set_current_page:330 | 页码切换到: 4
2025-07-15 16:52:31.078 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:964 | 🔧 [紧急修复] 开始分页处理: 第5页, 表名: salary_data_2025_07_active_employees
2025-07-15 16:52:31.078 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:439 | 正在从表 salary_data_2025_07_active_employees 分页获取数据: 第5页, 每页50条
2025-07-15 16:52:31.078 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:462 | 成功从表 salary_data_2025_07_active_employees 获取第5页数据: 50 行，总计1396行
2025-07-15 16:52:31.192 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:133 | 使用已存在的配置文件: state/data/field_mappings.json
2025-07-15 16:52:31.192 | INFO     | src.modules.data_import.config_sync_manager:__init__:70 | 配置同步管理器初始化完成
2025-07-15 16:52:31.192 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:1151 | 🔧 [修复] 没有找到匹配的字段映射
2025-07-15 16:52:31.192 | INFO     | src.gui.prototype.prototype_main_window:_apply_system_field_filtering:1183 | 🔧 [修复] 过滤了 4 个系统字段
2025-07-15 16:52:31.304 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:133 | 使用已存在的配置文件: state/data/field_mappings.json
2025-07-15 16:52:31.304 | INFO     | src.modules.data_import.config_sync_manager:__init__:70 | 配置同步管理器初始化完成
2025-07-15 16:52:31.304 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:1220 | 🔧 [修复] 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-15 16:52:31.304 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:238 | 开始格式化工资表数据 - 表类型: salary_data_2025_07_active_employees
2025-07-15 16:52:31.304 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:251 | 原始数据: 50行, 24列
2025-07-15 16:52:31.304 | INFO     | src.modules.data_storage.salary_data_formatter:_extract_table_type_from_name:335 | 🔧 [表名映射] 通过关键词 'active_employees' 匹配到表类型: active_employees
2025-07-15 16:52:31.304 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:255 | 🔧 [表名映射] 输入表名: salary_data_2025_07_active_employees, 提取的表类型: active_employees
2025-07-15 16:52:31.304 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:259 | 使用表类型配置: active_employees, 配置字段: ['id_fields', 'string_fields', 'integer_fields', 'float_fields', 'special_fields']
2025-07-15 16:52:31.304 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:263 | 🔧 [格式化调试] 开始处理ID字段: ['employee_id', 'employee_type_code', '工号', '人员类别代码']
2025-07-15 16:52:31.304 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:268 | 🔧 [格式化调试] 处理前 employee_id 字段样例: ['20141490.0', '20151523.0', '20151540.0']
2025-07-15 16:52:31.304 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:268 | 🔧 [格式化调试] 处理前 employee_type_code 字段样例: ['17.0', '17.0', '17.0']
2025-07-15 16:52:31.318 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:377 | 🔧 [强化调试] 处理ID字段 employee_id: 原始样例=['20141490.0', '20151523.0', '20151540.0'], 原始类型=object
2025-07-15 16:52:31.318 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:426 | 🔧 [强化调试] 清理后ID字段 employee_id: 样例=['20141490', '20151523', '20151540']
2025-07-15 16:52:31.318 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:443 | 🔧 [强化调试] 最终ID字段 employee_id: 样例=['20141490', '20151523', '20151540']
2025-07-15 16:52:31.318 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:454 | 🔧 [验证成功] ID字段 employee_id 格式化完成，无小数点问题
2025-07-15 16:52:31.318 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:377 | 🔧 [强化调试] 处理ID字段 employee_type_code: 原始样例=['17.0', '17.0', '17.0'], 原始类型=object
2025-07-15 16:52:31.318 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:426 | 🔧 [强化调试] 清理后ID字段 employee_type_code: 样例=['17', '17', '17']
2025-07-15 16:52:31.318 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:443 | 🔧 [强化调试] 最终ID字段 employee_type_code: 样例=['17', '17', '17']
2025-07-15 16:52:31.318 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:454 | 🔧 [验证成功] ID字段 employee_type_code 格式化完成，无小数点问题
2025-07-15 16:52:31.318 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_id_fields:461 | 🔧 [警告] ID字段 工号 不存在于数据框中
2025-07-15 16:52:31.318 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_id_fields:461 | 🔧 [警告] ID字段 人员类别代码 不存在于数据框中
2025-07-15 16:52:31.318 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:274 | 🔧 [格式化调试] 处理后 employee_id 字段样例: ['20141490', '20151523', '20151540']
2025-07-15 16:52:31.318 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:274 | 🔧 [格式化调试] 处理后 employee_type_code 字段样例: ['17', '17', '17']
2025-07-15 16:52:31.318 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:472 | 🔧 [强化调试] 开始格式化字符串字段: ['employee_id', 'employee_name', 'department', 'employee_type_code', 'employee_type', '工号', '姓名', '部门名称', '人员类别代码', '人员类别']
2025-07-15 16:52:31.318 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 employee_id: 原始样例=['20141490', '20151523', '20151540'], 原始类型=object
2025-07-15 16:52:31.318 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:504 | 🔧 [修复] 工号字段 employee_id 已特殊处理，去除小数点格式
2025-07-15 16:52:31.318 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 employee_id: 样例=['20141490', '20151523', '20151540'], 类型=object
2025-07-15 16:52:31.333 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 employee_id 格式化完成，无小数点问题
2025-07-15 16:52:31.333 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 employee_name: 原始样例=['张飞雪', '杨晓松', '柯志强'], 原始类型=object
2025-07-15 16:52:31.333 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 employee_name: 样例=['张飞雪', '杨晓松', '柯志强'], 类型=object
2025-07-15 16:52:31.333 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 employee_name 格式化完成，无小数点问题
2025-07-15 16:52:31.349 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 department: 原始样例=['医药研究院', '医药研究院', '临床医学院'], 原始类型=object
2025-07-15 16:52:31.349 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 department: 样例=['医药研究院', '医药研究院', '临床医学院'], 类型=object
2025-07-15 16:52:31.349 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 department 格式化完成，无小数点问题
2025-07-15 16:52:31.349 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 employee_type_code: 原始样例=['17', '17', '17'], 原始类型=object
2025-07-15 16:52:31.349 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 employee_type_code: 样例=['17', '17', '17'], 类型=object
2025-07-15 16:52:31.349 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 employee_type_code 格式化完成，无小数点问题
2025-07-15 16:52:31.349 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 employee_type: 原始样例=['科研单位人员', '科研单位人员', '科研单位人员'], 原始类型=object
2025-07-15 16:52:31.349 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 employee_type: 样例=['科研单位人员', '科研单位人员', '科研单位人员'], 类型=object
2025-07-15 16:52:31.349 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 employee_type 格式化完成，无小数点问题
2025-07-15 16:52:31.349 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 工号 不存在于数据框中
2025-07-15 16:52:31.349 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 姓名 不存在于数据框中
2025-07-15 16:52:31.349 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 部门名称 不存在于数据框中
2025-07-15 16:52:31.349 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 人员类别代码 不存在于数据框中
2025-07-15 16:52:31.349 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 人员类别 不存在于数据框中
2025-07-15 16:52:31.349 | INFO     | src.modules.data_storage.salary_data_formatter:_format_integer_fields:604 | 🔧 [强化调试] 开始格式化整型字段: []
2025-07-15 16:52:31.349 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:655 | 🔧 [强化调试] 开始格式化浮点型字段: ['position_salary_2025', 'grade_salary_2025', 'allowance', 'balance_allowance', 'basic_performance_2025', 'health_fee', 'transport_allowance', 'property_allowance', 'communication_allowance', 'performance_bonus_2025', 'provident_fund_2025', 'housing_allowance', 'car_allowance', 'supplement', 'advance', 'total_salary', 'pension_insurance', '2025年岗位工资', '2025年薪级工资', '津贴', '结余津贴', '2025年基础性绩效', '卫生费', '交通补贴', '物业补贴', '通讯补贴', '2025年奖励性绩效预发', '2025公积金', '住房补贴', '车补', '补发', '借支', '应发工资', '代扣代存养老保险']
2025-07-15 16:52:31.365 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 position_salary_2025: 原始样例=[2185.0, 3030.0, 2185.0], 原始类型=float64
2025-07-15 16:52:31.365 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 position_salary_2025: 样例=[2185.0, 3030.0, 2185.0], 类型=float64
2025-07-15 16:52:31.365 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 position_salary_2025 格式化完成，保留两位小数
2025-07-15 16:52:31.365 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 grade_salary_2025: 原始样例=[1696.0, 2075.0, 1427.0], 原始类型=float64
2025-07-15 16:52:31.365 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 grade_salary_2025: 样例=[1696.0, 2075.0, 1427.0], 类型=float64
2025-07-15 16:52:31.380 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 grade_salary_2025 格式化完成，保留两位小数
2025-07-15 16:52:31.380 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 allowance: 原始样例=[0.0, 102.0, 0.0], 原始类型=float64
2025-07-15 16:52:31.380 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 allowance: 样例=[0.0, 102.0, 0.0], 类型=float64
2025-07-15 16:52:31.380 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 allowance 格式化完成，保留两位小数
2025-07-15 16:52:31.380 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 balance_allowance: 原始样例=[56.0, 56.0, 56.0], 原始类型=float64
2025-07-15 16:52:31.380 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 balance_allowance: 样例=[56.0, 56.0, 56.0], 类型=float64
2025-07-15 16:52:31.380 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 balance_allowance 格式化完成，保留两位小数
2025-07-15 16:52:31.380 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 basic_performance_2025: 原始样例=[2978.0, 3466.0, 2978.0], 原始类型=float64
2025-07-15 16:52:31.380 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 basic_performance_2025: 样例=[2978.0, 3466.0, 2978.0], 类型=float64
2025-07-15 16:52:31.380 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 basic_performance_2025 格式化完成，保留两位小数
2025-07-15 16:52:31.380 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 health_fee: 原始样例=[20.0, 0.0, 0.0], 原始类型=float64
2025-07-15 16:52:31.380 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 health_fee: 样例=[20.0, 0.0, 0.0], 类型=float64
2025-07-15 16:52:31.380 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 health_fee 格式化完成，保留两位小数
2025-07-15 16:52:31.380 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 transport_allowance: 原始样例=[220.0, 220.0, 220.0], 原始类型=float64
2025-07-15 16:52:31.380 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 transport_allowance: 样例=[220.0, 220.0, 220.0], 类型=float64
2025-07-15 16:52:31.380 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 transport_allowance 格式化完成，保留两位小数
2025-07-15 16:52:31.380 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 property_allowance: 原始样例=[200.0, 240.0, 200.0], 原始类型=float64
2025-07-15 16:52:31.396 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 property_allowance: 样例=[200.0, 240.0, 200.0], 类型=float64
2025-07-15 16:52:31.396 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 property_allowance 格式化完成，保留两位小数
2025-07-15 16:52:31.396 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 communication_allowance: 原始样例=[nan, nan, nan], 原始类型=float64
2025-07-15 16:52:31.396 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 communication_allowance: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-15 16:52:31.396 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 communication_allowance 格式化完成，保留两位小数
2025-07-15 16:52:31.396 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 performance_bonus_2025: 原始样例=[1500.0, 2000.0, 1000.0], 原始类型=float64
2025-07-15 16:52:31.412 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 performance_bonus_2025: 样例=[1500.0, 2000.0, 1000.0], 类型=float64
2025-07-15 16:52:31.412 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 performance_bonus_2025 格式化完成，保留两位小数
2025-07-15 16:52:31.412 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 provident_fund_2025: 原始样例=[1633.0, 2128.0, 1320.0], 原始类型=float64
2025-07-15 16:52:31.412 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 provident_fund_2025: 样例=[1633.0, 2128.0, 1320.0], 类型=float64
2025-07-15 16:52:31.412 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 provident_fund_2025 格式化完成，保留两位小数
2025-07-15 16:52:31.412 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 housing_allowance: 原始样例=[216.33831810748745, 239.46529221291777, 209.3505288151313], 原始类型=float64
2025-07-15 16:52:31.412 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 housing_allowance: 样例=[216.34, 239.47, 209.35], 类型=float64
2025-07-15 16:52:31.412 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 housing_allowance 格式化完成，保留两位小数
2025-07-15 16:52:31.412 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 car_allowance: 原始样例=[None, None, None], 原始类型=object
2025-07-15 16:52:31.412 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 car_allowance: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-15 16:52:31.412 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 car_allowance 格式化完成，保留两位小数
2025-07-15 16:52:31.412 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 supplement: 原始样例=[nan, nan, 352.0], 原始类型=float64
2025-07-15 16:52:31.412 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 supplement: 样例=[0.0, 0.0, 352.0], 类型=float64
2025-07-15 16:52:31.412 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 supplement 格式化完成，保留两位小数
2025-07-15 16:52:31.412 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 advance: 原始样例=[nan, nan, nan], 原始类型=float64
2025-07-15 16:52:31.412 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 advance: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-15 16:52:31.412 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 advance 格式化完成，保留两位小数
2025-07-15 16:52:31.412 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 total_salary: 原始样例=[9071.338318107488, 11428.465292212917, 8627.350528815132], 原始类型=float64
2025-07-15 16:52:31.412 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 total_salary: 样例=[9071.34, 11428.47, 8627.35], 类型=float64
2025-07-15 16:52:31.429 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 total_salary 格式化完成，保留两位小数
2025-07-15 16:52:31.429 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 pension_insurance: 原始样例=[1159.8000000000002, 1478.16, 1131.1200000000001], 原始类型=float64
2025-07-15 16:52:31.429 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 pension_insurance: 样例=[1159.8, 1478.16, 1131.12], 类型=float64
2025-07-15 16:52:31.429 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 pension_insurance 格式化完成，保留两位小数
2025-07-15 16:52:31.429 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 2025年岗位工资 不存在于数据框中
2025-07-15 16:52:31.444 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 2025年薪级工资 不存在于数据框中
2025-07-15 16:52:31.444 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 津贴 不存在于数据框中
2025-07-15 16:52:31.444 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 结余津贴 不存在于数据框中
2025-07-15 16:52:31.444 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 2025年基础性绩效 不存在于数据框中
2025-07-15 16:52:31.444 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 卫生费 不存在于数据框中
2025-07-15 16:52:31.444 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 交通补贴 不存在于数据框中
2025-07-15 16:52:31.444 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 物业补贴 不存在于数据框中
2025-07-15 16:52:31.444 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 通讯补贴 不存在于数据框中
2025-07-15 16:52:31.444 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 2025年奖励性绩效预发 不存在于数据框中
2025-07-15 16:52:31.444 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 2025公积金 不存在于数据框中
2025-07-15 16:52:31.444 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 住房补贴 不存在于数据框中
2025-07-15 16:52:31.444 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 车补 不存在于数据框中
2025-07-15 16:52:31.444 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 补发 不存在于数据框中
2025-07-15 16:52:31.444 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 借支 不存在于数据框中
2025-07-15 16:52:31.444 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 应发工资 不存在于数据框中
2025-07-15 16:52:31.444 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 代扣代存养老保险 不存在于数据框中
2025-07-15 16:52:31.444 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:711 | 🔧 [特殊字段] 开始处理表类型 active_employees 的月份、年份字段
2025-07-15 16:52:31.444 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:731 | 🔧 [特殊字段] 处理月份字段 month: 原始样例=['2025-07', '2025-07', '2025-07'], 原始类型=object
2025-07-15 16:52:31.458 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:774 | 🔧 [特殊字段] 处理后月份字段 month: 样例=['07', '07', '07']
2025-07-15 16:52:31.458 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:785 | 🔧 [验证成功] 月份字段 month 格式化完成，所有值为有效月份格式
2025-07-15 16:52:31.458 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:805 | 🔧 [特殊字段] 处理年份字段 year: 原始样例=[2025, 2025, 2025], 原始类型=int64
2025-07-15 16:52:31.458 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:845 | 🔧 [特殊字段] 处理后年份字段 year: 样例=['2025', '2025', '2025']
2025-07-15 16:52:31.458 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:856 | 🔧 [验证成功] 年份字段 year 格式化完成，所有值为有效年份格式
2025-07-15 16:52:31.458 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:866 | 🔧 [特殊字段] 月份、年份字段处理完成
2025-07-15 16:52:31.474 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:291 | 数据格式化完成: 50行, 24列
2025-07-15 16:52:31.474 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3560 | 最大可见行数已更新: 50 -> 50
2025-07-15 16:52:31.474 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:3613 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-15 16:52:31.474 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第0行数据工号: 20141490
2025-07-15 16:52:31.474 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第1行数据工号: 20151523
2025-07-15 16:52:31.474 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第2行数据工号: 20151540
2025-07-15 16:52:31.474 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第3行数据工号: 20121352
2025-07-15 16:52:31.474 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第4行数据工号: 20191709
2025-07-15 16:52:31.474 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:457 | 🔧 [排序修复] 可见行数据顺序: ['20141490', '20151523', '20151540', '20121352', '20191709']
2025-07-15 16:52:31.474 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:424 | 表格数据已设置: 50 行, 24 列
2025-07-15 16:52:31.505 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2174 | 🔧 [性能分析] 表格数据设置完成: 50 行, 最大可见行数: 50, 总耗时: 31.34ms
2025-07-15 16:52:31.505 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 初始化和状态: 0.00ms (0.0%)
2025-07-15 16:52:31.505 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 字段映射: 0.00ms (0.0%)
2025-07-15 16:52:31.505 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 数据模型更新: 0.00ms (0.0%)
2025-07-15 16:52:31.505 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 表头设置: 0.00ms (0.0%)
2025-07-15 16:52:31.505 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 设置行数: 0.00ms (0.0%)
2025-07-15 16:52:31.505 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 填充可见数据: 31.34ms (100.0%)
2025-07-15 16:52:31.505 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 调整列宽: 0.00ms (0.0%)
2025-07-15 16:52:31.505 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 设置行号: 0.00ms (0.0%)
2025-07-15 16:52:31.505 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2182 | 🔧 [性能分析] 最耗时步骤: 填充可见数据 (31.34ms)
2025-07-15 16:52:31.505 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:1038 | 🔧 [紧急修复] 分页处理完成: 第5页, 50条记录
2025-07-15 16:52:31.521 | ERROR    | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3449 | 处理分页变化事件失败: 'PrototypeMainWindow' object has no attribute 'current_table_name'
2025-07-15 16:52:31.521 | INFO     | src.gui.widgets.pagination_widget:set_current_page:330 | 页码切换到: 5
2025-07-15 16:52:47.958 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:964 | 🔧 [紧急修复] 开始分页处理: 第6页, 表名: salary_data_2025_07_active_employees
2025-07-15 16:52:47.974 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:439 | 正在从表 salary_data_2025_07_active_employees 分页获取数据: 第6页, 每页50条
2025-07-15 16:52:47.974 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:462 | 成功从表 salary_data_2025_07_active_employees 获取第6页数据: 50 行，总计1396行
2025-07-15 16:52:48.085 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:133 | 使用已存在的配置文件: state/data/field_mappings.json
2025-07-15 16:52:48.085 | INFO     | src.modules.data_import.config_sync_manager:__init__:70 | 配置同步管理器初始化完成
2025-07-15 16:52:48.085 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:1151 | 🔧 [修复] 没有找到匹配的字段映射
2025-07-15 16:52:48.085 | INFO     | src.gui.prototype.prototype_main_window:_apply_system_field_filtering:1183 | 🔧 [修复] 过滤了 4 个系统字段
2025-07-15 16:52:48.198 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:133 | 使用已存在的配置文件: state/data/field_mappings.json
2025-07-15 16:52:48.198 | INFO     | src.modules.data_import.config_sync_manager:__init__:70 | 配置同步管理器初始化完成
2025-07-15 16:52:48.198 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:1220 | 🔧 [修复] 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-15 16:52:48.198 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:238 | 开始格式化工资表数据 - 表类型: salary_data_2025_07_active_employees
2025-07-15 16:52:48.198 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:251 | 原始数据: 50行, 24列
2025-07-15 16:52:48.198 | INFO     | src.modules.data_storage.salary_data_formatter:_extract_table_type_from_name:335 | 🔧 [表名映射] 通过关键词 'active_employees' 匹配到表类型: active_employees
2025-07-15 16:52:48.198 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:255 | 🔧 [表名映射] 输入表名: salary_data_2025_07_active_employees, 提取的表类型: active_employees
2025-07-15 16:52:48.198 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:259 | 使用表类型配置: active_employees, 配置字段: ['id_fields', 'string_fields', 'integer_fields', 'float_fields', 'special_fields']
2025-07-15 16:52:48.198 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:263 | 🔧 [格式化调试] 开始处理ID字段: ['employee_id', 'employee_type_code', '工号', '人员类别代码']
2025-07-15 16:52:48.198 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:268 | 🔧 [格式化调试] 处理前 employee_id 字段样例: ['20141496.0', '20010696.0', '20211005.0']
2025-07-15 16:52:48.198 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:268 | 🔧 [格式化调试] 处理前 employee_type_code 字段样例: ['1.0', '1.0', '1.0']
2025-07-15 16:52:48.198 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:377 | 🔧 [强化调试] 处理ID字段 employee_id: 原始样例=['20141496.0', '20010696.0', '20211005.0'], 原始类型=object
2025-07-15 16:52:48.198 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:426 | 🔧 [强化调试] 清理后ID字段 employee_id: 样例=['20141496', '20010696', '20211005']
2025-07-15 16:52:48.198 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:443 | 🔧 [强化调试] 最终ID字段 employee_id: 样例=['20141496', '20010696', '20211005']
2025-07-15 16:52:48.198 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:454 | 🔧 [验证成功] ID字段 employee_id 格式化完成，无小数点问题
2025-07-15 16:52:48.214 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:377 | 🔧 [强化调试] 处理ID字段 employee_type_code: 原始样例=['1.0', '1.0', '1.0'], 原始类型=object
2025-07-15 16:52:48.214 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:426 | 🔧 [强化调试] 清理后ID字段 employee_type_code: 样例=['1', '1', '1']
2025-07-15 16:52:48.214 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:443 | 🔧 [强化调试] 最终ID字段 employee_type_code: 样例=['01', '01', '01']
2025-07-15 16:52:48.214 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:454 | 🔧 [验证成功] ID字段 employee_type_code 格式化完成，无小数点问题
2025-07-15 16:52:48.214 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_id_fields:461 | 🔧 [警告] ID字段 工号 不存在于数据框中
2025-07-15 16:52:48.214 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_id_fields:461 | 🔧 [警告] ID字段 人员类别代码 不存在于数据框中
2025-07-15 16:52:48.214 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:274 | 🔧 [格式化调试] 处理后 employee_id 字段样例: ['20141496', '20010696', '20211005']
2025-07-15 16:52:48.214 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:274 | 🔧 [格式化调试] 处理后 employee_type_code 字段样例: ['01', '01', '01']
2025-07-15 16:52:48.229 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:472 | 🔧 [强化调试] 开始格式化字符串字段: ['employee_id', 'employee_name', 'department', 'employee_type_code', 'employee_type', '工号', '姓名', '部门名称', '人员类别代码', '人员类别']
2025-07-15 16:52:48.229 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 employee_id: 原始样例=['20141496', '20010696', '20211005'], 原始类型=object
2025-07-15 16:52:48.229 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:504 | 🔧 [修复] 工号字段 employee_id 已特殊处理，去除小数点格式
2025-07-15 16:52:48.229 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 employee_id: 样例=['20141496', '20010696', '20211005'], 类型=object
2025-07-15 16:52:48.229 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 employee_id 格式化完成，无小数点问题
2025-07-15 16:52:48.229 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 employee_name: 原始样例=['黄琦', '刘小玲', '陈林枫'], 原始类型=object
2025-07-15 16:52:48.229 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 employee_name: 样例=['黄琦', '刘小玲', '陈林枫'], 类型=object
2025-07-15 16:52:48.229 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 employee_name 格式化完成，无小数点问题
2025-07-15 16:52:48.229 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 department: 原始样例=['药学院', '药学院', '药学院'], 原始类型=object
2025-07-15 16:52:48.229 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 department: 样例=['药学院', '药学院', '药学院'], 类型=object
2025-07-15 16:52:48.229 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 department 格式化完成，无小数点问题
2025-07-15 16:52:48.229 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 employee_type_code: 原始样例=['01', '01', '01'], 原始类型=object
2025-07-15 16:52:48.229 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 employee_type_code: 样例=['01', '01', '01'], 类型=object
2025-07-15 16:52:48.229 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 employee_type_code 格式化完成，无小数点问题
2025-07-15 16:52:48.229 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 employee_type: 原始样例=['教学院其它人员', '教学院其它人员', '教学单位专技人员'], 原始类型=object
2025-07-15 16:52:48.260 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 employee_type: 样例=['教学院其它人员', '教学院其它人员', '教学单位专技人员'], 类型=object
2025-07-15 16:52:48.260 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 employee_type 格式化完成，无小数点问题
2025-07-15 16:52:48.260 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 工号 不存在于数据框中
2025-07-15 16:52:48.260 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 姓名 不存在于数据框中
2025-07-15 16:52:48.260 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 部门名称 不存在于数据框中
2025-07-15 16:52:48.260 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 人员类别代码 不存在于数据框中
2025-07-15 16:52:48.260 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 人员类别 不存在于数据框中
2025-07-15 16:52:48.260 | INFO     | src.modules.data_storage.salary_data_formatter:_format_integer_fields:604 | 🔧 [强化调试] 开始格式化整型字段: []
2025-07-15 16:52:48.260 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:655 | 🔧 [强化调试] 开始格式化浮点型字段: ['position_salary_2025', 'grade_salary_2025', 'allowance', 'balance_allowance', 'basic_performance_2025', 'health_fee', 'transport_allowance', 'property_allowance', 'communication_allowance', 'performance_bonus_2025', 'provident_fund_2025', 'housing_allowance', 'car_allowance', 'supplement', 'advance', 'total_salary', 'pension_insurance', '2025年岗位工资', '2025年薪级工资', '津贴', '结余津贴', '2025年基础性绩效', '卫生费', '交通补贴', '物业补贴', '通讯补贴', '2025年奖励性绩效预发', '2025公积金', '住房补贴', '车补', '补发', '借支', '应发工资', '代扣代存养老保险']
2025-07-15 16:52:48.260 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 position_salary_2025: 原始样例=[1925.0, 2185.0, 2185.0], 原始类型=float64
2025-07-15 16:52:48.260 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 position_salary_2025: 样例=[1925.0, 2185.0, 2185.0], 类型=float64
2025-07-15 16:52:48.260 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 position_salary_2025 格式化完成，保留两位小数
2025-07-15 16:52:48.260 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 grade_salary_2025: 原始样例=[2075.0, 2275.0, 1251.0], 原始类型=float64
2025-07-15 16:52:48.260 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 grade_salary_2025: 样例=[2075.0, 2275.0, 1251.0], 类型=float64
2025-07-15 16:52:48.260 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 grade_salary_2025 格式化完成，保留两位小数
2025-07-15 16:52:48.260 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 allowance: 原始样例=[102.0, 0.0, 0.0], 原始类型=float64
2025-07-15 16:52:48.260 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 allowance: 样例=[102.0, 0.0, 0.0], 类型=float64
2025-07-15 16:52:48.260 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 allowance 格式化完成，保留两位小数
2025-07-15 16:52:48.276 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 balance_allowance: 原始样例=[56.0, 56.0, 56.0], 原始类型=float64
2025-07-15 16:52:48.276 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 balance_allowance: 样例=[56.0, 56.0, 56.0], 类型=float64
2025-07-15 16:52:48.276 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 balance_allowance 格式化完成，保留两位小数
2025-07-15 16:52:48.276 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 basic_performance_2025: 原始样例=[2696.0, 2978.0, 2978.0], 原始类型=float64
2025-07-15 16:52:48.276 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 basic_performance_2025: 样例=[2696.0, 2978.0, 2978.0], 类型=float64
2025-07-15 16:52:48.276 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 basic_performance_2025 格式化完成，保留两位小数
2025-07-15 16:52:48.276 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 health_fee: 原始样例=[0.0, 20.0, 0.0], 原始类型=float64
2025-07-15 16:52:48.292 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 health_fee: 样例=[0.0, 20.0, 0.0], 类型=float64
2025-07-15 16:52:48.292 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 health_fee 格式化完成，保留两位小数
2025-07-15 16:52:48.292 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 transport_allowance: 原始样例=[220.0, 220.0, 220.0], 原始类型=float64
2025-07-15 16:52:48.292 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 transport_allowance: 样例=[220.0, 220.0, 220.0], 类型=float64
2025-07-15 16:52:48.292 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 transport_allowance 格式化完成，保留两位小数
2025-07-15 16:52:48.292 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 property_allowance: 原始样例=[200.0, 200.0, 200.0], 原始类型=float64
2025-07-15 16:52:48.292 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 property_allowance: 样例=[200.0, 200.0, 200.0], 类型=float64
2025-07-15 16:52:48.292 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 property_allowance 格式化完成，保留两位小数
2025-07-15 16:52:48.292 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 communication_allowance: 原始样例=[nan, nan, nan], 原始类型=float64
2025-07-15 16:52:48.292 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 communication_allowance: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-15 16:52:48.292 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 communication_allowance 格式化完成，保留两位小数
2025-07-15 16:52:48.292 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 performance_bonus_2025: 原始样例=[1000.0, 1000.0, 1000.0], 原始类型=float64
2025-07-15 16:52:48.292 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 performance_bonus_2025: 样例=[1000.0, 1000.0, 1000.0], 类型=float64
2025-07-15 16:52:48.292 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 performance_bonus_2025 格式化完成，保留两位小数
2025-07-15 16:52:48.292 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 provident_fund_2025: 原始样例=[2510.0, 1452.0, 2187.0], 原始类型=float64
2025-07-15 16:52:48.292 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 provident_fund_2025: 样例=[2510.0, 1452.0, 2187.0], 类型=float64
2025-07-15 16:52:48.292 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 provident_fund_2025 格式化完成，保留两位小数
2025-07-15 16:52:48.307 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 housing_allowance: 原始样例=[213.31467349802054, 143.0, 189.0], 原始类型=float64
2025-07-15 16:52:48.307 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 housing_allowance: 样例=[213.31, 143.0, 189.0], 类型=float64
2025-07-15 16:52:48.307 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 housing_allowance 格式化完成，保留两位小数
2025-07-15 16:52:48.307 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 car_allowance: 原始样例=[nan, nan, nan], 原始类型=float64
2025-07-15 16:52:48.307 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 car_allowance: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-15 16:52:48.307 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 car_allowance 格式化完成，保留两位小数
2025-07-15 16:52:48.323 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 supplement: 原始样例=[None, None, None], 原始类型=object
2025-07-15 16:52:48.323 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 supplement: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-15 16:52:48.323 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 supplement 格式化完成，保留两位小数
2025-07-15 16:52:48.323 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 advance: 原始样例=[None, None, None], 原始类型=object
2025-07-15 16:52:48.323 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 advance: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-15 16:52:48.323 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 advance 格式化完成，保留两位小数
2025-07-15 16:52:48.323 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 total_salary: 原始样例=[8487.314673498022, 9077.0, 8079.0], 原始类型=float64
2025-07-15 16:52:48.323 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 total_salary: 样例=[8487.31, 9077.0, 8079.0], 类型=float64
2025-07-15 16:52:48.323 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 total_salary 格式化完成，保留两位小数
2025-07-15 16:52:48.323 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 pension_insurance: 原始样例=[1026.9199999999998, 1225.1999999999998, 944.1099999999999], 原始类型=float64
2025-07-15 16:52:48.323 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 pension_insurance: 样例=[1026.92, 1225.2, 944.11], 类型=float64
2025-07-15 16:52:48.323 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 pension_insurance 格式化完成，保留两位小数
2025-07-15 16:52:48.323 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 2025年岗位工资 不存在于数据框中
2025-07-15 16:52:48.323 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 2025年薪级工资 不存在于数据框中
2025-07-15 16:52:48.323 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 津贴 不存在于数据框中
2025-07-15 16:52:48.323 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 结余津贴 不存在于数据框中
2025-07-15 16:52:48.323 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 2025年基础性绩效 不存在于数据框中
2025-07-15 16:52:48.323 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 卫生费 不存在于数据框中
2025-07-15 16:52:48.323 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 交通补贴 不存在于数据框中
2025-07-15 16:52:48.323 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 物业补贴 不存在于数据框中
2025-07-15 16:52:48.323 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 通讯补贴 不存在于数据框中
2025-07-15 16:52:48.323 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 2025年奖励性绩效预发 不存在于数据框中
2025-07-15 16:52:48.323 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 2025公积金 不存在于数据框中
2025-07-15 16:52:48.323 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 住房补贴 不存在于数据框中
2025-07-15 16:52:48.323 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 车补 不存在于数据框中
2025-07-15 16:52:48.323 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 补发 不存在于数据框中
2025-07-15 16:52:48.323 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 借支 不存在于数据框中
2025-07-15 16:52:48.323 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 应发工资 不存在于数据框中
2025-07-15 16:52:48.323 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 代扣代存养老保险 不存在于数据框中
2025-07-15 16:52:48.323 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:711 | 🔧 [特殊字段] 开始处理表类型 active_employees 的月份、年份字段
2025-07-15 16:52:48.323 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:731 | 🔧 [特殊字段] 处理月份字段 month: 原始样例=['2025-07', '2025-07', '2025-07'], 原始类型=object
2025-07-15 16:52:48.323 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:774 | 🔧 [特殊字段] 处理后月份字段 month: 样例=['07', '07', '07']
2025-07-15 16:52:48.323 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:785 | 🔧 [验证成功] 月份字段 month 格式化完成，所有值为有效月份格式
2025-07-15 16:52:48.323 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:805 | 🔧 [特殊字段] 处理年份字段 year: 原始样例=[2025, 2025, 2025], 原始类型=int64
2025-07-15 16:52:48.323 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:845 | 🔧 [特殊字段] 处理后年份字段 year: 样例=['2025', '2025', '2025']
2025-07-15 16:52:48.339 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:856 | 🔧 [验证成功] 年份字段 year 格式化完成，所有值为有效年份格式
2025-07-15 16:52:48.339 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:866 | 🔧 [特殊字段] 月份、年份字段处理完成
2025-07-15 16:52:48.339 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:291 | 数据格式化完成: 50行, 24列
2025-07-15 16:52:48.355 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3560 | 最大可见行数已更新: 50 -> 50
2025-07-15 16:52:48.355 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:3613 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-15 16:52:48.355 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第0行数据工号: 20141496
2025-07-15 16:52:48.355 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第1行数据工号: 20010696
2025-07-15 16:52:48.355 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第2行数据工号: 20211005
2025-07-15 16:52:48.355 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第3行数据工号: 20211035
2025-07-15 16:52:48.355 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第4行数据工号: 20211034
2025-07-15 16:52:48.355 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:457 | 🔧 [排序修复] 可见行数据顺序: ['20141496', '20010696', '20211005', '20211035', '20211034']
2025-07-15 16:52:48.355 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:424 | 表格数据已设置: 50 行, 24 列
2025-07-15 16:52:48.370 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2174 | 🔧 [性能分析] 表格数据设置完成: 50 行, 最大可见行数: 50, 总耗时: 14.71ms
2025-07-15 16:52:48.370 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 初始化和状态: 0.00ms (0.0%)
2025-07-15 16:52:48.370 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 字段映射: 0.00ms (0.0%)
2025-07-15 16:52:48.370 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 数据模型更新: 0.00ms (0.0%)
2025-07-15 16:52:48.370 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 表头设置: 0.00ms (0.0%)
2025-07-15 16:52:48.370 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 设置行数: 0.00ms (0.0%)
2025-07-15 16:52:48.370 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 填充可见数据: 14.71ms (100.0%)
2025-07-15 16:52:48.370 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 调整列宽: 0.00ms (0.0%)
2025-07-15 16:52:48.370 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 设置行号: 0.00ms (0.0%)
2025-07-15 16:52:48.370 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2182 | 🔧 [性能分析] 最耗时步骤: 填充可见数据 (14.71ms)
2025-07-15 16:52:48.370 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:1038 | 🔧 [紧急修复] 分页处理完成: 第6页, 50条记录
2025-07-15 16:52:48.370 | ERROR    | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3449 | 处理分页变化事件失败: 'PrototypeMainWindow' object has no attribute 'current_table_name'
2025-07-15 16:52:48.370 | INFO     | src.gui.widgets.pagination_widget:set_current_page:330 | 页码切换到: 6
2025-07-15 17:01:10.882 | INFO     | src.gui.prototype.prototype_main_window:toggle_global_sort:3341 | 🔧 [全局排序] 主窗口全局排序开关: 禁用
2025-07-15 17:01:10.882 | INFO     | src.gui.prototype.prototype_main_window:toggle_global_sort:944 | 🔧 [全局排序] 全局排序已禁用
2025-07-15 17:01:10.882 | WARNING  | src.gui.prototype.prototype_main_window:_reload_current_page_with_sort:904 | 🔧 [修复] 所有方法都失败，使用默认页码1
2025-07-15 17:01:10.882 | INFO     | src.gui.prototype.prototype_main_window:_reload_current_page_with_sort:906 | 🔧 [全局排序] 重新加载页面: 1页, 排序列: 0
2025-07-15 17:01:10.882 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:487 | 正在从表 salary_data_2025_07_active_employees 分页获取数据（支持排序）: 第1页, 每页50条, 排序=0列
2025-07-15 17:01:10.882 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:547 | 🔧 [排序调试] 执行的完整SQL查询: SELECT * FROM "salary_data_2025_07_active_employees" LIMIT 50 OFFSET 0
2025-07-15 17:01:10.903 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:561 | 成功从表 salary_data_2025_07_active_employees 获取第1页数据（含排序）: 50 行，总计1396行
2025-07-15 17:01:11.008 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:133 | 使用已存在的配置文件: state/data/field_mappings.json
2025-07-15 17:01:11.008 | INFO     | src.modules.data_import.config_sync_manager:__init__:70 | 配置同步管理器初始化完成
2025-07-15 17:01:11.008 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:1151 | 🔧 [修复] 没有找到匹配的字段映射
2025-07-15 17:01:11.008 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3560 | 最大可见行数已更新: 50 -> 50
2025-07-15 17:01:11.008 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:3613 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-15 17:01:11.008 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第0行数据工号: 19990089.0
2025-07-15 17:01:11.008 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第1行数据工号: 20161565.0
2025-07-15 17:01:11.008 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第2行数据工号: 20191782.0
2025-07-15 17:01:11.008 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第3行数据工号: 20151515.0
2025-07-15 17:01:11.008 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第4行数据工号: 20181640.0
2025-07-15 17:01:11.008 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:457 | 🔧 [排序修复] 可见行数据顺序: ['19990089.0', '20161565.0', '20191782.0', '20151515.0', '20181640.0']
2025-07-15 17:01:11.008 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:424 | 表格数据已设置: 50 行, 28 列
2025-07-15 17:01:11.040 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2174 | 🔧 [性能分析] 表格数据设置完成: 50 行, 最大可见行数: 50, 总耗时: 31.37ms
2025-07-15 17:01:11.040 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 初始化和状态: 0.00ms (0.0%)
2025-07-15 17:01:11.040 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 字段映射: 0.00ms (0.0%)
2025-07-15 17:01:11.040 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 数据模型更新: 0.00ms (0.0%)
2025-07-15 17:01:11.040 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 表头设置: 0.00ms (0.0%)
2025-07-15 17:01:11.040 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 设置行数: 0.00ms (0.0%)
2025-07-15 17:01:11.040 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 填充可见数据: 31.37ms (100.0%)
2025-07-15 17:01:11.040 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 调整列宽: 0.00ms (0.0%)
2025-07-15 17:01:11.040 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 设置行号: 0.00ms (0.0%)
2025-07-15 17:01:11.040 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2182 | 🔧 [性能分析] 最耗时步骤: 填充可见数据 (31.37ms)
2025-07-15 17:01:11.040 | INFO     | src.gui.prototype.prototype_main_window:_reload_current_page_with_sort:936 | 🔧 [全局排序] 页面重新加载完成: 50行数据
2025-07-15 17:01:22.540 | INFO     | src.gui.prototype.prototype_main_window:_on_refresh_data:553 | 刷新数据功能被触发，将强制刷新导航面板。
2025-07-15 17:01:22.556 | WARNING  | src.gui.prototype.prototype_main_window:_on_refresh_data:568 | 导航面板不支持强制刷新。
2025-07-15 17:01:33.718 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表']
2025-07-15 17:01:33.719 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:4645 | 导航变化: 工资表 > 2025年
2025-07-15 17:01:33.723 | INFO     | src.gui.widgets.pagination_widget:reset:354 | 分页状态已重置
2025-07-15 17:01:33.778 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:377 | 已清理所有缓存
2025-07-15 17:01:33.780 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:5571 | 已注册 2 个表格到表头管理器
2025-07-15 17:01:33.781 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:509 | 开始增强版表头重影检测，共 2 个表格
2025-07-15 17:01:33.781 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:571 | 增强版表头重影检测完成，耗时 1.04ms
2025-07-15 17:01:33.781 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:572 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-07-15 17:01:33.782 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:5533 | 导航切换时的完全表格重置完成
2025-07-15 17:01:33.782 | INFO     | src.gui.prototype.prototype_main_window:clear_field_processing_cache:4564 | 🔧 [缓存清理] 清理了所有 1 个字段处理缓存条目
2025-07-15 17:01:33.783 | INFO     | src.gui.widgets.pagination_widget:reset:354 | 分页状态已重置
2025-07-15 17:01:33.783 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:5185 | 🔧 [表名生成] 导航路径不完整(2层): ['工资表', '2025年']
2025-07-15 17:01:33.786 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:881 | 找到 4 个匹配类型 'salary_data' 的表
2025-07-15 17:01:33.786 | WARNING  | src.gui.prototype.prototype_main_window:_on_navigation_changed:4693 | 🔧 [表名修复] 无法从路径生成表名: ['工资表', '2025年']
2025-07-15 17:01:33.786 | WARNING  | src.gui.prototype.prototype_main_window:set_data:595 | 尝试设置空数据，将显示提示信息。
2025-07-15 17:01:33.786 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:3589 | 数据量为0，不调整最大可见行数，保持当前值: 50
2025-07-15 17:01:33.787 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:424 | 表格数据已设置: 0 行, 7 列
2025-07-15 17:01:33.788 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2174 | 🔧 [性能分析] 表格数据设置完成: 0 行, 最大可见行数: 50, 总耗时: 2.06ms
2025-07-15 17:01:33.789 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 初始化和状态: 1.03ms (50.1%)
2025-07-15 17:01:33.796 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 字段映射: 0.00ms (0.0%)
2025-07-15 17:01:33.796 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 数据模型更新: 0.00ms (0.0%)
2025-07-15 17:01:33.798 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 表头设置: 1.03ms (49.9%)
2025-07-15 17:01:33.798 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 设置行数: 0.00ms (0.0%)
2025-07-15 17:01:33.799 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 填充可见数据: 0.00ms (0.0%)
2025-07-15 17:01:33.799 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 调整列宽: 0.00ms (0.0%)
2025-07-15 17:01:33.799 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 设置行号: 0.00ms (0.0%)
2025-07-15 17:01:33.800 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2182 | 🔧 [性能分析] 最耗时步骤: 初始化和状态 (1.03ms)
2025-07-15 17:01:33.800 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1693 | 表格已初始化为空白状态，等待用户导入数据
2025-07-15 17:01:33.801 | INFO     | src.gui.widgets.pagination_widget:reset:354 | 分页状态已重置
2025-07-15 17:01:33.801 | WARNING  | src.gui.prototype.prototype_main_window:set_data:595 | 尝试设置空数据，将显示提示信息。
2025-07-15 17:01:33.801 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:3589 | 数据量为0，不调整最大可见行数，保持当前值: 50
2025-07-15 17:01:33.802 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:424 | 表格数据已设置: 0 行, 7 列
2025-07-15 17:01:33.802 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2174 | 🔧 [性能分析] 表格数据设置完成: 0 行, 最大可见行数: 50, 总耗时: 1.06ms
2025-07-15 17:01:33.803 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 初始化和状态: 1.06ms (100.0%)
2025-07-15 17:01:33.803 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 字段映射: 0.00ms (0.0%)
2025-07-15 17:01:33.803 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 数据模型更新: 0.00ms (0.0%)
2025-07-15 17:01:33.804 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 表头设置: 0.00ms (0.0%)
2025-07-15 17:01:33.804 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 设置行数: 0.00ms (0.0%)
2025-07-15 17:01:33.804 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 填充可见数据: 0.00ms (0.0%)
2025-07-15 17:01:33.805 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 调整列宽: 0.00ms (0.0%)
2025-07-15 17:01:33.814 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 设置行号: 0.00ms (0.0%)
2025-07-15 17:01:33.814 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2182 | 🔧 [性能分析] 最耗时步骤: 初始化和状态 (1.06ms)
2025-07-15 17:01:33.814 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1693 | 表格已初始化为空白状态，等待用户导入数据
2025-07-15 17:01:33.815 | INFO     | src.gui.widgets.pagination_widget:reset:354 | 分页状态已重置
2025-07-15 17:01:33.815 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2025年
2025-07-15 17:01:34.766 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2025年 > 7月', '工资表']
2025-07-15 17:01:34.767 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:4645 | 导航变化: 工资表 > 2025年 > 7月
2025-07-15 17:01:34.769 | INFO     | src.gui.widgets.pagination_widget:reset:354 | 分页状态已重置
2025-07-15 17:01:34.803 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:377 | 已清理所有缓存
2025-07-15 17:01:34.805 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:5571 | 已注册 2 个表格到表头管理器
2025-07-15 17:01:34.806 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:509 | 开始增强版表头重影检测，共 2 个表格
2025-07-15 17:01:34.807 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:571 | 增强版表头重影检测完成，耗时 1.07ms
2025-07-15 17:01:34.807 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:572 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-07-15 17:01:34.808 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:5533 | 导航切换时的完全表格重置完成
2025-07-15 17:01:34.808 | INFO     | src.gui.prototype.prototype_main_window:clear_field_processing_cache:4564 | 🔧 [缓存清理] 清理了所有 0 个字段处理缓存条目
2025-07-15 17:01:34.809 | INFO     | src.gui.widgets.pagination_widget:reset:354 | 分页状态已重置
2025-07-15 17:01:34.809 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:5185 | 🔧 [表名生成] 导航路径不完整(3层): ['工资表', '2025年', '7月']
2025-07-15 17:01:34.811 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:881 | 找到 4 个匹配类型 'salary_data' 的表
2025-07-15 17:01:34.812 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:881 | 找到 4 个匹配类型 'salary_data' 的表
2025-07-15 17:01:34.813 | WARNING  | src.gui.prototype.prototype_main_window:_on_navigation_changed:4693 | 🔧 [表名修复] 无法从路径生成表名: ['工资表', '2025年', '7月']
2025-07-15 17:01:34.813 | WARNING  | src.gui.prototype.prototype_main_window:set_data:595 | 尝试设置空数据，将显示提示信息。
2025-07-15 17:01:34.813 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:3589 | 数据量为0，不调整最大可见行数，保持当前值: 50
2025-07-15 17:01:34.814 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:424 | 表格数据已设置: 0 行, 7 列
2025-07-15 17:01:34.822 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2174 | 🔧 [性能分析] 表格数据设置完成: 0 行, 最大可见行数: 50, 总耗时: 8.18ms
2025-07-15 17:01:34.822 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 初始化和状态: 1.04ms (12.7%)
2025-07-15 17:01:34.822 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 字段映射: 0.00ms (0.0%)
2025-07-15 17:01:34.823 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 数据模型更新: 6.10ms (74.5%)
2025-07-15 17:01:34.823 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 表头设置: 1.04ms (12.8%)
2025-07-15 17:01:34.823 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 设置行数: 0.00ms (0.0%)
2025-07-15 17:01:34.823 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 填充可见数据: 0.00ms (0.0%)
2025-07-15 17:01:34.824 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 调整列宽: 0.00ms (0.0%)
2025-07-15 17:01:34.824 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 设置行号: 0.00ms (0.0%)
2025-07-15 17:01:34.824 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2182 | 🔧 [性能分析] 最耗时步骤: 数据模型更新 (6.10ms)
2025-07-15 17:01:34.825 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1693 | 表格已初始化为空白状态，等待用户导入数据
2025-07-15 17:01:34.825 | INFO     | src.gui.widgets.pagination_widget:reset:354 | 分页状态已重置
2025-07-15 17:01:34.826 | WARNING  | src.gui.prototype.prototype_main_window:set_data:595 | 尝试设置空数据，将显示提示信息。
2025-07-15 17:01:34.826 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:3589 | 数据量为0，不调整最大可见行数，保持当前值: 50
2025-07-15 17:01:34.826 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:424 | 表格数据已设置: 0 行, 7 列
2025-07-15 17:01:34.827 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2174 | 🔧 [性能分析] 表格数据设置完成: 0 行, 最大可见行数: 50, 总耗时: 1.04ms
2025-07-15 17:01:34.827 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 初始化和状态: 0.00ms (0.0%)
2025-07-15 17:01:34.828 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 字段映射: 0.00ms (0.0%)
2025-07-15 17:01:34.828 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 数据模型更新: 1.04ms (100.0%)
2025-07-15 17:01:34.828 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 表头设置: 0.00ms (0.0%)
2025-07-15 17:01:34.828 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 设置行数: 0.00ms (0.0%)
2025-07-15 17:01:34.838 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 填充可见数据: 0.00ms (0.0%)
2025-07-15 17:01:34.838 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 调整列宽: 0.00ms (0.0%)
2025-07-15 17:01:34.838 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 设置行号: 0.00ms (0.0%)
2025-07-15 17:01:34.839 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2182 | 🔧 [性能分析] 最耗时步骤: 数据模型更新 (1.04ms)
2025-07-15 17:01:34.839 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1693 | 表格已初始化为空白状态，等待用户导入数据
2025-07-15 17:01:34.840 | INFO     | src.gui.widgets.pagination_widget:reset:354 | 分页状态已重置
2025-07-15 17:01:34.841 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2025年 > 7月
2025-07-15 17:01:36.141 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2025年 > 7月', '工资表 > 2025年 > 7月 > 全部在职人员', '工资表']
2025-07-15 17:01:36.142 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:4645 | 导航变化: 工资表 > 2025年 > 7月 > 全部在职人员
2025-07-15 17:01:36.145 | INFO     | src.gui.widgets.pagination_widget:reset:354 | 分页状态已重置
2025-07-15 17:01:36.187 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:377 | 已清理所有缓存
2025-07-15 17:01:36.188 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:5571 | 已注册 2 个表格到表头管理器
2025-07-15 17:01:36.190 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:509 | 开始增强版表头重影检测，共 2 个表格
2025-07-15 17:01:36.190 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:571 | 增强版表头重影检测完成，耗时 0.00ms
2025-07-15 17:01:36.190 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:572 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-07-15 17:01:36.191 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:5533 | 导航切换时的完全表格重置完成
2025-07-15 17:01:36.191 | INFO     | src.gui.prototype.prototype_main_window:clear_field_processing_cache:4564 | 🔧 [缓存清理] 清理了所有 0 个字段处理缓存条目
2025-07-15 17:01:36.192 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:5254 | 🔧 [表名生成] 从完整路径生成表名: ['工资表', '2025年', '7月', '全部在职人员'] -> salary_data_2025_07_active_employees
2025-07-15 17:01:36.192 | INFO     | src.gui.widgets.pagination_widget:reset:354 | 分页状态已重置
2025-07-15 17:01:36.193 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:5254 | 🔧 [表名生成] 从完整路径生成表名: ['工资表', '2025年', '7月', '全部在职人员'] -> salary_data_2025_07_active_employees
2025-07-15 17:01:36.193 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:360 | 已清理表 salary_data_2025_07_active_employees 的缓存
2025-07-15 17:01:36.193 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:4716 | 🆕 使用新架构加载数据: salary_data_2025_07_active_employees（通过事件系统）
2025-07-15 17:01:36.194 | INFO     | src.services.table_data_service:load_table_data:291 | 加载表格数据: salary_data_2025_07_active_employees, 页码: 1
2025-07-15 17:01:36.194 | INFO     | src.core.unified_data_request_manager:request_table_data:177 | 开始处理数据请求: salary_data_2025_07_active_employees, 类型: initial_load
2025-07-15 17:01:36.195 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:487 | 正在从表 salary_data_2025_07_active_employees 分页获取数据（支持排序）: 第1页, 每页50条, 排序=0列
2025-07-15 17:01:36.196 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:547 | 🔧 [排序调试] 执行的完整SQL查询: SELECT * FROM "salary_data_2025_07_active_employees" LIMIT 50 OFFSET 0
2025-07-15 17:01:36.198 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:561 | 成功从表 salary_data_2025_07_active_employees 获取第1页数据（含排序）: 50 行，总计1396行
2025-07-15 17:01:36.199 | INFO     | src.core.unified_data_request_manager:request_table_data:226 | 数据请求处理完成: 28字段, 50行, 耗时4.6ms
2025-07-15 17:01:36.208 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:2982 | 🆕 接收到新架构数据更新事件: salary_data_2025_07_active_employees
2025-07-15 17:01:36.208 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3007 | 数据内容: 50行 x 28列
2025-07-15 17:01:36.208 | INFO     | src.gui.prototype.prototype_main_window:set_data:635 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-07-15 17:01:36.312 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:133 | 使用已存在的配置文件: state/data/field_mappings.json
2025-07-15 17:01:36.313 | INFO     | src.modules.data_import.config_sync_manager:__init__:70 | 配置同步管理器初始化完成
2025-07-15 17:01:36.315 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:1151 | 🔧 [修复] 没有找到匹配的字段映射
2025-07-15 17:01:36.316 | INFO     | src.gui.prototype.prototype_main_window:_apply_system_field_filtering:1183 | 🔧 [修复] 过滤了 4 个系统字段
2025-07-15 17:01:36.420 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:133 | 使用已存在的配置文件: state/data/field_mappings.json
2025-07-15 17:01:36.422 | INFO     | src.modules.data_import.config_sync_manager:__init__:70 | 配置同步管理器初始化完成
2025-07-15 17:01:36.423 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:1220 | 🔧 [修复] 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-15 17:01:36.459 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3560 | 最大可见行数已更新: 50 -> 50
2025-07-15 17:01:36.459 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:3613 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-15 17:01:36.463 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2237 | 🔧 [修复标识] 字段映射生效，样例: ['employee_id', 'employee_name', 'department'] -> ['工号', '姓名', '部门名称']
2025-07-15 17:01:36.463 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第0行数据工号: 19990089.0
2025-07-15 17:01:36.464 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第1行数据工号: 20161565.0
2025-07-15 17:01:36.464 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第2行数据工号: 20191782.0
2025-07-15 17:01:36.464 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第3行数据工号: 20151515.0
2025-07-15 17:01:36.465 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第4行数据工号: 20181640.0
2025-07-15 17:01:36.465 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:457 | 🔧 [排序修复] 可见行数据顺序: ['19990089.0', '20161565.0', '20191782.0', '20151515.0', '20181640.0']
2025-07-15 17:01:36.465 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:424 | 表格数据已设置: 50 行, 24 列
2025-07-15 17:01:36.468 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2363 | 🔧 [表格调试] 开始格式化ID字段: 列=工号, 原值=19990089.0, 类型=<class 'str'>
2025-07-15 17:01:36.468 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2370 | 🔧 [表格调试] ID字段格式化完成: 列=工号, 原值=19990089.0, 格式化后=19990089.0
2025-07-15 17:01:36.469 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2363 | 🔧 [表格调试] 开始格式化ID字段: 列=人员类别代码, 原值=1.0, 类型=<class 'str'>
2025-07-15 17:01:36.469 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2370 | 🔧 [表格调试] ID字段格式化完成: 列=人员类别代码, 原值=1.0, 格式化后=1.0
2025-07-15 17:01:36.497 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2174 | 🔧 [性能分析] 表格数据设置完成: 50 行, 最大可见行数: 50, 总耗时: 69.77ms
2025-07-15 17:01:36.498 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 初始化和状态: 35.40ms (50.7%)
2025-07-15 17:01:36.501 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 字段映射: 1.01ms (1.4%)
2025-07-15 17:01:36.501 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 数据模型更新: 3.02ms (4.3%)
2025-07-15 17:01:36.501 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 表头设置: 2.01ms (2.9%)
2025-07-15 17:01:36.502 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 设置行数: 0.00ms (0.0%)
2025-07-15 17:01:36.502 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 填充可见数据: 28.33ms (40.6%)
2025-07-15 17:01:36.502 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 调整列宽: 0.00ms (0.0%)
2025-07-15 17:01:36.503 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 设置行号: 0.00ms (0.0%)
2025-07-15 17:01:36.503 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2182 | 🔧 [性能分析] 最耗时步骤: 初始化和状态 (35.40ms)
2025-07-15 17:01:36.504 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 50
2025-07-15 17:01:36.505 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-15 17:01:36.505 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3033 | 🔧 [分页修复] 数据更新事件设置总记录数: 1396
2025-07-15 17:01:36.505 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3044 | 🔧 [分页修复] 分页状态: 当前页=1, 总页数=28, 总记录数=1396
2025-07-15 17:01:36.506 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3050 | 🔧 [分页修复] 按钮可用性: 下一页=True, 上一页=False
2025-07-15 17:01:36.506 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:4721 | 🆕 新架构数据加载请求已发送，等待事件系统处理
2025-07-15 17:01:36.506 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2025年 > 7月 > 全部在职人员
2025-07-15 17:01:43.999 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_changed:5401 | 排序变化: 1 列
2025-07-15 17:01:43.999 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_changed:5413 | 🔧 [调试] 数据重载状态正常，继续处理排序
2025-07-15 17:01:43.999 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_changed:5417 | 🔧 [调试] 开始更新排序指示器
2025-07-15 17:01:43.999 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_sort_indicators:5797 | 🔧 [排序指示器修复] 设置排序指示器: 列6, 顺序ascending
2025-07-15 17:01:44.015 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_changed:5419 | 🔧 [调试] 排序指示器更新完成
2025-07-15 17:01:44.015 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_changed:5425 | 🔧 [调试] 准备调用_save_and_apply_sort_state
2025-07-15 17:01:44.015 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5441 | 🔧 [调试] 开始保存排序状态: 1 列
2025-07-15 17:01:44.015 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5457 | 🔧 [调试] 已获取主窗口: PrototypeMainWindow
2025-07-15 17:01:44.015 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5463 | 🔧 [调试] 表名: salary_data_2025_07_active_employees, 表类型: 
2025-07-15 17:01:44.015 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5470 | 🔧 [调试] 使用简化排序逻辑，直接使用列名
2025-07-15 17:01:44.015 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5475 | 🔧 [调试] 处理排序列: SortColumn(column_index=6, order=<SortOrder.ASCENDING: 'ascending'>, priority=0, column_name='grade_salary_2025')
2025-07-15 17:01:44.015 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5515 | 🔧 [调试] 转换后的排序列: [{'column_name': 'grade_salary_2025', 'order': 'ascending', 'priority': 0, 'column_index': 6}]
2025-07-15 17:01:44.015 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5519 | 🔧 [调试] 排序状态管理器可用
2025-07-15 17:01:44.015 | INFO     | src.core.table_sort_state_manager:save_sort_state:229 | 已保存排序状态: salary_data_2025_07_active_employees (), 1 列
2025-07-15 17:01:44.015 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5529 | 🔧 [调试] 排序状态保存结果: True
2025-07-15 17:01:44.015 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5532 | 🆕 排序状态已保存: salary_data_2025_07_active_employees, 1 列
2025-07-15 17:01:44.015 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_trigger_data_reload:5625 | 🔧 [调试] 开始触发数据重新加载: salary_data_2025_07_active_employees
2025-07-15 17:01:44.015 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_trigger_data_reload:5636 | 🔧 [调试] 分页模式检查: True
2025-07-15 17:01:44.015 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_trigger_data_reload:5646 | 🔧 获取分页组件当前页码: 1
2025-07-15 17:01:44.015 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_trigger_data_reload:5655 | 🔧 排序时保持页码: 1
2025-07-15 17:01:44.015 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_trigger_data_reload:5658 | 🆕 触发分页数据重新加载（应用排序）: salary_data_2025_07_active_employees, 第1页
2025-07-15 17:01:44.015 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:4900 | 使用分页模式加载 salary_data_2025_07_active_employees，第1页，每页50条
2025-07-15 17:01:44.015 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:4943 | 缓存未命中，从数据库加载: salary_data_2025_07_active_employees 第1页
2025-07-15 17:01:44.015 | INFO     | src.gui.multi_column_sort_manager:add_sort_column:274 | 添加排序列: 6(grade_salary_2025) -> ascending, 当前排序列数: 1
2025-07-15 17:01:44.015 | INFO     | src.gui.prototype.prototype_main_window:run:128 | 开始加载表 salary_data_2025_07_active_employees 第1页数据，每页50条
2025-07-15 17:01:44.015 | INFO     | src.gui.multi_column_sort_manager:_on_sort_indicator_changed:211 | 排序指示器变化: 列6(grade_salary_2025) -> ascending
2025-07-15 17:01:44.015 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_header_sort_changed:5890 | 🔧 [表格排序] 表头排序变化: 列6, 顺序ASC
2025-07-15 17:01:44.015 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_changed:5401 | 排序变化: 1 列
2025-07-15 17:01:44.015 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_changed:5405 | 🔧 [排序修复] 正在重新加载数据，跳过排序处理
2025-07-15 17:01:44.015 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:439 | 正在从表 salary_data_2025_07_active_employees 分页获取数据: 第1页, 每页50条
2025-07-15 17:01:44.015 | INFO     | src.gui.multi_column_sort_manager:add_sort_column:274 | 添加排序列: 6(grade_salary_2025) -> ascending, 当前排序列数: 1
2025-07-15 17:01:44.015 | INFO     | src.gui.multi_column_sort_manager:_on_sort_indicator_changed:211 | 排序指示器变化: 列6(grade_salary_2025) -> ascending
2025-07-15 17:01:44.015 | INFO     | src.gui.prototype.prototype_main_window:_on_sort_indicator_changed:3112 | 🔧 [全局排序] 排序请求: 列6, 顺序ASC
2025-07-15 17:01:44.015 | INFO     | src.gui.prototype.prototype_main_window:_get_table_field_mapping:3297 | 🔧 [字段映射] 加载映射成功: salary_data_2025_07_active_employees, 28个字段
2025-07-15 17:01:44.015 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:462 | 成功从表 salary_data_2025_07_active_employees 获取第1页数据: 50 行，总计1396行
2025-07-15 17:01:44.015 | INFO     | src.gui.prototype.prototype_main_window:run:166 | 原始数据: 50行, 28列
2025-07-15 17:01:44.015 | INFO     | src.services.table_data_service:_handle_sort_request:135 | 处理排序请求: salary_data_2025_07_active_employees
2025-07-15 17:01:44.015 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:3259 | 🔧 [事件排序] 已发布排序请求事件: salary_data_2025_07_active_employees, 1列
2025-07-15 17:01:44.030 | INFO     | src.gui.prototype.prototype_main_window:_on_sort_indicator_changed:3144 | 🔧 [新架构排序] 排序完成: 2025年薪级工资 ascending, 保持第1页
2025-07-15 17:01:44.030 | INFO     | src.gui.prototype.prototype_main_window:run:173 | 开始应用字段映射
2025-07-15 17:01:44.030 | INFO     | src.core.unified_data_request_manager:request_table_data:177 | 开始处理数据请求: salary_data_2025_07_active_employees, 类型: sort_change
2025-07-15 17:01:44.030 | INFO     | src.gui.prototype.prototype_main_window:_apply_unified_field_processing:4454 | 🔧 [修复标识] 开始统一字段处理: salary_data_2025_07_active_employees, 原始列数: 28
2025-07-15 17:01:44.030 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:634 | 🔧 [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_07_active_employees' 中（类型: REAL）
2025-07-15 17:01:44.030 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:487 | 正在从表 salary_data_2025_07_active_employees 分页获取数据（支持排序）: 第1页, 每页50条, 排序=1列
2025-07-15 17:01:44.046 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:516 | 🔧 [排序修复] 准备排序列: grade_salary_2025
2025-07-15 17:01:44.046 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:634 | 🔧 [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_07_active_employees' 中（类型: REAL）
2025-07-15 17:01:44.046 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:523 | 🔧 [排序修复] 列 'grade_salary_2025' 验证通过，开始排序
2025-07-15 17:01:44.046 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:532 | 🔧 [排序修复] 数值列排序: grade_salary_2025 ASC
2025-07-15 17:01:44.046 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:539 | 🔧 [排序修复] 添加排序子句: CAST("grade_salary_2025" AS REAL) ASC
2025-07-15 17:01:44.046 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:547 | 🔧 [排序调试] 执行的完整SQL查询: SELECT * FROM "salary_data_2025_07_active_employees" ORDER BY CAST("grade_salary_2025" AS REAL) ASC LIMIT 50 OFFSET 0
2025-07-15 17:01:44.046 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:559 | 🔧 [排序调试] 查询结果中 grade_salary_2025 的前10个值: [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 596.0]
2025-07-15 17:01:44.046 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:561 | 成功从表 salary_data_2025_07_active_employees 获取第1页数据（含排序）: 50 行，总计1396行
2025-07-15 17:01:44.046 | INFO     | src.core.unified_data_request_manager:request_table_data:226 | 数据请求处理完成: 28字段, 50行, 耗时15.7ms
2025-07-15 17:01:44.046 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:2982 | 🆕 接收到新架构数据更新事件: salary_data_2025_07_active_employees
2025-07-15 17:01:44.046 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:4635 | 🔧 [修复标识] 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-15 17:01:44.046 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3007 | 数据内容: 50行 x 28列
2025-07-15 17:01:44.046 | INFO     | src.gui.prototype.prototype_main_window:set_data:635 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-07-15 17:01:44.046 | INFO     | src.gui.prototype.prototype_main_window:_apply_unified_field_processing:4499 | 🔧 [字段处理] 统一字段处理完成并缓存: 24个字段
2025-07-15 17:01:44.062 | INFO     | src.gui.prototype.prototype_main_window:run:183 | 🔧 [修复标识] PaginationWorker - 字段映射成功: 28 -> 24列
2025-07-15 17:01:44.062 | INFO     | src.gui.prototype.prototype_main_window:run:197 | 字段映射成功: 24列
2025-07-15 17:01:44.062 | INFO     | src.gui.prototype.prototype_main_window:run:207 | 开始应用数据格式化处理
2025-07-15 17:01:44.062 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:238 | 开始格式化工资表数据 - 表类型: salary_data_2025_07_active_employees
2025-07-15 17:01:44.062 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:251 | 原始数据: 50行, 24列
2025-07-15 17:01:44.062 | INFO     | src.modules.data_storage.salary_data_formatter:_extract_table_type_from_name:335 | 🔧 [表名映射] 通过关键词 'active_employees' 匹配到表类型: active_employees
2025-07-15 17:01:44.062 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:255 | 🔧 [表名映射] 输入表名: salary_data_2025_07_active_employees, 提取的表类型: active_employees
2025-07-15 17:01:44.062 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:259 | 使用表类型配置: active_employees, 配置字段: ['id_fields', 'string_fields', 'integer_fields', 'float_fields', 'special_fields']
2025-07-15 17:01:44.062 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:263 | 🔧 [格式化调试] 开始处理ID字段: ['employee_id', 'employee_type_code', '工号', '人员类别代码']
2025-07-15 17:01:44.077 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:268 | 🔧 [格式化调试] 处理前 工号 字段样例: ['19990089.0', '20161565.0', '20191782.0']
2025-07-15 17:01:44.077 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:268 | 🔧 [格式化调试] 处理前 人员类别代码 字段样例: ['1.0', '1.0', '17.0']
2025-07-15 17:01:44.077 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_id_fields:461 | 🔧 [警告] ID字段 employee_id 不存在于数据框中
2025-07-15 17:01:44.077 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_id_fields:461 | 🔧 [警告] ID字段 employee_type_code 不存在于数据框中
2025-07-15 17:01:44.077 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:377 | 🔧 [强化调试] 处理ID字段 工号: 原始样例=['19990089.0', '20161565.0', '20191782.0'], 原始类型=object
2025-07-15 17:01:44.077 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:426 | 🔧 [强化调试] 清理后ID字段 工号: 样例=['19990089', '20161565', '20191782']
2025-07-15 17:01:44.077 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:443 | 🔧 [强化调试] 最终ID字段 工号: 样例=['19990089', '20161565', '20191782']
2025-07-15 17:01:44.077 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:454 | 🔧 [验证成功] ID字段 工号 格式化完成，无小数点问题
2025-07-15 17:01:44.077 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:377 | 🔧 [强化调试] 处理ID字段 人员类别代码: 原始样例=['1.0', '1.0', '17.0'], 原始类型=object
2025-07-15 17:01:44.077 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:426 | 🔧 [强化调试] 清理后ID字段 人员类别代码: 样例=['1', '1', '17']
2025-07-15 17:01:44.077 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:443 | 🔧 [强化调试] 最终ID字段 人员类别代码: 样例=['01', '01', '17']
2025-07-15 17:01:44.077 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:454 | 🔧 [验证成功] ID字段 人员类别代码 格式化完成，无小数点问题
2025-07-15 17:01:44.077 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:274 | 🔧 [格式化调试] 处理后 工号 字段样例: ['19990089', '20161565', '20191782']
2025-07-15 17:01:44.077 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:274 | 🔧 [格式化调试] 处理后 人员类别代码 字段样例: ['01', '01', '17']
2025-07-15 17:01:44.077 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:472 | 🔧 [强化调试] 开始格式化字符串字段: ['employee_id', 'employee_name', 'department', 'employee_type_code', 'employee_type', '工号', '姓名', '部门名称', '人员类别代码', '人员类别']
2025-07-15 17:01:44.077 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 employee_id 不存在于数据框中
2025-07-15 17:01:44.077 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 employee_name 不存在于数据框中
2025-07-15 17:01:44.077 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 department 不存在于数据框中
2025-07-15 17:01:44.077 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 employee_type_code 不存在于数据框中
2025-07-15 17:01:44.077 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 employee_type 不存在于数据框中
2025-07-15 17:01:44.077 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 工号: 原始样例=['19990089', '20161565', '20191782'], 原始类型=object
2025-07-15 17:01:44.077 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:504 | 🔧 [修复] 工号字段 工号 已特殊处理，去除小数点格式
2025-07-15 17:01:44.093 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 工号: 样例=['19990089', '20161565', '20191782'], 类型=object
2025-07-15 17:01:44.108 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 工号 格式化完成，无小数点问题
2025-07-15 17:01:44.108 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 姓名: 原始样例=['杨胜', '胡四平', '肖啸'], 原始类型=object
2025-07-15 17:01:44.108 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 姓名: 样例=['杨胜', '胡四平', '肖啸'], 类型=object
2025-07-15 17:01:44.108 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 姓名 格式化完成，无小数点问题
2025-07-15 17:01:44.108 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 部门名称: 原始样例=['自动化学院', '自动化学院', '自动化学院'], 原始类型=object
2025-07-15 17:01:44.108 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 部门名称: 样例=['自动化学院', '自动化学院', '自动化学院'], 类型=object
2025-07-15 17:01:44.108 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 部门名称 格式化完成，无小数点问题
2025-07-15 17:01:44.108 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 人员类别代码: 原始样例=['01', '01', '17'], 原始类型=object
2025-07-15 17:01:44.108 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 人员类别代码: 样例=['01', '01', '17'], 类型=object
2025-07-15 17:01:44.108 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 人员类别代码 格式化完成，无小数点问题
2025-07-15 17:01:44.108 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 人员类别: 原始样例=['教学院其它人员', '教学单位专技人员', '科研单位人员'], 原始类型=object
2025-07-15 17:01:44.108 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 人员类别: 样例=['教学院其它人员', '教学单位专技人员', '科研单位人员'], 类型=object
2025-07-15 17:01:44.108 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 人员类别 格式化完成，无小数点问题
2025-07-15 17:01:44.108 | INFO     | src.modules.data_storage.salary_data_formatter:_format_integer_fields:604 | 🔧 [强化调试] 开始格式化整型字段: []
2025-07-15 17:01:44.108 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:655 | 🔧 [强化调试] 开始格式化浮点型字段: ['position_salary_2025', 'grade_salary_2025', 'allowance', 'balance_allowance', 'basic_performance_2025', 'health_fee', 'transport_allowance', 'property_allowance', 'communication_allowance', 'performance_bonus_2025', 'provident_fund_2025', 'housing_allowance', 'car_allowance', 'supplement', 'advance', 'total_salary', 'pension_insurance', '2025年岗位工资', '2025年薪级工资', '津贴', '结余津贴', '2025年基础性绩效', '卫生费', '交通补贴', '物业补贴', '通讯补贴', '2025年奖励性绩效预发', '2025公积金', '住房补贴', '车补', '补发', '借支', '应发工资', '代扣代存养老保险']
2025-07-15 17:01:44.108 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 position_salary_2025 不存在于数据框中
2025-07-15 17:01:44.108 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 grade_salary_2025 不存在于数据框中
2025-07-15 17:01:44.124 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 allowance 不存在于数据框中
2025-07-15 17:01:44.124 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 balance_allowance 不存在于数据框中
2025-07-15 17:01:44.124 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 basic_performance_2025 不存在于数据框中
2025-07-15 17:01:44.140 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 health_fee 不存在于数据框中
2025-07-15 17:01:44.140 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 transport_allowance 不存在于数据框中
2025-07-15 17:01:44.140 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 property_allowance 不存在于数据框中
2025-07-15 17:01:44.140 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 communication_allowance 不存在于数据框中
2025-07-15 17:01:44.140 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 performance_bonus_2025 不存在于数据框中
2025-07-15 17:01:44.140 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 provident_fund_2025 不存在于数据框中
2025-07-15 17:01:44.140 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 housing_allowance 不存在于数据框中
2025-07-15 17:01:44.140 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 car_allowance 不存在于数据框中
2025-07-15 17:01:44.140 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 supplement 不存在于数据框中
2025-07-15 17:01:44.140 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 advance 不存在于数据框中
2025-07-15 17:01:44.140 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 total_salary 不存在于数据框中
2025-07-15 17:01:44.140 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 pension_insurance 不存在于数据框中
2025-07-15 17:01:44.140 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025年岗位工资: 原始样例=[2880.0, 3030.0, 2185.0], 原始类型=float64
2025-07-15 17:01:44.140 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025年岗位工资: 样例=[2880.0, 3030.0, 2185.0], 类型=float64
2025-07-15 17:01:44.140 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025年岗位工资 格式化完成，保留两位小数
2025-07-15 17:01:44.140 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025年薪级工资: 原始样例=[2375.0, 1696.0, 1427.0], 原始类型=float64
2025-07-15 17:01:44.140 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025年薪级工资: 样例=[2375.0, 1696.0, 1427.0], 类型=float64
2025-07-15 17:01:44.140 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025年薪级工资 格式化完成，保留两位小数
2025-07-15 17:01:44.140 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 津贴: 原始样例=[102.0, 0.0, 0.0], 原始类型=float64
2025-07-15 17:01:44.140 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 津贴: 样例=[102.0, 0.0, 0.0], 类型=float64
2025-07-15 17:01:44.140 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 津贴 格式化完成，保留两位小数
2025-07-15 17:01:44.140 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 结余津贴: 原始样例=[56.0, 56.0, 56.0], 原始类型=float64
2025-07-15 17:01:44.140 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 结余津贴: 样例=[56.0, 56.0, 56.0], 类型=float64
2025-07-15 17:01:44.140 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 结余津贴 格式化完成，保留两位小数
2025-07-15 17:01:44.140 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025年基础性绩效: 原始样例=[3594.0, 3466.0, 2978.0], 原始类型=float64
2025-07-15 17:01:44.155 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025年基础性绩效: 样例=[3594.0, 3466.0, 2978.0], 类型=float64
2025-07-15 17:01:44.171 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025年基础性绩效 格式化完成，保留两位小数
2025-07-15 17:01:44.171 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 卫生费: 原始样例=[0.0, 0.0, 0.0], 原始类型=float64
2025-07-15 17:01:44.176 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 卫生费: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-15 17:01:44.176 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 卫生费 格式化完成，保留两位小数
2025-07-15 17:01:44.176 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:133 | 使用已存在的配置文件: state/data/field_mappings.json
2025-07-15 17:01:44.176 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 交通补贴: 原始样例=[220.0, 220.0, 220.0], 原始类型=float64
2025-07-15 17:01:44.176 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 交通补贴: 样例=[220.0, 220.0, 220.0], 类型=float64
2025-07-15 17:01:44.176 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 交通补贴 格式化完成，保留两位小数
2025-07-15 17:01:44.176 | INFO     | src.modules.data_import.config_sync_manager:__init__:70 | 配置同步管理器初始化完成
2025-07-15 17:01:44.176 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 物业补贴: 原始样例=[240.0, 240.0, 200.0], 原始类型=float64
2025-07-15 17:01:44.176 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:1151 | 🔧 [修复] 没有找到匹配的字段映射
2025-07-15 17:01:44.176 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 物业补贴: 样例=[240.0, 240.0, 200.0], 类型=float64
2025-07-15 17:01:44.176 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 物业补贴 格式化完成，保留两位小数
2025-07-15 17:01:44.176 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 通讯补贴: 原始样例=[50.0, nan, nan], 原始类型=float64
2025-07-15 17:01:44.176 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 通讯补贴: 样例=[50.0, 0.0, 0.0], 类型=float64
2025-07-15 17:01:44.176 | INFO     | src.gui.prototype.prototype_main_window:_apply_system_field_filtering:1183 | 🔧 [修复] 过滤了 4 个系统字段
2025-07-15 17:01:44.176 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 通讯补贴 格式化完成，保留两位小数
2025-07-15 17:01:44.176 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025年奖励性绩效预发: 原始样例=[2500.0, 1000.0, 1000.0], 原始类型=float64
2025-07-15 17:01:44.176 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025年奖励性绩效预发: 样例=[2500.0, 1000.0, 1000.0], 类型=float64
2025-07-15 17:01:44.176 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025年奖励性绩效预发 格式化完成，保留两位小数
2025-07-15 17:01:44.176 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025公积金: 原始样例=[2097.0, 1860.0, 1984.0], 原始类型=float64
2025-07-15 17:01:44.176 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025公积金: 样例=[2097.0, 1860.0, 1984.0], 类型=float64
2025-07-15 17:01:44.176 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025公积金 格式化完成，保留两位小数
2025-07-15 17:01:44.176 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 住房补贴: 原始样例=[271.9745083294993, 189.0, 174.16354166666667], 原始类型=float64
2025-07-15 17:01:44.192 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 住房补贴: 样例=[271.97, 189.0, 174.16], 类型=float64
2025-07-15 17:01:44.192 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 住房补贴 格式化完成，保留两位小数
2025-07-15 17:01:44.192 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 车补: 原始样例=[None, None, None], 原始类型=object
2025-07-15 17:01:44.192 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 车补: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-15 17:01:44.192 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 车补 格式化完成，保留两位小数
2025-07-15 17:01:44.192 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 补发: 原始样例=[None, None, None], 原始类型=object
2025-07-15 17:01:44.192 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 补发: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-15 17:01:44.192 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 补发 格式化完成，保留两位小数
2025-07-15 17:01:44.192 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 借支: 原始样例=[nan, nan, 2000.0], 原始类型=float64
2025-07-15 17:01:44.207 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 借支: 样例=[0.0, 0.0, 2000.0], 类型=float64
2025-07-15 17:01:44.207 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 借支 格式化完成，保留两位小数
2025-07-15 17:01:44.207 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 应发工资: 原始样例=[12288.9745083295, 9897.0, 6240.163541666667], 原始类型=float64
2025-07-15 17:01:44.207 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 应发工资: 样例=[12288.97, 9897.0, 6240.16], 类型=float64
2025-07-15 17:01:44.207 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 应发工资 格式化完成，保留两位小数
2025-07-15 17:01:44.207 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 代扣代存养老保险: 原始样例=[1525.8000000000002, 1140.53, 1113.75], 原始类型=float64
2025-07-15 17:01:44.207 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 代扣代存养老保险: 样例=[1525.8, 1140.53, 1113.75], 类型=float64
2025-07-15 17:01:44.207 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 代扣代存养老保险 格式化完成，保留两位小数
2025-07-15 17:01:44.207 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:711 | 🔧 [特殊字段] 开始处理表类型 active_employees 的月份、年份字段
2025-07-15 17:01:44.207 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:866 | 🔧 [特殊字段] 月份、年份字段处理完成
2025-07-15 17:01:44.207 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:291 | 数据格式化完成: 50行, 24列
2025-07-15 17:01:44.207 | INFO     | src.gui.prototype.prototype_main_window:run:215 | 数据格式化成功: 50行, 24列
2025-07-15 17:01:44.207 | INFO     | src.gui.prototype.prototype_main_window:run:239 | 最终数据: 50行, 24列, 总记录数: 1396
2025-07-15 17:01:44.207 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:4973 | 分页数据加载成功（数据库）: 50条数据，第1页，总计1396条
2025-07-15 17:01:44.207 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:439 | 正在从表 salary_data_2025_07_active_employees 分页获取数据: 第2页, 每页50条
2025-07-15 17:01:44.223 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:5006 | 🔧 [异步分页] 开始应用数据格式化处理
2025-07-15 17:01:44.223 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:462 | 成功从表 salary_data_2025_07_active_employees 获取第2页数据: 50 行，总计1396行
2025-07-15 17:01:44.223 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:238 | 开始格式化工资表数据 - 表类型: salary_data_2025_07_active_employees
2025-07-15 17:01:44.239 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:251 | 原始数据: 50行, 24列
2025-07-15 17:01:44.239 | INFO     | src.modules.data_storage.salary_data_formatter:_extract_table_type_from_name:335 | 🔧 [表名映射] 通过关键词 'active_employees' 匹配到表类型: active_employees
2025-07-15 17:01:44.239 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:255 | 🔧 [表名映射] 输入表名: salary_data_2025_07_active_employees, 提取的表类型: active_employees
2025-07-15 17:01:44.239 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:259 | 使用表类型配置: active_employees, 配置字段: ['id_fields', 'string_fields', 'integer_fields', 'float_fields', 'special_fields']
2025-07-15 17:01:44.239 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:263 | 🔧 [格式化调试] 开始处理ID字段: ['employee_id', 'employee_type_code', '工号', '人员类别代码']
2025-07-15 17:01:44.239 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:268 | 🔧 [格式化调试] 处理前 工号 字段样例: ['19990089', '20161565', '20191782']
2025-07-15 17:01:44.239 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:268 | 🔧 [格式化调试] 处理前 人员类别代码 字段样例: ['01', '01', '17']
2025-07-15 17:01:44.239 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_id_fields:461 | 🔧 [警告] ID字段 employee_id 不存在于数据框中
2025-07-15 17:01:44.239 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_id_fields:461 | 🔧 [警告] ID字段 employee_type_code 不存在于数据框中
2025-07-15 17:01:44.239 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:377 | 🔧 [强化调试] 处理ID字段 工号: 原始样例=['19990089', '20161565', '20191782'], 原始类型=object
2025-07-15 17:01:44.239 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:426 | 🔧 [强化调试] 清理后ID字段 工号: 样例=['19990089', '20161565', '20191782']
2025-07-15 17:01:44.239 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:443 | 🔧 [强化调试] 最终ID字段 工号: 样例=['19990089', '20161565', '20191782']
2025-07-15 17:01:44.239 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:454 | 🔧 [验证成功] ID字段 工号 格式化完成，无小数点问题
2025-07-15 17:01:44.239 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:377 | 🔧 [强化调试] 处理ID字段 人员类别代码: 原始样例=['01', '01', '17'], 原始类型=object
2025-07-15 17:01:44.239 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:426 | 🔧 [强化调试] 清理后ID字段 人员类别代码: 样例=['01', '01', '17']
2025-07-15 17:01:44.239 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:443 | 🔧 [强化调试] 最终ID字段 人员类别代码: 样例=['01', '01', '17']
2025-07-15 17:01:44.239 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:454 | 🔧 [验证成功] ID字段 人员类别代码 格式化完成，无小数点问题
2025-07-15 17:01:44.239 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:274 | 🔧 [格式化调试] 处理后 工号 字段样例: ['19990089', '20161565', '20191782']
2025-07-15 17:01:44.239 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:274 | 🔧 [格式化调试] 处理后 人员类别代码 字段样例: ['01', '01', '17']
2025-07-15 17:01:44.239 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:472 | 🔧 [强化调试] 开始格式化字符串字段: ['employee_id', 'employee_name', 'department', 'employee_type_code', 'employee_type', '工号', '姓名', '部门名称', '人员类别代码', '人员类别']
2025-07-15 17:01:44.239 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 employee_id 不存在于数据框中
2025-07-15 17:01:44.239 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 employee_name 不存在于数据框中
2025-07-15 17:01:44.239 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 department 不存在于数据框中
2025-07-15 17:01:44.239 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 employee_type_code 不存在于数据框中
2025-07-15 17:01:44.239 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 employee_type 不存在于数据框中
2025-07-15 17:01:44.239 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 工号: 原始样例=['19990089', '20161565', '20191782'], 原始类型=object
2025-07-15 17:01:44.239 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:504 | 🔧 [修复] 工号字段 工号 已特殊处理，去除小数点格式
2025-07-15 17:01:44.239 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 工号: 样例=['19990089', '20161565', '20191782'], 类型=object
2025-07-15 17:01:44.239 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 工号 格式化完成，无小数点问题
2025-07-15 17:01:44.239 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 姓名: 原始样例=['杨胜', '胡四平', '肖啸'], 原始类型=object
2025-07-15 17:01:44.255 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 姓名: 样例=['杨胜', '胡四平', '肖啸'], 类型=object
2025-07-15 17:01:44.255 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 姓名 格式化完成，无小数点问题
2025-07-15 17:01:44.255 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 部门名称: 原始样例=['自动化学院', '自动化学院', '自动化学院'], 原始类型=object
2025-07-15 17:01:44.255 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 部门名称: 样例=['自动化学院', '自动化学院', '自动化学院'], 类型=object
2025-07-15 17:01:44.255 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 部门名称 格式化完成，无小数点问题
2025-07-15 17:01:44.255 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 人员类别代码: 原始样例=['01', '01', '17'], 原始类型=object
2025-07-15 17:01:44.270 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 人员类别代码: 样例=['01', '01', '17'], 类型=object
2025-07-15 17:01:44.270 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 人员类别代码 格式化完成，无小数点问题
2025-07-15 17:01:44.270 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 人员类别: 原始样例=['教学院其它人员', '教学单位专技人员', '科研单位人员'], 原始类型=object
2025-07-15 17:01:44.270 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 人员类别: 样例=['教学院其它人员', '教学单位专技人员', '科研单位人员'], 类型=object
2025-07-15 17:01:44.270 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 人员类别 格式化完成，无小数点问题
2025-07-15 17:01:44.270 | INFO     | src.modules.data_storage.salary_data_formatter:_format_integer_fields:604 | 🔧 [强化调试] 开始格式化整型字段: []
2025-07-15 17:01:44.270 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:655 | 🔧 [强化调试] 开始格式化浮点型字段: ['position_salary_2025', 'grade_salary_2025', 'allowance', 'balance_allowance', 'basic_performance_2025', 'health_fee', 'transport_allowance', 'property_allowance', 'communication_allowance', 'performance_bonus_2025', 'provident_fund_2025', 'housing_allowance', 'car_allowance', 'supplement', 'advance', 'total_salary', 'pension_insurance', '2025年岗位工资', '2025年薪级工资', '津贴', '结余津贴', '2025年基础性绩效', '卫生费', '交通补贴', '物业补贴', '通讯补贴', '2025年奖励性绩效预发', '2025公积金', '住房补贴', '车补', '补发', '借支', '应发工资', '代扣代存养老保险']
2025-07-15 17:01:44.270 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 position_salary_2025 不存在于数据框中
2025-07-15 17:01:44.270 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 grade_salary_2025 不存在于数据框中
2025-07-15 17:01:44.270 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 allowance 不存在于数据框中
2025-07-15 17:01:44.270 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 balance_allowance 不存在于数据框中
2025-07-15 17:01:44.270 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 basic_performance_2025 不存在于数据框中
2025-07-15 17:01:44.270 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 health_fee 不存在于数据框中
2025-07-15 17:01:44.270 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 transport_allowance 不存在于数据框中
2025-07-15 17:01:44.270 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 property_allowance 不存在于数据框中
2025-07-15 17:01:44.270 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 communication_allowance 不存在于数据框中
2025-07-15 17:01:44.270 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 performance_bonus_2025 不存在于数据框中
2025-07-15 17:01:44.270 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 provident_fund_2025 不存在于数据框中
2025-07-15 17:01:44.270 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 housing_allowance 不存在于数据框中
2025-07-15 17:01:44.270 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 car_allowance 不存在于数据框中
2025-07-15 17:01:44.270 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 supplement 不存在于数据框中
2025-07-15 17:01:44.270 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 advance 不存在于数据框中
2025-07-15 17:01:44.270 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 total_salary 不存在于数据框中
2025-07-15 17:01:44.270 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 pension_insurance 不存在于数据框中
2025-07-15 17:01:44.270 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025年岗位工资: 原始样例=[2880.0, 3030.0, 2185.0], 原始类型=float64
2025-07-15 17:01:44.270 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025年岗位工资: 样例=[2880.0, 3030.0, 2185.0], 类型=float64
2025-07-15 17:01:44.270 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025年岗位工资 格式化完成，保留两位小数
2025-07-15 17:01:44.270 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025年薪级工资: 原始样例=[2375.0, 1696.0, 1427.0], 原始类型=float64
2025-07-15 17:01:44.270 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025年薪级工资: 样例=[2375.0, 1696.0, 1427.0], 类型=float64
2025-07-15 17:01:44.270 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025年薪级工资 格式化完成，保留两位小数
2025-07-15 17:01:44.270 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 津贴: 原始样例=[102.0, 0.0, 0.0], 原始类型=float64
2025-07-15 17:01:44.270 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 津贴: 样例=[102.0, 0.0, 0.0], 类型=float64
2025-07-15 17:01:44.286 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 津贴 格式化完成，保留两位小数
2025-07-15 17:01:44.292 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:133 | 使用已存在的配置文件: state/data/field_mappings.json
2025-07-15 17:01:44.292 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 结余津贴: 原始样例=[56.0, 56.0, 56.0], 原始类型=float64
2025-07-15 17:01:44.292 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 结余津贴: 样例=[56.0, 56.0, 56.0], 类型=float64
2025-07-15 17:01:44.292 | INFO     | src.modules.data_import.config_sync_manager:__init__:70 | 配置同步管理器初始化完成
2025-07-15 17:01:44.292 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 结余津贴 格式化完成，保留两位小数
2025-07-15 17:01:44.292 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:1220 | 🔧 [修复] 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-15 17:01:44.292 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025年基础性绩效: 原始样例=[3594.0, 3466.0, 2978.0], 原始类型=float64
2025-07-15 17:01:44.292 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025年基础性绩效: 样例=[3594.0, 3466.0, 2978.0], 类型=float64
2025-07-15 17:01:44.292 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025年基础性绩效 格式化完成，保留两位小数
2025-07-15 17:01:44.323 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3560 | 最大可见行数已更新: 50 -> 50
2025-07-15 17:01:44.323 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 卫生费: 原始样例=[0.0, 0.0, 0.0], 原始类型=float64
2025-07-15 17:01:44.323 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:3613 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-15 17:01:44.323 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 卫生费: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-15 17:01:44.323 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 卫生费 格式化完成，保留两位小数
2025-07-15 17:01:44.323 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 交通补贴: 原始样例=[220.0, 220.0, 220.0], 原始类型=float64
2025-07-15 17:01:44.323 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 交通补贴: 样例=[220.0, 220.0, 220.0], 类型=float64
2025-07-15 17:01:44.323 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2237 | 🔧 [修复标识] 字段映射生效，样例: ['employee_id', 'employee_name', 'department'] -> ['工号', '姓名', '部门名称']
2025-07-15 17:01:44.323 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 交通补贴 格式化完成，保留两位小数
2025-07-15 17:01:44.339 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第0行数据工号: 19961347.0
2025-07-15 17:01:44.339 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 物业补贴: 原始样例=[240.0, 240.0, 200.0], 原始类型=float64
2025-07-15 17:01:44.339 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第1行数据工号: 20251003.0
2025-07-15 17:01:44.339 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 物业补贴: 样例=[240.0, 240.0, 200.0], 类型=float64
2025-07-15 17:01:44.339 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第2行数据工号: 20251006.0
2025-07-15 17:01:44.339 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 物业补贴 格式化完成，保留两位小数
2025-07-15 17:01:44.339 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第3行数据工号: 20251007.0
2025-07-15 17:01:44.339 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 通讯补贴: 原始样例=[50.0, 0.0, 0.0], 原始类型=float64
2025-07-15 17:01:44.339 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第4行数据工号: 20251008.0
2025-07-15 17:01:44.339 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 通讯补贴: 样例=[50.0, 0.0, 0.0], 类型=float64
2025-07-15 17:01:44.339 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:457 | 🔧 [排序修复] 可见行数据顺序: ['19961347.0', '20251003.0', '20251006.0', '20251007.0', '20251008.0']
2025-07-15 17:01:44.339 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 通讯补贴 格式化完成，保留两位小数
2025-07-15 17:01:44.339 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:424 | 表格数据已设置: 50 行, 24 列
2025-07-15 17:01:44.339 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025年奖励性绩效预发: 原始样例=[2500.0, 1000.0, 1000.0], 原始类型=float64
2025-07-15 17:01:44.386 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2363 | 🔧 [表格调试] 开始格式化ID字段: 列=工号, 原值=19961347.0, 类型=<class 'str'>
2025-07-15 17:01:44.386 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025年奖励性绩效预发: 样例=[2500.0, 1000.0, 1000.0], 类型=float64
2025-07-15 17:01:44.386 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2370 | 🔧 [表格调试] ID字段格式化完成: 列=工号, 原值=19961347.0, 格式化后=19961347.0
2025-07-15 17:01:44.386 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025年奖励性绩效预发 格式化完成，保留两位小数
2025-07-15 17:01:44.386 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2363 | 🔧 [表格调试] 开始格式化ID字段: 列=人员类别代码, 原值=17.0, 类型=<class 'str'>
2025-07-15 17:01:44.386 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025公积金: 原始样例=[2097.0, 1860.0, 1984.0], 原始类型=float64
2025-07-15 17:01:44.386 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2370 | 🔧 [表格调试] ID字段格式化完成: 列=人员类别代码, 原值=17.0, 格式化后=17.0
2025-07-15 17:01:44.386 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025公积金: 样例=[2097.0, 1860.0, 1984.0], 类型=float64
2025-07-15 17:01:44.511 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025公积金 格式化完成，保留两位小数
2025-07-15 17:01:44.526 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 住房补贴: 原始样例=[271.97, 189.0, 174.16], 原始类型=float64
2025-07-15 17:01:44.526 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2174 | 🔧 [性能分析] 表格数据设置完成: 50 行, 最大可见行数: 50, 总耗时: 234.73ms
2025-07-15 17:01:44.526 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 住房补贴: 样例=[271.97, 189.0, 174.16], 类型=float64
2025-07-15 17:01:44.526 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 初始化和状态: 31.42ms (13.4%)
2025-07-15 17:01:44.526 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 住房补贴 格式化完成，保留两位小数
2025-07-15 17:01:44.526 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 字段映射: 0.00ms (0.0%)
2025-07-15 17:01:44.526 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 车补: 原始样例=[0.0, 0.0, 0.0], 原始类型=float64
2025-07-15 17:01:44.526 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 数据模型更新: 15.69ms (6.7%)
2025-07-15 17:01:44.526 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 车补: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-15 17:01:44.526 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 表头设置: 47.00ms (20.0%)
2025-07-15 17:01:44.526 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 车补 格式化完成，保留两位小数
2025-07-15 17:01:44.526 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 设置行数: 0.00ms (0.0%)
2025-07-15 17:01:44.526 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 补发: 原始样例=[0.0, 0.0, 0.0], 原始类型=float64
2025-07-15 17:01:44.526 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 填充可见数据: 140.62ms (59.9%)
2025-07-15 17:01:44.526 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 补发: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-15 17:01:44.526 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 调整列宽: 0.00ms (0.0%)
2025-07-15 17:01:44.526 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 补发 格式化完成，保留两位小数
2025-07-15 17:01:44.526 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 设置行号: 0.00ms (0.0%)
2025-07-15 17:01:44.526 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 借支: 原始样例=[0.0, 0.0, 2000.0], 原始类型=float64
2025-07-15 17:01:44.526 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2182 | 🔧 [性能分析] 最耗时步骤: 填充可见数据 (140.62ms)
2025-07-15 17:01:44.526 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 借支: 样例=[0.0, 0.0, 2000.0], 类型=float64
2025-07-15 17:01:44.526 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 50
2025-07-15 17:01:44.526 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 借支 格式化完成，保留两位小数
2025-07-15 17:01:44.526 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-15 17:01:44.526 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 应发工资: 原始样例=[12288.97, 9897.0, 6240.16], 原始类型=float64
2025-07-15 17:01:44.526 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3033 | 🔧 [分页修复] 数据更新事件设置总记录数: 1396
2025-07-15 17:01:44.526 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 应发工资: 样例=[12288.97, 9897.0, 6240.16], 类型=float64
2025-07-15 17:01:44.526 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3044 | 🔧 [分页修复] 分页状态: 当前页=1, 总页数=28, 总记录数=1396
2025-07-15 17:01:44.526 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 应发工资 格式化完成，保留两位小数
2025-07-15 17:01:44.526 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3050 | 🔧 [分页修复] 按钮可用性: 下一页=True, 上一页=False
2025-07-15 17:01:44.542 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 代扣代存养老保险: 原始样例=[1525.8, 1140.53, 1113.75], 原始类型=float64
2025-07-15 17:01:44.542 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 代扣代存养老保险: 样例=[1525.8, 1140.53, 1113.75], 类型=float64
2025-07-15 17:01:44.542 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 代扣代存养老保险 格式化完成，保留两位小数
2025-07-15 17:01:44.542 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:711 | 🔧 [特殊字段] 开始处理表类型 active_employees 的月份、年份字段
2025-07-15 17:01:44.542 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:866 | 🔧 [特殊字段] 月份、年份字段处理完成
2025-07-15 17:01:44.558 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:291 | 数据格式化完成: 50行, 24列
2025-07-15 17:01:44.558 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:5008 | 🔧 [异步分页] 数据格式化成功: 50行, 24列
2025-07-15 17:01:44.558 | INFO     | src.gui.prototype.prototype_main_window:set_data:635 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-07-15 17:01:44.662 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:133 | 使用已存在的配置文件: state/data/field_mappings.json
2025-07-15 17:01:44.662 | INFO     | src.modules.data_import.config_sync_manager:__init__:70 | 配置同步管理器初始化完成
2025-07-15 17:01:44.662 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:1146 | 🔧 [修复] 应用字段映射: 24个字段
2025-07-15 17:01:44.771 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:133 | 使用已存在的配置文件: state/data/field_mappings.json
2025-07-15 17:01:44.771 | INFO     | src.modules.data_import.config_sync_manager:__init__:70 | 配置同步管理器初始化完成
2025-07-15 17:01:44.771 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:1220 | 🔧 [修复] 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-15 17:01:44.802 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3560 | 最大可见行数已更新: 50 -> 50
2025-07-15 17:01:44.802 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:3613 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-15 17:01:44.802 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2237 | 🔧 [修复标识] 字段映射生效，样例: ['employee_id', 'employee_name', 'department'] -> ['工号', '姓名', '部门名称']
2025-07-15 17:01:44.802 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第0行数据工号: 19990089
2025-07-15 17:01:44.802 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第1行数据工号: 20161565
2025-07-15 17:01:44.802 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第2行数据工号: 20191782
2025-07-15 17:01:44.802 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第3行数据工号: 20151515
2025-07-15 17:01:44.802 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第4行数据工号: 20181640
2025-07-15 17:01:44.802 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:457 | 🔧 [排序修复] 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-15 17:01:44.802 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:424 | 表格数据已设置: 50 行, 24 列
2025-07-15 17:01:44.802 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2363 | 🔧 [表格调试] 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-15 17:01:44.802 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2370 | 🔧 [表格调试] ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-15 17:01:44.802 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2363 | 🔧 [表格调试] 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-15 17:01:44.802 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2370 | 🔧 [表格调试] ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-15 17:01:44.818 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2174 | 🔧 [性能分析] 表格数据设置完成: 50 行, 最大可见行数: 50, 总耗时: 47.12ms
2025-07-15 17:01:44.818 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 初始化和状态: 31.46ms (66.8%)
2025-07-15 17:01:44.818 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 字段映射: 0.00ms (0.0%)
2025-07-15 17:01:44.833 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 数据模型更新: 0.00ms (0.0%)
2025-07-15 17:01:44.833 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 表头设置: 0.00ms (0.0%)
2025-07-15 17:01:44.833 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 设置行数: 0.00ms (0.0%)
2025-07-15 17:01:44.833 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 填充可见数据: 15.66ms (33.2%)
2025-07-15 17:01:44.833 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 调整列宽: 0.00ms (0.0%)
2025-07-15 17:01:44.833 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 设置行号: 0.00ms (0.0%)
2025-07-15 17:01:44.833 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2182 | 🔧 [性能分析] 最耗时步骤: 初始化和状态 (31.46ms)
2025-07-15 17:01:44.833 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 50
2025-07-15 17:01:44.833 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-15 17:01:48.990 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_changed:5401 | 排序变化: 1 列
2025-07-15 17:01:48.990 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_changed:5413 | 🔧 [调试] 数据重载状态正常，继续处理排序
2025-07-15 17:01:49.005 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_changed:5417 | 🔧 [调试] 开始更新排序指示器
2025-07-15 17:01:49.005 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_sort_indicators:5797 | 🔧 [排序指示器修复] 设置排序指示器: 列6, 顺序ascending
2025-07-15 17:01:49.005 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_changed:5419 | 🔧 [调试] 排序指示器更新完成
2025-07-15 17:01:49.005 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_changed:5425 | 🔧 [调试] 准备调用_save_and_apply_sort_state
2025-07-15 17:01:49.005 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5441 | 🔧 [调试] 开始保存排序状态: 1 列
2025-07-15 17:01:49.005 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5457 | 🔧 [调试] 已获取主窗口: PrototypeMainWindow
2025-07-15 17:01:49.005 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5463 | 🔧 [调试] 表名: salary_data_2025_07_active_employees, 表类型: 
2025-07-15 17:01:49.005 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5470 | 🔧 [调试] 使用简化排序逻辑，直接使用列名
2025-07-15 17:01:49.005 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5475 | 🔧 [调试] 处理排序列: SortColumn(column_index=6, order=<SortOrder.ASCENDING: 'ascending'>, priority=0, column_name='grade_salary_2025')
2025-07-15 17:01:49.005 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5515 | 🔧 [调试] 转换后的排序列: [{'column_name': 'grade_salary_2025', 'order': 'ascending', 'priority': 0, 'column_index': 6}]
2025-07-15 17:01:49.005 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5519 | 🔧 [调试] 排序状态管理器可用
2025-07-15 17:01:49.005 | INFO     | src.core.table_sort_state_manager:save_sort_state:229 | 已保存排序状态: salary_data_2025_07_active_employees (), 1 列
2025-07-15 17:01:49.005 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5529 | 🔧 [调试] 排序状态保存结果: True
2025-07-15 17:01:49.005 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5532 | 🆕 排序状态已保存: salary_data_2025_07_active_employees, 1 列
2025-07-15 17:01:49.005 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_trigger_data_reload:5625 | 🔧 [调试] 开始触发数据重新加载: salary_data_2025_07_active_employees
2025-07-15 17:01:49.005 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_trigger_data_reload:5636 | 🔧 [调试] 分页模式检查: True
2025-07-15 17:01:49.005 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_trigger_data_reload:5646 | 🔧 获取分页组件当前页码: 1
2025-07-15 17:01:49.005 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_trigger_data_reload:5655 | 🔧 排序时保持页码: 1
2025-07-15 17:01:49.005 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_trigger_data_reload:5658 | 🆕 触发分页数据重新加载（应用排序）: salary_data_2025_07_active_employees, 第1页
2025-07-15 17:01:49.005 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:4900 | 使用分页模式加载 salary_data_2025_07_active_employees，第1页，每页50条
2025-07-15 17:01:49.005 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:4909 | 缓存命中: salary_data_2025_07_active_employees 第1页
2025-07-15 17:01:49.005 | INFO     | src.gui.prototype.prototype_main_window:_apply_unified_field_processing:4454 | 🔧 [修复标识] 开始统一字段处理: salary_data_2025_07_active_employees, 原始列数: 24
2025-07-15 17:01:49.005 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:4635 | 🔧 [修复标识] 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-15 17:01:49.005 | INFO     | src.gui.prototype.prototype_main_window:_apply_unified_field_processing:4499 | 🔧 [字段处理] 统一字段处理完成并缓存: 24个字段
2025-07-15 17:01:49.005 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:4973 | 分页数据加载成功（缓存）: 50条数据，第1页，总计1396条
2025-07-15 17:01:49.021 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:5006 | 🔧 [异步分页] 开始应用数据格式化处理
2025-07-15 17:01:49.021 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:238 | 开始格式化工资表数据 - 表类型: salary_data_2025_07_active_employees
2025-07-15 17:01:49.021 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:251 | 原始数据: 50行, 24列
2025-07-15 17:01:49.021 | INFO     | src.modules.data_storage.salary_data_formatter:_extract_table_type_from_name:335 | 🔧 [表名映射] 通过关键词 'active_employees' 匹配到表类型: active_employees
2025-07-15 17:01:49.021 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:255 | 🔧 [表名映射] 输入表名: salary_data_2025_07_active_employees, 提取的表类型: active_employees
2025-07-15 17:01:49.021 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:259 | 使用表类型配置: active_employees, 配置字段: ['id_fields', 'string_fields', 'integer_fields', 'float_fields', 'special_fields']
2025-07-15 17:01:49.021 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:263 | 🔧 [格式化调试] 开始处理ID字段: ['employee_id', 'employee_type_code', '工号', '人员类别代码']
2025-07-15 17:01:49.021 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:268 | 🔧 [格式化调试] 处理前 工号 字段样例: ['19990089', '20161565', '20191782']
2025-07-15 17:01:49.021 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:268 | 🔧 [格式化调试] 处理前 人员类别代码 字段样例: ['01', '01', '17']
2025-07-15 17:01:49.021 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_id_fields:461 | 🔧 [警告] ID字段 employee_id 不存在于数据框中
2025-07-15 17:01:49.021 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_id_fields:461 | 🔧 [警告] ID字段 employee_type_code 不存在于数据框中
2025-07-15 17:01:49.021 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:377 | 🔧 [强化调试] 处理ID字段 工号: 原始样例=['19990089', '20161565', '20191782'], 原始类型=object
2025-07-15 17:01:49.021 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:426 | 🔧 [强化调试] 清理后ID字段 工号: 样例=['19990089', '20161565', '20191782']
2025-07-15 17:01:49.021 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:443 | 🔧 [强化调试] 最终ID字段 工号: 样例=['19990089', '20161565', '20191782']
2025-07-15 17:01:49.021 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:454 | 🔧 [验证成功] ID字段 工号 格式化完成，无小数点问题
2025-07-15 17:01:49.021 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:377 | 🔧 [强化调试] 处理ID字段 人员类别代码: 原始样例=['01', '01', '17'], 原始类型=object
2025-07-15 17:01:49.036 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:426 | 🔧 [强化调试] 清理后ID字段 人员类别代码: 样例=['01', '01', '17']
2025-07-15 17:01:49.036 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:443 | 🔧 [强化调试] 最终ID字段 人员类别代码: 样例=['01', '01', '17']
2025-07-15 17:01:49.036 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:454 | 🔧 [验证成功] ID字段 人员类别代码 格式化完成，无小数点问题
2025-07-15 17:01:49.036 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:274 | 🔧 [格式化调试] 处理后 工号 字段样例: ['19990089', '20161565', '20191782']
2025-07-15 17:01:49.036 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:274 | 🔧 [格式化调试] 处理后 人员类别代码 字段样例: ['01', '01', '17']
2025-07-15 17:01:49.036 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:472 | 🔧 [强化调试] 开始格式化字符串字段: ['employee_id', 'employee_name', 'department', 'employee_type_code', 'employee_type', '工号', '姓名', '部门名称', '人员类别代码', '人员类别']
2025-07-15 17:01:49.036 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 employee_id 不存在于数据框中
2025-07-15 17:01:49.036 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 employee_name 不存在于数据框中
2025-07-15 17:01:49.036 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 department 不存在于数据框中
2025-07-15 17:01:49.036 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 employee_type_code 不存在于数据框中
2025-07-15 17:01:49.036 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 employee_type 不存在于数据框中
2025-07-15 17:01:49.036 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 工号: 原始样例=['19990089', '20161565', '20191782'], 原始类型=object
2025-07-15 17:01:49.036 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:504 | 🔧 [修复] 工号字段 工号 已特殊处理，去除小数点格式
2025-07-15 17:01:49.036 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 工号: 样例=['19990089', '20161565', '20191782'], 类型=object
2025-07-15 17:01:49.036 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 工号 格式化完成，无小数点问题
2025-07-15 17:01:49.036 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 姓名: 原始样例=['杨胜', '胡四平', '肖啸'], 原始类型=object
2025-07-15 17:01:49.036 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 姓名: 样例=['杨胜', '胡四平', '肖啸'], 类型=object
2025-07-15 17:01:49.036 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 姓名 格式化完成，无小数点问题
2025-07-15 17:01:49.036 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 部门名称: 原始样例=['自动化学院', '自动化学院', '自动化学院'], 原始类型=object
2025-07-15 17:01:49.036 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 部门名称: 样例=['自动化学院', '自动化学院', '自动化学院'], 类型=object
2025-07-15 17:01:49.036 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 部门名称 格式化完成，无小数点问题
2025-07-15 17:01:49.036 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 人员类别代码: 原始样例=['01', '01', '17'], 原始类型=object
2025-07-15 17:01:49.036 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 人员类别代码: 样例=['01', '01', '17'], 类型=object
2025-07-15 17:01:49.036 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 人员类别代码 格式化完成，无小数点问题
2025-07-15 17:01:49.052 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 人员类别: 原始样例=['教学院其它人员', '教学单位专技人员', '科研单位人员'], 原始类型=object
2025-07-15 17:01:49.052 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 人员类别: 样例=['教学院其它人员', '教学单位专技人员', '科研单位人员'], 类型=object
2025-07-15 17:01:49.052 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 人员类别 格式化完成，无小数点问题
2025-07-15 17:01:49.052 | INFO     | src.modules.data_storage.salary_data_formatter:_format_integer_fields:604 | 🔧 [强化调试] 开始格式化整型字段: []
2025-07-15 17:01:49.052 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:655 | 🔧 [强化调试] 开始格式化浮点型字段: ['position_salary_2025', 'grade_salary_2025', 'allowance', 'balance_allowance', 'basic_performance_2025', 'health_fee', 'transport_allowance', 'property_allowance', 'communication_allowance', 'performance_bonus_2025', 'provident_fund_2025', 'housing_allowance', 'car_allowance', 'supplement', 'advance', 'total_salary', 'pension_insurance', '2025年岗位工资', '2025年薪级工资', '津贴', '结余津贴', '2025年基础性绩效', '卫生费', '交通补贴', '物业补贴', '通讯补贴', '2025年奖励性绩效预发', '2025公积金', '住房补贴', '车补', '补发', '借支', '应发工资', '代扣代存养老保险']
2025-07-15 17:01:49.052 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 position_salary_2025 不存在于数据框中
2025-07-15 17:01:49.052 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 grade_salary_2025 不存在于数据框中
2025-07-15 17:01:49.052 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 allowance 不存在于数据框中
2025-07-15 17:01:49.052 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 balance_allowance 不存在于数据框中
2025-07-15 17:01:49.052 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 basic_performance_2025 不存在于数据框中
2025-07-15 17:01:49.068 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 health_fee 不存在于数据框中
2025-07-15 17:01:49.068 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 transport_allowance 不存在于数据框中
2025-07-15 17:01:49.068 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 property_allowance 不存在于数据框中
2025-07-15 17:01:49.068 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 communication_allowance 不存在于数据框中
2025-07-15 17:01:49.068 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 performance_bonus_2025 不存在于数据框中
2025-07-15 17:01:49.068 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 provident_fund_2025 不存在于数据框中
2025-07-15 17:01:49.068 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 housing_allowance 不存在于数据框中
2025-07-15 17:01:49.068 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 car_allowance 不存在于数据框中
2025-07-15 17:01:49.068 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 supplement 不存在于数据框中
2025-07-15 17:01:49.068 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 advance 不存在于数据框中
2025-07-15 17:01:49.068 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 total_salary 不存在于数据框中
2025-07-15 17:01:49.068 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 pension_insurance 不存在于数据框中
2025-07-15 17:01:49.068 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025年岗位工资: 原始样例=[2880.0, 3030.0, 2185.0], 原始类型=float64
2025-07-15 17:01:49.068 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025年岗位工资: 样例=[2880.0, 3030.0, 2185.0], 类型=float64
2025-07-15 17:01:49.068 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025年岗位工资 格式化完成，保留两位小数
2025-07-15 17:01:49.068 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025年薪级工资: 原始样例=[2375.0, 1696.0, 1427.0], 原始类型=float64
2025-07-15 17:01:49.068 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025年薪级工资: 样例=[2375.0, 1696.0, 1427.0], 类型=float64
2025-07-15 17:01:49.068 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025年薪级工资 格式化完成，保留两位小数
2025-07-15 17:01:49.068 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 津贴: 原始样例=[102.0, 0.0, 0.0], 原始类型=float64
2025-07-15 17:01:49.068 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 津贴: 样例=[102.0, 0.0, 0.0], 类型=float64
2025-07-15 17:01:49.068 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 津贴 格式化完成，保留两位小数
2025-07-15 17:01:49.068 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 结余津贴: 原始样例=[56.0, 56.0, 56.0], 原始类型=float64
2025-07-15 17:01:49.068 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 结余津贴: 样例=[56.0, 56.0, 56.0], 类型=float64
2025-07-15 17:01:49.068 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 结余津贴 格式化完成，保留两位小数
2025-07-15 17:01:49.068 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025年基础性绩效: 原始样例=[3594.0, 3466.0, 2978.0], 原始类型=float64
2025-07-15 17:01:49.068 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025年基础性绩效: 样例=[3594.0, 3466.0, 2978.0], 类型=float64
2025-07-15 17:01:49.068 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025年基础性绩效 格式化完成，保留两位小数
2025-07-15 17:01:49.068 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 卫生费: 原始样例=[0.0, 0.0, 0.0], 原始类型=float64
2025-07-15 17:01:49.068 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 卫生费: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-15 17:01:49.068 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 卫生费 格式化完成，保留两位小数
2025-07-15 17:01:49.068 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 交通补贴: 原始样例=[220.0, 220.0, 220.0], 原始类型=float64
2025-07-15 17:01:49.083 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 交通补贴: 样例=[220.0, 220.0, 220.0], 类型=float64
2025-07-15 17:01:49.083 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 交通补贴 格式化完成，保留两位小数
2025-07-15 17:01:49.083 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 物业补贴: 原始样例=[240.0, 240.0, 200.0], 原始类型=float64
2025-07-15 17:01:49.083 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 物业补贴: 样例=[240.0, 240.0, 200.0], 类型=float64
2025-07-15 17:01:49.083 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 物业补贴 格式化完成，保留两位小数
2025-07-15 17:01:49.083 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 通讯补贴: 原始样例=[50.0, 0.0, 0.0], 原始类型=float64
2025-07-15 17:01:49.083 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 通讯补贴: 样例=[50.0, 0.0, 0.0], 类型=float64
2025-07-15 17:01:49.083 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 通讯补贴 格式化完成，保留两位小数
2025-07-15 17:01:49.083 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025年奖励性绩效预发: 原始样例=[2500.0, 1000.0, 1000.0], 原始类型=float64
2025-07-15 17:01:49.099 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025年奖励性绩效预发: 样例=[2500.0, 1000.0, 1000.0], 类型=float64
2025-07-15 17:01:49.099 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025年奖励性绩效预发 格式化完成，保留两位小数
2025-07-15 17:01:49.099 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025公积金: 原始样例=[2097.0, 1860.0, 1984.0], 原始类型=float64
2025-07-15 17:01:49.099 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025公积金: 样例=[2097.0, 1860.0, 1984.0], 类型=float64
2025-07-15 17:01:49.099 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025公积金 格式化完成，保留两位小数
2025-07-15 17:01:49.099 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 住房补贴: 原始样例=[271.97, 189.0, 174.16], 原始类型=float64
2025-07-15 17:01:49.099 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 住房补贴: 样例=[271.97, 189.0, 174.16], 类型=float64
2025-07-15 17:01:49.099 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 住房补贴 格式化完成，保留两位小数
2025-07-15 17:01:49.099 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 车补: 原始样例=[0.0, 0.0, 0.0], 原始类型=float64
2025-07-15 17:01:49.099 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 车补: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-15 17:01:49.099 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 车补 格式化完成，保留两位小数
2025-07-15 17:01:49.099 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 补发: 原始样例=[0.0, 0.0, 0.0], 原始类型=float64
2025-07-15 17:01:49.099 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 补发: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-15 17:01:49.099 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 补发 格式化完成，保留两位小数
2025-07-15 17:01:49.099 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 借支: 原始样例=[0.0, 0.0, 2000.0], 原始类型=float64
2025-07-15 17:01:49.099 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 借支: 样例=[0.0, 0.0, 2000.0], 类型=float64
2025-07-15 17:01:49.099 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 借支 格式化完成，保留两位小数
2025-07-15 17:01:49.099 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 应发工资: 原始样例=[12288.97, 9897.0, 6240.16], 原始类型=float64
2025-07-15 17:01:49.099 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 应发工资: 样例=[12288.97, 9897.0, 6240.16], 类型=float64
2025-07-15 17:01:49.099 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 应发工资 格式化完成，保留两位小数
2025-07-15 17:01:49.099 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 代扣代存养老保险: 原始样例=[1525.8, 1140.53, 1113.75], 原始类型=float64
2025-07-15 17:01:49.099 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 代扣代存养老保险: 样例=[1525.8, 1140.53, 1113.75], 类型=float64
2025-07-15 17:01:49.099 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 代扣代存养老保险 格式化完成，保留两位小数
2025-07-15 17:01:49.099 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:711 | 🔧 [特殊字段] 开始处理表类型 active_employees 的月份、年份字段
2025-07-15 17:01:49.099 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:866 | 🔧 [特殊字段] 月份、年份字段处理完成
2025-07-15 17:01:49.115 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:291 | 数据格式化完成: 50行, 24列
2025-07-15 17:01:49.115 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:5008 | 🔧 [异步分页] 数据格式化成功: 50行, 24列
2025-07-15 17:01:49.115 | INFO     | src.gui.prototype.prototype_main_window:set_data:635 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-07-15 17:01:49.230 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:133 | 使用已存在的配置文件: state/data/field_mappings.json
2025-07-15 17:01:49.230 | INFO     | src.modules.data_import.config_sync_manager:__init__:70 | 配置同步管理器初始化完成
2025-07-15 17:01:49.230 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:1146 | 🔧 [修复] 应用字段映射: 24个字段
2025-07-15 17:01:49.339 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:133 | 使用已存在的配置文件: state/data/field_mappings.json
2025-07-15 17:01:49.339 | INFO     | src.modules.data_import.config_sync_manager:__init__:70 | 配置同步管理器初始化完成
2025-07-15 17:01:49.339 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:1220 | 🔧 [修复] 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-15 17:01:49.374 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3560 | 最大可见行数已更新: 50 -> 50
2025-07-15 17:01:49.374 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:3613 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-15 17:01:49.374 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2237 | 🔧 [修复标识] 字段映射生效，样例: ['employee_id', 'employee_name', 'department'] -> ['工号', '姓名', '部门名称']
2025-07-15 17:01:49.374 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第0行数据工号: 19990089
2025-07-15 17:01:49.374 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第1行数据工号: 20161565
2025-07-15 17:01:49.374 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第2行数据工号: 20191782
2025-07-15 17:01:49.374 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第3行数据工号: 20151515
2025-07-15 17:01:49.374 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第4行数据工号: 20181640
2025-07-15 17:01:49.374 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:457 | 🔧 [排序修复] 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-15 17:01:49.374 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:424 | 表格数据已设置: 50 行, 24 列
2025-07-15 17:01:49.374 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2363 | 🔧 [表格调试] 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-15 17:01:49.374 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2370 | 🔧 [表格调试] ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-15 17:01:49.374 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2363 | 🔧 [表格调试] 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-15 17:01:49.374 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2370 | 🔧 [表格调试] ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-15 17:01:49.402 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2174 | 🔧 [性能分析] 表格数据设置完成: 50 行, 最大可见行数: 50, 总耗时: 62.74ms
2025-07-15 17:01:49.402 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 初始化和状态: 34.82ms (55.5%)
2025-07-15 17:01:49.402 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 字段映射: 0.00ms (0.0%)
2025-07-15 17:01:49.402 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 数据模型更新: 0.00ms (0.0%)
2025-07-15 17:01:49.402 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 表头设置: 0.00ms (0.0%)
2025-07-15 17:01:49.402 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 设置行数: 0.00ms (0.0%)
2025-07-15 17:01:49.402 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 填充可见数据: 27.93ms (44.5%)
2025-07-15 17:01:49.402 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 调整列宽: 0.00ms (0.0%)
2025-07-15 17:01:49.402 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 设置行号: 0.00ms (0.0%)
2025-07-15 17:01:49.402 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2182 | 🔧 [性能分析] 最耗时步骤: 初始化和状态 (34.82ms)
2025-07-15 17:01:49.402 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 50
2025-07-15 17:01:49.402 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-15 17:01:49.402 | INFO     | src.gui.multi_column_sort_manager:add_sort_column:274 | 添加排序列: 6(grade_salary_2025) -> ascending, 当前排序列数: 1
2025-07-15 17:01:49.402 | INFO     | src.gui.multi_column_sort_manager:_on_sort_indicator_changed:211 | 排序指示器变化: 列6(grade_salary_2025) -> ascending
2025-07-15 17:01:49.402 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_header_sort_changed:5890 | 🔧 [表格排序] 表头排序变化: 列6, 顺序ASC
2025-07-15 17:01:49.402 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_changed:5401 | 排序变化: 1 列
2025-07-15 17:01:49.402 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_changed:5413 | 🔧 [调试] 数据重载状态正常，继续处理排序
2025-07-15 17:01:49.402 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_changed:5417 | 🔧 [调试] 开始更新排序指示器
2025-07-15 17:01:49.402 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_sort_indicators:5797 | 🔧 [排序指示器修复] 设置排序指示器: 列6, 顺序ascending
2025-07-15 17:01:49.402 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_changed:5419 | 🔧 [调试] 排序指示器更新完成
2025-07-15 17:01:49.418 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_changed:5425 | 🔧 [调试] 准备调用_save_and_apply_sort_state
2025-07-15 17:01:49.418 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5441 | 🔧 [调试] 开始保存排序状态: 1 列
2025-07-15 17:01:49.418 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5457 | 🔧 [调试] 已获取主窗口: PrototypeMainWindow
2025-07-15 17:01:49.434 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5463 | 🔧 [调试] 表名: salary_data_2025_07_active_employees, 表类型: 
2025-07-15 17:01:49.434 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5470 | 🔧 [调试] 使用简化排序逻辑，直接使用列名
2025-07-15 17:01:49.434 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5475 | 🔧 [调试] 处理排序列: SortColumn(column_index=6, order=<SortOrder.ASCENDING: 'ascending'>, priority=0, column_name='grade_salary_2025')
2025-07-15 17:01:49.434 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5515 | 🔧 [调试] 转换后的排序列: [{'column_name': 'grade_salary_2025', 'order': 'ascending', 'priority': 0, 'column_index': 6}]
2025-07-15 17:01:49.434 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5519 | 🔧 [调试] 排序状态管理器可用
2025-07-15 17:01:49.434 | INFO     | src.core.table_sort_state_manager:save_sort_state:229 | 已保存排序状态: salary_data_2025_07_active_employees (), 1 列
2025-07-15 17:01:49.434 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5529 | 🔧 [调试] 排序状态保存结果: True
2025-07-15 17:01:49.434 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5532 | 🆕 排序状态已保存: salary_data_2025_07_active_employees, 1 列
2025-07-15 17:01:49.434 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_trigger_data_reload:5625 | 🔧 [调试] 开始触发数据重新加载: salary_data_2025_07_active_employees
2025-07-15 17:01:49.434 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_trigger_data_reload:5636 | 🔧 [调试] 分页模式检查: True
2025-07-15 17:01:49.434 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_trigger_data_reload:5646 | 🔧 获取分页组件当前页码: 1
2025-07-15 17:01:49.434 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_trigger_data_reload:5655 | 🔧 排序时保持页码: 1
2025-07-15 17:01:49.434 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_trigger_data_reload:5658 | 🆕 触发分页数据重新加载（应用排序）: salary_data_2025_07_active_employees, 第1页
2025-07-15 17:01:49.434 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:4900 | 使用分页模式加载 salary_data_2025_07_active_employees，第1页，每页50条
2025-07-15 17:01:49.434 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:4909 | 缓存命中: salary_data_2025_07_active_employees 第1页
2025-07-15 17:01:49.434 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:4973 | 分页数据加载成功（缓存）: 50条数据，第1页，总计1396条
2025-07-15 17:01:49.434 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:5006 | 🔧 [异步分页] 开始应用数据格式化处理
2025-07-15 17:01:49.434 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:238 | 开始格式化工资表数据 - 表类型: salary_data_2025_07_active_employees
2025-07-15 17:01:49.434 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:251 | 原始数据: 50行, 24列
2025-07-15 17:01:49.434 | INFO     | src.modules.data_storage.salary_data_formatter:_extract_table_type_from_name:335 | 🔧 [表名映射] 通过关键词 'active_employees' 匹配到表类型: active_employees
2025-07-15 17:01:49.434 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:255 | 🔧 [表名映射] 输入表名: salary_data_2025_07_active_employees, 提取的表类型: active_employees
2025-07-15 17:01:49.434 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:259 | 使用表类型配置: active_employees, 配置字段: ['id_fields', 'string_fields', 'integer_fields', 'float_fields', 'special_fields']
2025-07-15 17:01:49.434 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:263 | 🔧 [格式化调试] 开始处理ID字段: ['employee_id', 'employee_type_code', '工号', '人员类别代码']
2025-07-15 17:01:49.434 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:268 | 🔧 [格式化调试] 处理前 工号 字段样例: ['19990089', '20161565', '20191782']
2025-07-15 17:01:49.434 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:268 | 🔧 [格式化调试] 处理前 人员类别代码 字段样例: ['01', '01', '17']
2025-07-15 17:01:49.434 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_id_fields:461 | 🔧 [警告] ID字段 employee_id 不存在于数据框中
2025-07-15 17:01:49.434 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_id_fields:461 | 🔧 [警告] ID字段 employee_type_code 不存在于数据框中
2025-07-15 17:01:49.449 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:377 | 🔧 [强化调试] 处理ID字段 工号: 原始样例=['19990089', '20161565', '20191782'], 原始类型=object
2025-07-15 17:01:49.449 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:426 | 🔧 [强化调试] 清理后ID字段 工号: 样例=['19990089', '20161565', '20191782']
2025-07-15 17:01:49.449 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:443 | 🔧 [强化调试] 最终ID字段 工号: 样例=['19990089', '20161565', '20191782']
2025-07-15 17:01:49.449 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:454 | 🔧 [验证成功] ID字段 工号 格式化完成，无小数点问题
2025-07-15 17:01:49.449 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:377 | 🔧 [强化调试] 处理ID字段 人员类别代码: 原始样例=['01', '01', '17'], 原始类型=object
2025-07-15 17:01:49.449 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:426 | 🔧 [强化调试] 清理后ID字段 人员类别代码: 样例=['01', '01', '17']
2025-07-15 17:01:49.449 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:443 | 🔧 [强化调试] 最终ID字段 人员类别代码: 样例=['01', '01', '17']
2025-07-15 17:01:49.449 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:454 | 🔧 [验证成功] ID字段 人员类别代码 格式化完成，无小数点问题
2025-07-15 17:01:49.464 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:274 | 🔧 [格式化调试] 处理后 工号 字段样例: ['19990089', '20161565', '20191782']
2025-07-15 17:01:49.464 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:274 | 🔧 [格式化调试] 处理后 人员类别代码 字段样例: ['01', '01', '17']
2025-07-15 17:01:49.464 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:472 | 🔧 [强化调试] 开始格式化字符串字段: ['employee_id', 'employee_name', 'department', 'employee_type_code', 'employee_type', '工号', '姓名', '部门名称', '人员类别代码', '人员类别']
2025-07-15 17:01:49.464 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 employee_id 不存在于数据框中
2025-07-15 17:01:49.464 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 employee_name 不存在于数据框中
2025-07-15 17:01:49.464 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 department 不存在于数据框中
2025-07-15 17:01:49.464 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 employee_type_code 不存在于数据框中
2025-07-15 17:01:49.464 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 employee_type 不存在于数据框中
2025-07-15 17:01:49.464 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 工号: 原始样例=['19990089', '20161565', '20191782'], 原始类型=object
2025-07-15 17:01:49.464 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:504 | 🔧 [修复] 工号字段 工号 已特殊处理，去除小数点格式
2025-07-15 17:01:49.464 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 工号: 样例=['19990089', '20161565', '20191782'], 类型=object
2025-07-15 17:01:49.464 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 工号 格式化完成，无小数点问题
2025-07-15 17:01:49.464 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 姓名: 原始样例=['杨胜', '胡四平', '肖啸'], 原始类型=object
2025-07-15 17:01:49.464 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 姓名: 样例=['杨胜', '胡四平', '肖啸'], 类型=object
2025-07-15 17:01:49.464 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 姓名 格式化完成，无小数点问题
2025-07-15 17:01:49.464 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 部门名称: 原始样例=['自动化学院', '自动化学院', '自动化学院'], 原始类型=object
2025-07-15 17:01:49.464 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 部门名称: 样例=['自动化学院', '自动化学院', '自动化学院'], 类型=object
2025-07-15 17:01:49.464 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 部门名称 格式化完成，无小数点问题
2025-07-15 17:01:49.464 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 人员类别代码: 原始样例=['01', '01', '17'], 原始类型=object
2025-07-15 17:01:49.464 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 人员类别代码: 样例=['01', '01', '17'], 类型=object
2025-07-15 17:01:49.464 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 人员类别代码 格式化完成，无小数点问题
2025-07-15 17:01:49.464 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 人员类别: 原始样例=['教学院其它人员', '教学单位专技人员', '科研单位人员'], 原始类型=object
2025-07-15 17:01:49.480 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 人员类别: 样例=['教学院其它人员', '教学单位专技人员', '科研单位人员'], 类型=object
2025-07-15 17:01:49.480 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 人员类别 格式化完成，无小数点问题
2025-07-15 17:01:49.480 | INFO     | src.modules.data_storage.salary_data_formatter:_format_integer_fields:604 | 🔧 [强化调试] 开始格式化整型字段: []
2025-07-15 17:01:49.480 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:655 | 🔧 [强化调试] 开始格式化浮点型字段: ['position_salary_2025', 'grade_salary_2025', 'allowance', 'balance_allowance', 'basic_performance_2025', 'health_fee', 'transport_allowance', 'property_allowance', 'communication_allowance', 'performance_bonus_2025', 'provident_fund_2025', 'housing_allowance', 'car_allowance', 'supplement', 'advance', 'total_salary', 'pension_insurance', '2025年岗位工资', '2025年薪级工资', '津贴', '结余津贴', '2025年基础性绩效', '卫生费', '交通补贴', '物业补贴', '通讯补贴', '2025年奖励性绩效预发', '2025公积金', '住房补贴', '车补', '补发', '借支', '应发工资', '代扣代存养老保险']
2025-07-15 17:01:49.480 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 position_salary_2025 不存在于数据框中
2025-07-15 17:01:49.480 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 grade_salary_2025 不存在于数据框中
2025-07-15 17:01:49.480 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 allowance 不存在于数据框中
2025-07-15 17:01:49.480 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 balance_allowance 不存在于数据框中
2025-07-15 17:01:49.480 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 basic_performance_2025 不存在于数据框中
2025-07-15 17:01:49.480 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 health_fee 不存在于数据框中
2025-07-15 17:01:49.480 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 transport_allowance 不存在于数据框中
2025-07-15 17:01:49.480 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 property_allowance 不存在于数据框中
2025-07-15 17:01:49.480 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 communication_allowance 不存在于数据框中
2025-07-15 17:01:49.480 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 performance_bonus_2025 不存在于数据框中
2025-07-15 17:01:49.480 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 provident_fund_2025 不存在于数据框中
2025-07-15 17:01:49.480 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 housing_allowance 不存在于数据框中
2025-07-15 17:01:49.480 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 car_allowance 不存在于数据框中
2025-07-15 17:01:49.480 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 supplement 不存在于数据框中
2025-07-15 17:01:49.497 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 advance 不存在于数据框中
2025-07-15 17:01:49.497 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 total_salary 不存在于数据框中
2025-07-15 17:01:49.497 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 pension_insurance 不存在于数据框中
2025-07-15 17:01:49.497 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025年岗位工资: 原始样例=[2880.0, 3030.0, 2185.0], 原始类型=float64
2025-07-15 17:01:49.497 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025年岗位工资: 样例=[2880.0, 3030.0, 2185.0], 类型=float64
2025-07-15 17:01:49.497 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025年岗位工资 格式化完成，保留两位小数
2025-07-15 17:01:49.497 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025年薪级工资: 原始样例=[2375.0, 1696.0, 1427.0], 原始类型=float64
2025-07-15 17:01:49.497 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025年薪级工资: 样例=[2375.0, 1696.0, 1427.0], 类型=float64
2025-07-15 17:01:49.497 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025年薪级工资 格式化完成，保留两位小数
2025-07-15 17:01:49.497 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 津贴: 原始样例=[102.0, 0.0, 0.0], 原始类型=float64
2025-07-15 17:01:49.497 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 津贴: 样例=[102.0, 0.0, 0.0], 类型=float64
2025-07-15 17:01:49.497 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 津贴 格式化完成，保留两位小数
2025-07-15 17:01:49.497 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 结余津贴: 原始样例=[56.0, 56.0, 56.0], 原始类型=float64
2025-07-15 17:01:49.497 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 结余津贴: 样例=[56.0, 56.0, 56.0], 类型=float64
2025-07-15 17:01:49.497 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 结余津贴 格式化完成，保留两位小数
2025-07-15 17:01:49.497 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025年基础性绩效: 原始样例=[3594.0, 3466.0, 2978.0], 原始类型=float64
2025-07-15 17:01:49.497 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025年基础性绩效: 样例=[3594.0, 3466.0, 2978.0], 类型=float64
2025-07-15 17:01:49.497 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025年基础性绩效 格式化完成，保留两位小数
2025-07-15 17:01:49.497 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 卫生费: 原始样例=[0.0, 0.0, 0.0], 原始类型=float64
2025-07-15 17:01:49.497 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 卫生费: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-15 17:01:49.497 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 卫生费 格式化完成，保留两位小数
2025-07-15 17:01:49.497 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 交通补贴: 原始样例=[220.0, 220.0, 220.0], 原始类型=float64
2025-07-15 17:01:49.497 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 交通补贴: 样例=[220.0, 220.0, 220.0], 类型=float64
2025-07-15 17:01:49.511 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 交通补贴 格式化完成，保留两位小数
2025-07-15 17:01:49.511 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 物业补贴: 原始样例=[240.0, 240.0, 200.0], 原始类型=float64
2025-07-15 17:01:49.511 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 物业补贴: 样例=[240.0, 240.0, 200.0], 类型=float64
2025-07-15 17:01:49.511 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 物业补贴 格式化完成，保留两位小数
2025-07-15 17:01:49.511 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 通讯补贴: 原始样例=[50.0, 0.0, 0.0], 原始类型=float64
2025-07-15 17:01:49.511 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 通讯补贴: 样例=[50.0, 0.0, 0.0], 类型=float64
2025-07-15 17:01:49.511 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 通讯补贴 格式化完成，保留两位小数
2025-07-15 17:01:49.511 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025年奖励性绩效预发: 原始样例=[2500.0, 1000.0, 1000.0], 原始类型=float64
2025-07-15 17:01:49.526 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025年奖励性绩效预发: 样例=[2500.0, 1000.0, 1000.0], 类型=float64
2025-07-15 17:01:49.526 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025年奖励性绩效预发 格式化完成，保留两位小数
2025-07-15 17:01:49.526 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025公积金: 原始样例=[2097.0, 1860.0, 1984.0], 原始类型=float64
2025-07-15 17:01:49.526 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025公积金: 样例=[2097.0, 1860.0, 1984.0], 类型=float64
2025-07-15 17:01:49.526 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025公积金 格式化完成，保留两位小数
2025-07-15 17:01:49.526 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 住房补贴: 原始样例=[271.97, 189.0, 174.16], 原始类型=float64
2025-07-15 17:01:49.526 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 住房补贴: 样例=[271.97, 189.0, 174.16], 类型=float64
2025-07-15 17:01:49.526 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 住房补贴 格式化完成，保留两位小数
2025-07-15 17:01:49.526 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 车补: 原始样例=[0.0, 0.0, 0.0], 原始类型=float64
2025-07-15 17:01:49.526 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 车补: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-15 17:01:49.526 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 车补 格式化完成，保留两位小数
2025-07-15 17:01:49.526 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 补发: 原始样例=[0.0, 0.0, 0.0], 原始类型=float64
2025-07-15 17:01:49.526 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 补发: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-15 17:01:49.526 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 补发 格式化完成，保留两位小数
2025-07-15 17:01:49.526 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 借支: 原始样例=[0.0, 0.0, 2000.0], 原始类型=float64
2025-07-15 17:01:49.526 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 借支: 样例=[0.0, 0.0, 2000.0], 类型=float64
2025-07-15 17:01:49.526 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 借支 格式化完成，保留两位小数
2025-07-15 17:01:49.526 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 应发工资: 原始样例=[12288.97, 9897.0, 6240.16], 原始类型=float64
2025-07-15 17:01:49.526 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 应发工资: 样例=[12288.97, 9897.0, 6240.16], 类型=float64
2025-07-15 17:01:49.526 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 应发工资 格式化完成，保留两位小数
2025-07-15 17:01:49.526 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 代扣代存养老保险: 原始样例=[1525.8, 1140.53, 1113.75], 原始类型=float64
2025-07-15 17:01:49.526 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 代扣代存养老保险: 样例=[1525.8, 1140.53, 1113.75], 类型=float64
2025-07-15 17:01:49.526 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 代扣代存养老保险 格式化完成，保留两位小数
2025-07-15 17:01:49.526 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:711 | 🔧 [特殊字段] 开始处理表类型 active_employees 的月份、年份字段
2025-07-15 17:01:49.526 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:866 | 🔧 [特殊字段] 月份、年份字段处理完成
2025-07-15 17:01:49.542 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:291 | 数据格式化完成: 50行, 24列
2025-07-15 17:01:49.542 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:5008 | 🔧 [异步分页] 数据格式化成功: 50行, 24列
2025-07-15 17:01:49.542 | INFO     | src.gui.prototype.prototype_main_window:set_data:635 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-07-15 17:01:49.658 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:133 | 使用已存在的配置文件: state/data/field_mappings.json
2025-07-15 17:01:49.658 | INFO     | src.modules.data_import.config_sync_manager:__init__:70 | 配置同步管理器初始化完成
2025-07-15 17:01:49.658 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:1146 | 🔧 [修复] 应用字段映射: 24个字段
2025-07-15 17:01:49.766 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:133 | 使用已存在的配置文件: state/data/field_mappings.json
2025-07-15 17:01:49.766 | INFO     | src.modules.data_import.config_sync_manager:__init__:70 | 配置同步管理器初始化完成
2025-07-15 17:01:49.766 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:1220 | 🔧 [修复] 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-15 17:01:49.798 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3560 | 最大可见行数已更新: 50 -> 50
2025-07-15 17:01:49.798 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:3613 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-15 17:01:49.798 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2237 | 🔧 [修复标识] 字段映射生效，样例: ['employee_id', 'employee_name', 'department'] -> ['工号', '姓名', '部门名称']
2025-07-15 17:01:49.798 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第0行数据工号: 19990089
2025-07-15 17:01:49.798 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第1行数据工号: 20161565
2025-07-15 17:01:49.798 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第2行数据工号: 20191782
2025-07-15 17:01:49.798 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第3行数据工号: 20151515
2025-07-15 17:01:49.798 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第4行数据工号: 20181640
2025-07-15 17:01:49.798 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:457 | 🔧 [排序修复] 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-15 17:01:49.798 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:424 | 表格数据已设置: 50 行, 24 列
2025-07-15 17:01:49.798 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2363 | 🔧 [表格调试] 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-15 17:01:49.798 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2370 | 🔧 [表格调试] ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-15 17:01:49.798 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2363 | 🔧 [表格调试] 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-15 17:01:49.798 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2370 | 🔧 [表格调试] ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-15 17:01:49.829 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2174 | 🔧 [性能分析] 表格数据设置完成: 50 行, 最大可见行数: 50, 总耗时: 62.72ms
2025-07-15 17:01:49.829 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 初始化和状态: 31.40ms (50.1%)
2025-07-15 17:01:49.829 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 字段映射: 0.00ms (0.0%)
2025-07-15 17:01:49.829 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 数据模型更新: 0.00ms (0.0%)
2025-07-15 17:01:49.829 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 表头设置: 0.00ms (0.0%)
2025-07-15 17:01:49.829 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 设置行数: 0.00ms (0.0%)
2025-07-15 17:01:49.829 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 填充可见数据: 31.32ms (49.9%)
2025-07-15 17:01:49.829 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 调整列宽: 0.00ms (0.0%)
2025-07-15 17:01:49.829 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 设置行号: 0.00ms (0.0%)
2025-07-15 17:01:49.829 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2182 | 🔧 [性能分析] 最耗时步骤: 初始化和状态 (31.40ms)
2025-07-15 17:01:49.829 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 50
2025-07-15 17:01:49.829 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-15 17:01:49.829 | INFO     | src.gui.multi_column_sort_manager:add_sort_column:274 | 添加排序列: 6(grade_salary_2025) -> ascending, 当前排序列数: 1
2025-07-15 17:01:49.829 | INFO     | src.gui.multi_column_sort_manager:_on_sort_indicator_changed:211 | 排序指示器变化: 列6(grade_salary_2025) -> ascending
2025-07-15 17:01:49.829 | INFO     | src.gui.prototype.prototype_main_window:_on_sort_indicator_changed:3112 | 🔧 [全局排序] 排序请求: 列6, 顺序ASC
2025-07-15 17:01:49.829 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:3259 | 🔧 [事件排序] 已发布排序请求事件: salary_data_2025_07_active_employees, 1列
2025-07-15 17:01:49.829 | INFO     | src.services.table_data_service:_handle_sort_request:135 | 处理排序请求: salary_data_2025_07_active_employees
2025-07-15 17:01:49.829 | INFO     | src.gui.prototype.prototype_main_window:_on_sort_indicator_changed:3144 | 🔧 [新架构排序] 排序完成: 2025年薪级工资 ascending, 保持第1页
2025-07-15 17:01:49.829 | INFO     | src.core.unified_data_request_manager:request_table_data:177 | 开始处理数据请求: salary_data_2025_07_active_employees, 类型: sort_change
2025-07-15 17:01:49.829 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:634 | 🔧 [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_07_active_employees' 中（类型: REAL）
2025-07-15 17:01:49.829 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:487 | 正在从表 salary_data_2025_07_active_employees 分页获取数据（支持排序）: 第1页, 每页50条, 排序=1列
2025-07-15 17:01:49.829 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:516 | 🔧 [排序修复] 准备排序列: grade_salary_2025
2025-07-15 17:01:49.845 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:634 | 🔧 [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_07_active_employees' 中（类型: REAL）
2025-07-15 17:01:49.845 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:523 | 🔧 [排序修复] 列 'grade_salary_2025' 验证通过，开始排序
2025-07-15 17:01:49.845 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:532 | 🔧 [排序修复] 数值列排序: grade_salary_2025 ASC
2025-07-15 17:01:49.845 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:539 | 🔧 [排序修复] 添加排序子句: CAST("grade_salary_2025" AS REAL) ASC
2025-07-15 17:01:49.845 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:547 | 🔧 [排序调试] 执行的完整SQL查询: SELECT * FROM "salary_data_2025_07_active_employees" ORDER BY CAST("grade_salary_2025" AS REAL) ASC LIMIT 50 OFFSET 0
2025-07-15 17:01:49.872 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:559 | 🔧 [排序调试] 查询结果中 grade_salary_2025 的前10个值: [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 596.0]
2025-07-15 17:01:49.872 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:561 | 成功从表 salary_data_2025_07_active_employees 获取第1页数据（含排序）: 50 行，总计1396行
2025-07-15 17:01:49.872 | INFO     | src.core.unified_data_request_manager:request_table_data:226 | 数据请求处理完成: 28字段, 50行, 耗时42.6ms
2025-07-15 17:01:49.872 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:2982 | 🆕 接收到新架构数据更新事件: salary_data_2025_07_active_employees
2025-07-15 17:01:49.872 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3007 | 数据内容: 50行 x 28列
2025-07-15 17:01:49.872 | INFO     | src.gui.prototype.prototype_main_window:set_data:635 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-07-15 17:01:49.978 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:133 | 使用已存在的配置文件: state/data/field_mappings.json
2025-07-15 17:01:49.978 | INFO     | src.modules.data_import.config_sync_manager:__init__:70 | 配置同步管理器初始化完成
2025-07-15 17:01:49.978 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:1151 | 🔧 [修复] 没有找到匹配的字段映射
2025-07-15 17:01:49.978 | INFO     | src.gui.prototype.prototype_main_window:_apply_system_field_filtering:1183 | 🔧 [修复] 过滤了 4 个系统字段
2025-07-15 17:01:50.089 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:133 | 使用已存在的配置文件: state/data/field_mappings.json
2025-07-15 17:01:50.089 | INFO     | src.modules.data_import.config_sync_manager:__init__:70 | 配置同步管理器初始化完成
2025-07-15 17:01:50.089 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:1220 | 🔧 [修复] 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-15 17:01:50.105 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3560 | 最大可见行数已更新: 50 -> 50
2025-07-15 17:01:50.105 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:3613 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-15 17:01:50.105 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2237 | 🔧 [修复标识] 字段映射生效，样例: ['employee_id', 'employee_name', 'department'] -> ['工号', '姓名', '部门名称']
2025-07-15 17:01:50.105 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第0行数据工号: 19961347.0
2025-07-15 17:01:50.105 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第1行数据工号: 20251003.0
2025-07-15 17:01:50.105 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第2行数据工号: 20251006.0
2025-07-15 17:01:50.105 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第3行数据工号: 20251007.0
2025-07-15 17:01:50.105 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第4行数据工号: 20251008.0
2025-07-15 17:01:50.105 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:457 | 🔧 [排序修复] 可见行数据顺序: ['19961347.0', '20251003.0', '20251006.0', '20251007.0', '20251008.0']
2025-07-15 17:01:50.120 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:424 | 表格数据已设置: 50 行, 24 列
2025-07-15 17:01:50.120 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2363 | 🔧 [表格调试] 开始格式化ID字段: 列=工号, 原值=19961347.0, 类型=<class 'str'>
2025-07-15 17:01:50.120 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2370 | 🔧 [表格调试] ID字段格式化完成: 列=工号, 原值=19961347.0, 格式化后=19961347.0
2025-07-15 17:01:50.120 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2363 | 🔧 [表格调试] 开始格式化ID字段: 列=人员类别代码, 原值=17.0, 类型=<class 'str'>
2025-07-15 17:01:50.120 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2370 | 🔧 [表格调试] ID字段格式化完成: 列=人员类别代码, 原值=17.0, 格式化后=17.0
2025-07-15 17:01:50.152 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2174 | 🔧 [性能分析] 表格数据设置完成: 50 行, 最大可见行数: 50, 总耗时: 62.73ms
2025-07-15 17:01:50.152 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 初始化和状态: 15.76ms (25.1%)
2025-07-15 17:01:50.152 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 字段映射: 0.00ms (0.0%)
2025-07-15 17:01:50.152 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 数据模型更新: 15.67ms (25.0%)
2025-07-15 17:01:50.152 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 表头设置: 0.00ms (0.0%)
2025-07-15 17:01:50.152 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 设置行数: 0.00ms (0.0%)
2025-07-15 17:01:50.152 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 填充可见数据: 31.30ms (49.9%)
2025-07-15 17:01:50.152 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 调整列宽: 0.00ms (0.0%)
2025-07-15 17:01:50.152 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2177 | 🔧 [性能分析] - 设置行号: 0.00ms (0.0%)
2025-07-15 17:01:50.152 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2182 | 🔧 [性能分析] 最耗时步骤: 填充可见数据 (31.30ms)
2025-07-15 17:01:50.168 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 50
2025-07-15 17:01:50.168 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-15 17:01:50.168 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3033 | 🔧 [分页修复] 数据更新事件设置总记录数: 1396
2025-07-15 17:01:50.168 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3044 | 🔧 [分页修复] 分页状态: 当前页=1, 总页数=28, 总记录数=1396
2025-07-15 17:01:50.168 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3050 | 🔧 [分页修复] 按钮可用性: 下一页=True, 上一页=False
