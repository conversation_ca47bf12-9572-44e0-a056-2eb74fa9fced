2025-07-15 19:00:56.815 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-07-15 19:00:56.815 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-07-15 19:00:56.815 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-07-15 19:00:56.815 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-07-15 19:00:56.815 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-07-15 19:00:56.815 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-07-15 19:00:58.959 | INFO     | __main__:setup_app_logging:239 | 月度工资异动处理系统 v2.0.0-refactored 启动
2025-07-15 19:00:58.959 | INFO     | __main__:main:303 | 初始化核心管理器...
2025-07-15 19:00:58.959 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-07-15 19:00:58.959 | INFO     | src.modules.system_config.config_manager:load_config:338 | 正在加载配置文件: C:\test\salary_changes\salary_changes\config.json
2025-07-15 19:00:58.959 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:252 | 配置文件验证通过
2025-07-15 19:00:58.959 | INFO     | src.modules.system_config.config_manager:load_config:350 | 配置文件加载成功
2025-07-15 19:00:58.959 | INFO     | src.modules.data_storage.database_manager:_initialize_database:101 | 开始初始化数据库...
2025-07-15 19:00:59.006 | INFO     | src.modules.data_storage.database_manager:_initialize_database:156 | 数据库初始化完成
2025-07-15 19:00:59.006 | INFO     | src.modules.data_storage.database_manager:__init__:97 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-07-15 19:00:59.006 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-07-15 19:00:59.006 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:104 | 动态表管理器初始化完成
2025-07-15 19:00:59.006 | INFO     | __main__:main:308 | 核心管理器初始化完成。
2025-07-15 19:00:59.006 | INFO     | src.core.data_source_unification_manager:__init__:128 | 数据源统一管理器初始化完成
2025-07-15 19:00:59.006 | INFO     | src.core.table_sort_state_manager:__init__:174 | 表级排序状态管理器初始化完成
2025-07-15 19:00:59.068 | INFO     | src.core.architecture_factory:__init__:62 | 架构重构工厂初始化完成
2025-07-15 19:00:59.068 | INFO     | src.core.architecture_factory:initialize_architecture:72 | 开始初始化架构重构系统...
2025-07-15 19:00:59.189 | WARNING  | src.modules.data_import.config_sync_manager:_do_config_initialization:129 | 创建了最小默认配置，可能需要重新导入数据以生成字段映射
2025-07-15 19:00:59.193 | INFO     | src.modules.data_import.config_sync_manager:__init__:70 | 配置同步管理器初始化完成
2025-07-15 19:00:59.195 | INFO     | src.core.event_bus:__init__:227 | 事件总线初始化完成
2025-07-15 19:00:59.196 | INFO     | src.core.unified_state_manager:_load_state:435 | 状态文件不存在，使用默认状态
2025-07-15 19:00:59.196 | INFO     | src.core.unified_state_manager:__init__:177 | 统一状态管理器初始化完成
2025-07-15 19:00:59.196 | INFO     | src.core.unified_data_request_manager:__init__:164 | 统一数据请求管理器初始化完成
2025-07-15 19:00:59.197 | INFO     | src.core.unified_data_request_manager:__init__:164 | 统一数据请求管理器初始化完成
2025-07-15 19:00:59.197 | INFO     | src.core.unified_state_manager:_load_state:435 | 状态文件不存在，使用默认状态
2025-07-15 19:00:59.198 | INFO     | src.core.unified_state_manager:__init__:177 | 统一状态管理器初始化完成
2025-07-15 19:00:59.198 | INFO     | src.services.table_data_service:__init__:68 | 表格数据服务初始化完成
2025-07-15 19:00:59.199 | INFO     | src.core.architecture_factory:initialize_architecture:103 | 🎉 架构重构系统初始化成功！耗时: 130.8ms
2025-07-15 19:00:59.199 | INFO     | src.gui.prototype.prototype_main_window:__init__:2842 | ✅ 新架构集成成功！
2025-07-15 19:00:59.201 | INFO     | src.gui.prototype.prototype_main_window:_inject_config_sync_manager_to_existing_tables:2960 | 🔧 [修复标识] ConfigSyncManager重新注入完成，已更新0个表格实例
2025-07-15 19:00:59.204 | INFO     | src.gui.prototype.prototype_main_window:_setup_new_architecture_listeners:2926 | ✅ 新架构事件监听器设置完成
2025-07-15 19:00:59.208 | INFO     | src.gui.widgets.pagination_cache_manager:__init__:107 | 分页缓存管理器初始化完成 - 最大条目数: 50, 最大内存: 50MB
2025-07-15 19:00:59.209 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:121 | 响应式布局管理器初始化完成
2025-07-15 19:00:59.209 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初始化完成
2025-07-15 19:00:59.431 | INFO     | src.gui.prototype.prototype_main_window:_create_menu_bar:2129 | 菜单栏创建完成
2025-07-15 19:00:59.431 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-07-15 19:00:59.431 | INFO     | src.modules.system_config.user_preferences:load_preferences:183 | 正在加载偏好设置: C:\test\salary_changes\salary_changes\user_preferences.json
2025-07-15 19:00:59.431 | INFO     | src.modules.system_config.user_preferences:load_preferences:193 | 偏好设置加载成功
2025-07-15 19:00:59.431 | INFO     | src.gui.prototype.prototype_main_window:__init__:2105 | 菜单栏管理器初始化完成
2025-07-15 19:00:59.431 | INFO     | src.gui.table_header_manager:__init__:68 | 表头管理器初始化完成
2025-07-15 19:00:59.431 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:3664 | 管理器设置完成，包含增强版表头管理器
2025-07-15 19:00:59.431 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-07-15 19:00:59.447 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:148 | 智能树形展开算法初始化完成
2025-07-15 19:00:59.447 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:1042 | 开始从元数据动态加载工资数据...
2025-07-15 19:00:59.447 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:1049 | 检测到首次启动，将延迟加载导航数据确保数据库就绪
2025-07-15 19:00:59.458 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:680 | 导航面板已重构：移除功能性导航，专注数据导航
2025-07-15 19:00:59.458 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:715 | 恢复导航状态: 0个展开项
2025-07-15 19:00:59.458 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表']
2025-07-15 19:00:59.462 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:521 | 增强导航面板初始化完成
2025-07-15 19:00:59.486 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1312 | 快捷键注册完成: 18/18 个
2025-07-15 19:00:59.487 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1523 | 拖拽排序管理器初始化完成
2025-07-15 19:00:59.491 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1892 | ConfigSyncManager未通过依赖注入提供，使用降级方案
2025-07-15 19:00:59.596 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:133 | 使用已存在的配置文件: state/data/field_mappings.json
2025-07-15 19:00:59.598 | INFO     | src.modules.data_import.config_sync_manager:__init__:70 | 配置同步管理器初始化完成
2025-07-15 19:00:59.600 | INFO     | src.modules.data_import.header_edit_manager:__init__:81 | 表头编辑管理器初始化完成
2025-07-15 19:00:59.612 | INFO     | src.gui.multi_column_sort_manager:__init__:103 | 多列排序管理器初始化完成，最大排序列数: 3
2025-07-15 19:00:59.612 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1917 | 多列排序管理器初始化完成
2025-07-15 19:00:59.614 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1934 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数: 1000
2025-07-15 19:00:59.614 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:3587 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-07-15 19:00:59.615 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:424 | 表格数据已设置: 0 行, 7 列
2025-07-15 19:00:59.618 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2172 | 🔧 [性能分析] 表格数据设置完成: 0 行, 最大可见行数: 1000, 总耗时: 4.15ms
2025-07-15 19:00:59.619 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 初始化和状态: 1.06ms (25.6%)
2025-07-15 19:00:59.624 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 字段映射: 0.00ms (0.0%)
2025-07-15 19:00:59.624 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 数据模型更新: 1.04ms (25.0%)
2025-07-15 19:00:59.625 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 表头设置: 1.02ms (24.5%)
2025-07-15 19:00:59.626 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 设置行数: 0.00ms (0.0%)
2025-07-15 19:00:59.627 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 填充可见数据: 0.00ms (0.0%)
2025-07-15 19:00:59.628 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 调整列宽: 0.00ms (0.0%)
2025-07-15 19:00:59.628 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 设置行号: 1.03ms (24.9%)
2025-07-15 19:00:59.630 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2180 | 🔧 [性能分析] 最耗时步骤: 初始化和状态 (1.06ms)
2025-07-15 19:00:59.633 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1704 | 表格已初始化为空白状态，等待用户导入数据
2025-07-15 19:00:59.643 | INFO     | src.gui.widgets.pagination_widget:__init__:165 | 分页组件初始化完成
2025-07-15 19:00:59.667 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:512 | 控制面板按钮信号连接完成
2025-07-15 19:00:59.680 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-07-15 19:00:59.681 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-07-15 19:00:59.683 | INFO     | src.gui.prototype.prototype_main_window:_setup_shortcuts:3637 | 快捷键设置完成
2025-07-15 19:00:59.685 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:3626 | 主窗口UI设置完成。
2025-07-15 19:00:59.685 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:3675 | 🔧 [全局排序] 全局排序开关连接成功
2025-07-15 19:00:59.686 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:3710 | ✅ 已连接排序指示器变化信号到新架构
2025-07-15 19:00:59.687 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:3731 | ✅ 已连接分页组件事件到新架构
2025-07-15 19:00:59.687 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:3733 | 信号连接设置完成
2025-07-15 19:00:59.688 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:4449 | 已加载字段映射信息，共0个表的映射
2025-07-15 19:00:59.688 | WARNING  | src.gui.prototype.prototype_main_window:set_data:602 | 尝试设置空数据，将显示提示信息。
2025-07-15 19:00:59.691 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:3587 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-07-15 19:00:59.701 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:424 | 表格数据已设置: 0 行, 7 列
2025-07-15 19:00:59.702 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2172 | 🔧 [性能分析] 表格数据设置完成: 0 行, 最大可见行数: 1000, 总耗时: 11.90ms
2025-07-15 19:00:59.703 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 初始化和状态: 10.84ms (91.1%)
2025-07-15 19:00:59.704 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 字段映射: 0.00ms (0.0%)
2025-07-15 19:00:59.705 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 数据模型更新: 1.06ms (8.9%)
2025-07-15 19:00:59.706 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 表头设置: 0.00ms (0.0%)
2025-07-15 19:00:59.707 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 设置行数: 0.00ms (0.0%)
2025-07-15 19:00:59.708 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 填充可见数据: 0.00ms (0.0%)
2025-07-15 19:00:59.708 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 调整列宽: 0.00ms (0.0%)
2025-07-15 19:00:59.709 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 设置行号: 0.00ms (0.0%)
2025-07-15 19:00:59.716 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2180 | 🔧 [性能分析] 最耗时步骤: 初始化和状态 (10.84ms)
2025-07-15 19:00:59.717 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1704 | 表格已初始化为空白状态，等待用户导入数据
2025-07-15 19:00:59.717 | INFO     | src.gui.widgets.pagination_widget:reset:354 | 分页状态已重置
2025-07-15 19:00:59.718 | WARNING  | src.gui.prototype.prototype_main_window:set_data:602 | 尝试设置空数据，将显示提示信息。
2025-07-15 19:00:59.719 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:3587 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-07-15 19:00:59.720 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:424 | 表格数据已设置: 0 行, 7 列
2025-07-15 19:00:59.722 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2172 | 🔧 [性能分析] 表格数据设置完成: 0 行, 最大可见行数: 1000, 总耗时: 3.13ms
2025-07-15 19:00:59.723 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 初始化和状态: 0.00ms (0.0%)
2025-07-15 19:00:59.729 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 字段映射: 1.08ms (34.6%)
2025-07-15 19:00:59.730 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 数据模型更新: 1.03ms (32.9%)
2025-07-15 19:00:59.731 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 表头设置: 1.02ms (32.5%)
2025-07-15 19:00:59.731 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 设置行数: 0.00ms (0.0%)
2025-07-15 19:00:59.732 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 填充可见数据: 0.00ms (0.0%)
2025-07-15 19:00:59.732 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 调整列宽: 0.00ms (0.0%)
2025-07-15 19:00:59.733 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 设置行号: 0.00ms (0.0%)
2025-07-15 19:00:59.734 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2180 | 🔧 [性能分析] 最耗时步骤: 字段映射 (1.08ms)
2025-07-15 19:00:59.735 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1704 | 表格已初始化为空白状态，等待用户导入数据
2025-07-15 19:00:59.736 | INFO     | src.gui.widgets.pagination_widget:reset:354 | 分页状态已重置
2025-07-15 19:00:59.743 | INFO     | src.gui.prototype.prototype_main_window:_setup_legacy_compatibility:6218 | ✅ 老架构兼容性接口设置完成
2025-07-15 19:00:59.744 | INFO     | src.gui.prototype.prototype_main_window:__init__:2900 | 原型主窗口初始化完成
2025-07-15 19:00:59.979 | INFO     | __main__:main:330 | 应用程序启动成功
2025-07-15 19:00:59.987 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: sm (宽度: 1266px)
2025-07-15 19:00:59.987 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:1852 | MainWorkspaceArea 响应式适配: sm
2025-07-15 19:00:59.990 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1003 | 执行延迟的自动选择最新数据...
2025-07-15 19:00:59.990 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1014 | 导航树即将刷新，跳过当前自动选择，将在刷新后执行
2025-07-15 19:01:00.247 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_load_salary_data:1064 | 执行延迟的工资数据加载...
2025-07-15 19:01:00.247 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1230 | 开始强制刷新工资数据导航... (尝试 1/3)
2025-07-15 19:01:00.247 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1238 | 找到工资表节点: 📊 工资表
2025-07-15 19:01:00.247 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_table_list:873 | 首次查询发现表数量异常: 0 个表，期望 1 个，可能需要重试
2025-07-15 19:01:00.362 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:881 | 找到 0 个匹配类型 'salary_data' 的表
2025-07-15 19:01:00.362 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_navigation_tree_data:972 | 在 table_metadata 中未找到任何 'salary_data' 类型的表
2025-07-15 19:01:00.362 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:884 | 找到 3 个总表
2025-07-15 19:01:00.362 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1290 | 检测到数据库中没有工资数据表，直接使用兜底数据
2025-07-15 19:01:00.362 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_load_fallback_salary_data:1187 | 使用兜底数据加载导航
2025-07-15 19:01:00.563 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_post_refresh_auto_select:1093 | 导航树刷新完成，重新执行自动选择...
2025-07-15 19:01:00.563 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1032 | 开始获取最新工资数据路径...
2025-07-15 19:01:00.563 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_table_list:873 | 首次查询发现表数量异常: 0 个表，期望 1 个，可能需要重试
2025-07-15 19:01:00.668 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:881 | 找到 0 个匹配类型 'salary_data' 的表
2025-07-15 19:01:00.668 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1037 | 未找到任何工资数据表
2025-07-15 19:01:00.668 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_post_refresh_auto_select:1098 | 未找到最新工资数据路径
2025-07-15 19:01:01.903 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data:529 | 数据导入功能被触发，发出 import_requested 信号。
2025-07-15 19:01:01.903 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:3965 | 接收到数据导入请求，推断的目标路径: 工资表 > 2025年 > 07月 > 全部在职人员。打开导入对话框。
2025-07-15 19:01:01.919 | INFO     | src.modules.data_import.auto_field_mapping_generator:__init__:60 | 自动字段映射生成器初始化完成
2025-07-15 19:01:02.023 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:133 | 使用已存在的配置文件: state/data/field_mappings.json
2025-07-15 19:01:02.023 | INFO     | src.modules.data_import.config_sync_manager:__init__:70 | 配置同步管理器初始化完成
2025-07-15 19:01:02.023 | INFO     | src.modules.data_import.multi_sheet_importer:__init__:70 | 多Sheet导入器初始化完成
2025-07-15 19:01:02.023 | INFO     | src.gui.widgets.target_selection_widget:_save_config:285 | 配置保存成功
2025-07-15 19:01:02.023 | INFO     | src.gui.widgets.target_selection_widget:set_target_from_path:505 | 从路径设置目标: 工资表 > 2025年 > 07月 > 全部在职人员
2025-07-15 19:01:02.023 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_table_list:873 | 首次查询发现表数量异常: 0 个表，期望 1 个，可能需要重试
2025-07-15 19:01:02.140 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:881 | 找到 0 个匹配类型 'salary_data' 的表
2025-07-15 19:01:02.158 | INFO     | src.gui.main_dialogs:_get_template_fields:1884 | 使用字段模板: 全部在职人员工资表
2025-07-15 19:01:02.203 | INFO     | src.modules.data_import.import_defaults_manager:load_settings:86 | 未找到用户设置文件，使用默认设置
2025-07-15 19:01:02.203 | INFO     | src.gui.main_dialogs:_apply_default_settings:2206 | 已应用默认设置: {'start_row': 1, 'import_mode': 'multi_sheet', 'auto_match_sheet': True, 'include_header': True, 'skip_empty_rows': True, 'create_table_mode': 'sheet_name', 'import_strategy': 'separate_tables', 'table_template': 'salary_data'}
2025-07-15 19:01:02.203 | INFO     | src.gui.main_dialogs:_setup_tooltips:2461 | 工具提示设置完成
2025-07-15 19:01:02.203 | INFO     | src.gui.main_dialogs:_setup_shortcuts:2500 | 快捷键设置完成
2025-07-15 19:01:02.203 | INFO     | src.gui.main_dialogs:__init__:77 | 数据导入对话框初始化完成。
2025-07-15 19:01:08.859 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-07-15 19:01:10.192 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-07-15 19:01:10.196 | INFO     | src.modules.data_import.smart_sheet_matcher:record_user_choice:372 | 记录用户选择: 全部在职人员 -> 全部在职人员工资表
2025-07-15 19:01:10.197 | INFO     | src.gui.main_dialogs:_auto_select_sheet_by_category:2241 | 根据人员类别 '全部在职人员' 自动选择工作表: 全部在职人员工资表 (匹配类型: fuzzy, 得分: 0.60)
2025-07-15 19:01:14.532 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-07-15 19:01:14.720 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-07-15 19:01:14.736 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-07-15 19:01:14.736 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:200 | 开始导入多Sheet Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-07-15 19:01:14.736 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-07-15 19:01:14.939 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-07-15 19:01:14.939 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:211 | 检测到 4 个工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-07-15 19:01:14.954 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-07-15 19:01:14.954 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-07-15 19:01:14.954 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-07-15 19:01:15.064 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:320 | 🔧 [修复标识] Excel读取完成: 16列 (列过滤: 否)
2025-07-15 19:01:15.064 | INFO     | src.modules.data_import.excel_importer:_clean_data:427 | 🔧 [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-07-15 19:01:15.064 | INFO     | src.modules.data_import.excel_importer:_clean_data:438 | 数据清理完成: 保留所有 16 列 (原始 16 列)
2025-07-15 19:01:15.064 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:339 | 导入完成: 3行 x 16列
2025-07-15 19:01:15.064 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:567 | 数据导入过滤: 发现1条姓名为空的记录
2025-07-15 19:01:15.064 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:569 | 过滤记录[行3]: 姓名字段(姓名)为空, 数据: {'序号': '总计', '合计': np.float64(34405.100000000006)}
2025-07-15 19:01:15.064 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:607 | 数据过滤完成: 原始3条记录，过滤1条无效记录，有效记录2条
2025-07-15 19:01:15.064 | INFO     | src.modules.data_import.excel_importer:import_data:286 | 🔧 [修复标识] 数据导入最终完成: 2行 × 16列
2025-07-15 19:01:15.064 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:445 | 未找到工作表 离休人员工资表 的配置，使用智能默认处理
2025-07-15 19:01:15.064 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:726 | 为Sheet '离休人员工资表' 生成智能默认配置: 1 个必需字段
2025-07-15 19:01:15.079 | INFO     | src.modules.data_import.specialized_field_mapping_generator:generate_mapping:139 | 为模板 retired_employees 生成了 21 个字段映射
2025-07-15 19:01:15.079 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:462 | 使用专用模板 retired_employees 生成字段映射: 21 个字段
2025-07-15 19:01:15.079 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:605 | 完整字段映射保存成功: salary_data_2025_07_retired_employees
2025-07-15 19:01:15.079 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:504 | 为表 salary_data_2025_07_retired_employees 生成标准化字段映射: 21 个字段
2025-07-15 19:01:15.079 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:514 | Sheet 离休人员工资表 存在 1 个验证错误
2025-07-15 19:01:15.095 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:523 | Sheet 离休人员工资表 数据处理完成: 2 行
2025-07-15 19:01:15.095 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:360 | Sheet '离休人员工资表' 检测到模板类型: retired_employees
2025-07-15 19:01:15.095 | INFO     | src.modules.data_storage.dynamic_table_manager:_create_table_from_schema:692 | 成功创建表: salary_data_2025_07_retired_employees
2025-07-15 19:01:15.111 | INFO     | src.modules.data_storage.dynamic_table_manager:create_specialized_salary_table:360 | 专用工资数据表创建成功: salary_data_2025_07_retired_employees (模板: retired_employees)
2025-07-15 19:01:15.142 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1131 | 🔧 [修复标识] 导入字段映射加载完成: 28 个映射规则
2025-07-15 19:01:15.142 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1139 | 🔧 [修复标识] 保留未映射列: ['data_source', 'import_time']
2025-07-15 19:01:15.142 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1148 | 🔧 [修复标识] 导入列名映射成功: 18 个字段已映射
2025-07-15 19:01:15.158 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1285 | 成功向表 salary_data_2025_07_retired_employees 保存 2 条数据。
2025-07-15 19:01:15.205 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-07-15 19:01:15.205 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-07-15 19:01:15.205 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-07-15 19:01:15.314 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:320 | 🔧 [修复标识] Excel读取完成: 27列 (列过滤: 否)
2025-07-15 19:01:15.314 | INFO     | src.modules.data_import.excel_importer:_clean_data:427 | 🔧 [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-07-15 19:01:15.314 | INFO     | src.modules.data_import.excel_importer:_clean_data:438 | 数据清理完成: 保留所有 27 列 (原始 27 列)
2025-07-15 19:01:15.314 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:339 | 导入完成: 14行 x 27列
2025-07-15 19:01:15.314 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:567 | 数据导入过滤: 发现1条姓名为空的记录
2025-07-15 19:01:15.314 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:569 | 过滤记录[行14]: 姓名字段(姓名)为空, 数据: {'序号': '总计', '应发工资': np.float64(33607.14625)}
2025-07-15 19:01:15.314 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:607 | 数据过滤完成: 原始14条记录，过滤1条无效记录，有效记录13条
2025-07-15 19:01:15.314 | INFO     | src.modules.data_import.excel_importer:import_data:286 | 🔧 [修复标识] 数据导入最终完成: 13行 × 27列
2025-07-15 19:01:15.314 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:445 | 未找到工作表 退休人员工资表 的配置，使用智能默认处理
2025-07-15 19:01:15.314 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:726 | 为Sheet '退休人员工资表' 生成智能默认配置: 1 个必需字段
2025-07-15 19:01:15.330 | INFO     | src.modules.data_import.specialized_field_mapping_generator:generate_mapping:139 | 为模板 pension_employees 生成了 32 个字段映射
2025-07-15 19:01:15.330 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:462 | 使用专用模板 pension_employees 生成字段映射: 32 个字段
2025-07-15 19:01:15.330 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:605 | 完整字段映射保存成功: salary_data_2025_07_pension_employees
2025-07-15 19:01:15.330 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:504 | 为表 salary_data_2025_07_pension_employees 生成标准化字段映射: 32 个字段
2025-07-15 19:01:15.330 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:514 | Sheet 退休人员工资表 存在 1 个验证错误
2025-07-15 19:01:15.345 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:523 | Sheet 退休人员工资表 数据处理完成: 13 行
2025-07-15 19:01:15.345 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:360 | Sheet '退休人员工资表' 检测到模板类型: pension_employees
2025-07-15 19:01:15.345 | INFO     | src.modules.data_storage.dynamic_table_manager:_create_table_from_schema:692 | 成功创建表: salary_data_2025_07_pension_employees
2025-07-15 19:01:15.361 | INFO     | src.modules.data_storage.dynamic_table_manager:create_specialized_salary_table:360 | 专用工资数据表创建成功: salary_data_2025_07_pension_employees (模板: pension_employees)
2025-07-15 19:01:15.361 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1131 | 🔧 [修复标识] 导入字段映射加载完成: 32 个映射规则
2025-07-15 19:01:15.361 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1139 | 🔧 [修复标识] 保留未映射列: ['data_source', 'import_time']
2025-07-15 19:01:15.361 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1148 | 🔧 [修复标识] 导入列名映射成功: 29 个字段已映射
2025-07-15 19:01:15.392 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1285 | 成功向表 salary_data_2025_07_pension_employees 保存 13 条数据。
2025-07-15 19:01:15.392 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-07-15 19:01:15.392 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-07-15 19:01:15.392 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-07-15 19:01:15.533 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:320 | 🔧 [修复标识] Excel读取完成: 23列 (列过滤: 否)
2025-07-15 19:01:15.533 | INFO     | src.modules.data_import.excel_importer:_clean_data:427 | 🔧 [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-07-15 19:01:15.549 | INFO     | src.modules.data_import.excel_importer:_clean_data:438 | 数据清理完成: 保留所有 23 列 (原始 23 列)
2025-07-15 19:01:15.549 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:339 | 导入完成: 1397行 x 23列
2025-07-15 19:01:15.549 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:567 | 数据导入过滤: 发现1条姓名为空的记录
2025-07-15 19:01:15.549 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:569 | 过滤记录[行1397]: 姓名字段(姓名)为空, 数据: {'2025年岗位工资': np.float64(3971045.71), '2025年薪级工资': np.int64(3138129), '津贴': np.float64(94436.73000000001), '结余津贴': np.int64(78008), '2025年基础性绩效': np.int64(4706933), '卫生费': np.float64(14240.0), '交通补贴': np.float64(306460.0), '物业补贴': np.float64(305860.0), '住房补贴': np.float64(337019.8710385144), '车补': np.float64(12870.0), '通讯补贴': np.float64(10250.0), '2025年奖励性绩效预发': np.int64(2262100), '补发': np.float64(2528.0), '借支': np.float64(122740.12055172413), '应发工资': np.float64(15117140.190486807), '2025公积金': np.int64(2390358), '代扣代存养老保险': np.float64(1967911.5299999986)}
2025-07-15 19:01:15.549 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:607 | 数据过滤完成: 原始1397条记录，过滤1条无效记录，有效记录1396条
2025-07-15 19:01:15.549 | INFO     | src.modules.data_import.excel_importer:import_data:286 | 🔧 [修复标识] 数据导入最终完成: 1396行 × 23列
2025-07-15 19:01:15.564 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:445 | 未找到工作表 全部在职人员工资表 的配置，使用智能默认处理
2025-07-15 19:01:15.564 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:726 | 为Sheet '全部在职人员工资表' 生成智能默认配置: 2 个必需字段
2025-07-15 19:01:15.564 | INFO     | src.modules.data_import.specialized_field_mapping_generator:generate_mapping:139 | 为模板 active_employees 生成了 28 个字段映射
2025-07-15 19:01:15.564 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:462 | 使用专用模板 active_employees 生成字段映射: 28 个字段
2025-07-15 19:01:15.582 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:605 | 完整字段映射保存成功: salary_data_2025_07_active_employees
2025-07-15 19:01:15.582 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:504 | 为表 salary_data_2025_07_active_employees 生成标准化字段映射: 28 个字段
2025-07-15 19:01:15.582 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:514 | Sheet 全部在职人员工资表 存在 2 个验证错误
2025-07-15 19:01:15.582 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:523 | Sheet 全部在职人员工资表 数据处理完成: 1396 行
2025-07-15 19:01:15.582 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:360 | Sheet '全部在职人员工资表' 检测到模板类型: active_employees
2025-07-15 19:01:15.595 | INFO     | src.modules.data_storage.dynamic_table_manager:_create_table_from_schema:692 | 成功创建表: salary_data_2025_07_active_employees
2025-07-15 19:01:15.595 | INFO     | src.modules.data_storage.dynamic_table_manager:create_specialized_salary_table:360 | 专用工资数据表创建成功: salary_data_2025_07_active_employees (模板: active_employees)
2025-07-15 19:01:15.627 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1131 | 🔧 [修复标识] 导入字段映射加载完成: 28 个映射规则
2025-07-15 19:01:15.627 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1139 | 🔧 [修复标识] 保留未映射列: ['data_source', 'import_time']
2025-07-15 19:01:15.627 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1148 | 🔧 [修复标识] 导入列名映射成功: 25 个字段已映射
2025-07-15 19:01:15.673 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1285 | 成功向表 salary_data_2025_07_active_employees 保存 1396 条数据。
2025-07-15 19:01:15.673 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-07-15 19:01:15.673 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-07-15 19:01:15.673 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-07-15 19:01:15.783 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:320 | 🔧 [修复标识] Excel读取完成: 21列 (列过滤: 否)
2025-07-15 19:01:15.783 | INFO     | src.modules.data_import.excel_importer:_clean_data:427 | 🔧 [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-07-15 19:01:15.783 | INFO     | src.modules.data_import.excel_importer:_clean_data:438 | 数据清理完成: 保留所有 21 列 (原始 21 列)
2025-07-15 19:01:15.800 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:339 | 导入完成: 63行 x 21列
2025-07-15 19:01:15.800 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:567 | 数据导入过滤: 发现1条姓名为空的记录
2025-07-15 19:01:15.800 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:569 | 过滤记录[行63]: 姓名字段(姓名)为空, 数据: {'应发工资': np.float64(471980.9)}
2025-07-15 19:01:15.800 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:607 | 数据过滤完成: 原始63条记录，过滤1条无效记录，有效记录62条
2025-07-15 19:01:15.800 | INFO     | src.modules.data_import.excel_importer:import_data:286 | 🔧 [修复标识] 数据导入最终完成: 62行 × 21列
2025-07-15 19:01:15.800 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:445 | 未找到工作表 A岗职工 的配置，使用智能默认处理
2025-07-15 19:01:15.800 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:726 | 为Sheet 'A岗职工' 生成智能默认配置: 2 个必需字段
2025-07-15 19:01:15.800 | INFO     | src.modules.data_import.specialized_field_mapping_generator:generate_mapping:139 | 为模板 a_grade_employees 生成了 26 个字段映射
2025-07-15 19:01:15.800 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:462 | 使用专用模板 a_grade_employees 生成字段映射: 26 个字段
2025-07-15 19:01:15.814 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:605 | 完整字段映射保存成功: salary_data_2025_07_a_grade_employees
2025-07-15 19:01:15.814 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:504 | 为表 salary_data_2025_07_a_grade_employees 生成标准化字段映射: 26 个字段
2025-07-15 19:01:15.814 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:514 | Sheet A岗职工 存在 2 个验证错误
2025-07-15 19:01:15.814 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:523 | Sheet A岗职工 数据处理完成: 62 行
2025-07-15 19:01:15.814 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:360 | Sheet 'A岗职工' 检测到模板类型: a_grade_employees
2025-07-15 19:01:15.814 | INFO     | src.modules.data_storage.dynamic_table_manager:_create_table_from_schema:692 | 成功创建表: salary_data_2025_07_a_grade_employees
2025-07-15 19:01:15.829 | INFO     | src.modules.data_storage.dynamic_table_manager:create_specialized_salary_table:360 | 专用工资数据表创建成功: salary_data_2025_07_a_grade_employees (模板: a_grade_employees)
2025-07-15 19:01:15.845 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1131 | 🔧 [修复标识] 导入字段映射加载完成: 26 个映射规则
2025-07-15 19:01:15.845 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1139 | 🔧 [修复标识] 保留未映射列: ['data_source', 'import_time']
2025-07-15 19:01:15.845 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1148 | 🔧 [修复标识] 导入列名映射成功: 23 个字段已映射
2025-07-15 19:01:15.861 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1285 | 成功向表 salary_data_2025_07_a_grade_employees 保存 62 条数据。
2025-07-15 19:01:15.861 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:230 | 多Sheet导入完成: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_07_retired_employees 保存 2 条数据。', 'table_name': 'salary_data_2025_07_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_07_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2025_07_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_07_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2025_07_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2025_07_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2025_07_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 6}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']}
2025-07-15 19:01:15.861 | INFO     | src.gui.main_dialogs:_execute_multi_sheet_import:1479 | 多Sheet导入成功: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_07_retired_employees 保存 2 条数据。', 'table_name': 'salary_data_2025_07_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_07_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2025_07_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_07_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2025_07_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2025_07_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2025_07_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 6}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工'], 'import_mode': 'multi_sheet', 'strategy': 'separate_tables', 'data_period': '2025-07', 'data_description': '2025年7月工资数据', 'file_path': 'C:/test/salary_changes/salary_changes/data/工资表/2025年5月份正式工工资（报财务)  最终版.xls', 'target_path': '工资表 > 2025年 > 07月 > 全部在职人员'}
2025-07-15 19:01:15.877 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:3978 | 开始处理导入结果: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_07_retired_employees 保存 2 条数据。', 'table_name': 'salary_data_2025_07_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_07_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2025_07_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_07_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2025_07_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2025_07_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2025_07_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 6}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工'], 'import_mode': 'multi_sheet', 'strategy': 'separate_tables', 'data_period': '2025-07', 'data_description': '2025年7月工资数据', 'file_path': 'C:/test/salary_changes/salary_changes/data/工资表/2025年5月份正式工工资（报财务)  最终版.xls', 'target_path': '工资表 > 2025年 > 07月 > 全部在职人员'}
2025-07-15 19:01:15.877 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:3989 | 导入模式: multi_sheet, 目标路径: '工资表 > 2025年 > 07月 > 全部在职人员'
2025-07-15 19:01:15.892 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:4007 | 接收到导入数据, 来源: 未知来源, 目标路径: 工资表 > 2025年 > 07月 > 全部在职人员
2025-07-15 19:01:16.693 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:4095 | 检查是否需要更新导航面板: ['工资表', '2025年', '07月', '全部在职人员']
2025-07-15 19:01:16.693 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:4099 | 检测到工资数据导入，开始刷新导航面板
2025-07-15 19:01:16.696 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:4103 | 使用强制刷新方法
2025-07-15 19:01:16.697 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1230 | 开始强制刷新工资数据导航... (尝试 1/3)
2025-07-15 19:01:16.699 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1238 | 找到工资表节点: 📊 工资表
2025-07-15 19:01:16.716 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:881 | 找到 4 个匹配类型 'salary_data' 的表
2025-07-15 19:01:16.717 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2025年，包含 1 个月份
2025-07-15 19:01:16.720 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1343 | 工资数据导航已强制刷新: 1 个年份, 1 个月份
2025-07-15 19:01:16.721 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1346 | 默认路径: 工资表 > 2025年 > 7月 > 全部在职人员
2025-07-15 19:01:16.721 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1385 | force_refresh_salary_data 执行完成
2025-07-15 19:01:16.722 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:4108 | 将在1500ms后导航到: 工资表 > 2025年 > 07月 > 全部在职人员
2025-07-15 19:01:18.221 | INFO     | src.gui.prototype.prototype_main_window:_navigate_to_imported_path:4184 | 尝试导航到新导入的路径: 工资表 > 2025年 > 07月 > 全部在职人员
2025-07-15 19:01:18.221 | INFO     | src.gui.prototype.prototype_main_window:_navigate_to_imported_path:4189 | 已成功导航到新导入的路径: 工资表 > 2025年 > 07月 > 全部在职人员
2025-07-15 19:01:18.723 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:5412 | 🔧 [表名生成] 从完整路径生成表名: ['工资表', '2025年', '07月', '全部在职人员'] -> salary_data_2025_07_active_employees
2025-07-15 19:01:18.724 | INFO     | src.gui.prototype.prototype_main_window:_refresh_current_data_display:4268 | 开始刷新当前数据显示: salary_data_2025_07_active_employees
2025-07-15 19:01:18.727 | INFO     | src.gui.prototype.prototype_main_window:_refresh_current_data_display:4275 | 分页模式刷新: 第1页，每页50条
2025-07-15 19:01:18.728 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:5058 | 使用分页模式加载 salary_data_2025_07_active_employees，第1页，每页50条
2025-07-15 19:01:18.729 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:5101 | 缓存未命中，从数据库加载: salary_data_2025_07_active_employees 第1页
2025-07-15 19:01:18.730 | INFO     | src.gui.prototype.prototype_main_window:run:128 | 开始加载表 salary_data_2025_07_active_employees 第1页数据，每页50条
2025-07-15 19:01:18.732 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:439 | 正在从表 salary_data_2025_07_active_employees 分页获取数据: 第1页, 每页50条
2025-07-15 19:01:18.736 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:462 | 成功从表 salary_data_2025_07_active_employees 获取第1页数据: 50 行，总计1396行
2025-07-15 19:01:18.740 | INFO     | src.gui.prototype.prototype_main_window:run:166 | 原始数据: 50行, 28列
2025-07-15 19:01:18.740 | INFO     | src.gui.prototype.prototype_main_window:run:173 | 开始应用字段映射
2025-07-15 19:01:18.742 | INFO     | src.gui.prototype.prototype_main_window:_apply_unified_field_processing:4589 | 🔧 [修复标识] 开始统一字段处理: salary_data_2025_07_active_employees, 原始列数: 28
2025-07-15 19:01:18.786 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:4784 | 🔧 [修复标识] 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-15 19:01:18.788 | INFO     | src.gui.prototype.prototype_main_window:_apply_unified_field_processing:4634 | 🔧 [字段处理] 统一字段处理完成并缓存: 24个字段
2025-07-15 19:01:18.789 | INFO     | src.gui.prototype.prototype_main_window:run:183 | 🔧 [修复标识] PaginationWorker - 字段映射成功: 28 -> 24列
2025-07-15 19:01:18.814 | INFO     | src.gui.prototype.prototype_main_window:run:197 | 字段映射成功: 24列
2025-07-15 19:01:18.856 | INFO     | src.gui.prototype.prototype_main_window:run:207 | 开始应用数据格式化处理
2025-07-15 19:01:18.857 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:238 | 开始格式化工资表数据 - 表类型: salary_data_2025_07_active_employees
2025-07-15 19:01:18.880 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:251 | 原始数据: 50行, 24列
2025-07-15 19:01:18.884 | INFO     | src.modules.data_storage.salary_data_formatter:_extract_table_type_from_name:335 | 🔧 [表名映射] 通过关键词 'active_employees' 匹配到表类型: active_employees
2025-07-15 19:01:18.884 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:255 | 🔧 [表名映射] 输入表名: salary_data_2025_07_active_employees, 提取的表类型: active_employees
2025-07-15 19:01:18.886 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:259 | 使用表类型配置: active_employees, 配置字段: ['id_fields', 'string_fields', 'integer_fields', 'float_fields', 'special_fields']
2025-07-15 19:01:18.887 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:263 | 🔧 [格式化调试] 开始处理ID字段: ['employee_id', 'employee_type_code', '工号', '人员类别代码']
2025-07-15 19:01:18.888 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:268 | 🔧 [格式化调试] 处理前 工号 字段样例: ['19990089.0', '20161565.0', '20191782.0']
2025-07-15 19:01:18.889 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:268 | 🔧 [格式化调试] 处理前 人员类别代码 字段样例: ['1.0', '1.0', '17.0']
2025-07-15 19:01:18.889 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_id_fields:461 | 🔧 [警告] ID字段 employee_id 不存在于数据框中
2025-07-15 19:01:18.890 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_id_fields:461 | 🔧 [警告] ID字段 employee_type_code 不存在于数据框中
2025-07-15 19:01:18.896 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:377 | 🔧 [强化调试] 处理ID字段 工号: 原始样例=['19990089.0', '20161565.0', '20191782.0'], 原始类型=object
2025-07-15 19:01:18.897 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:426 | 🔧 [强化调试] 清理后ID字段 工号: 样例=['19990089', '20161565', '20191782']
2025-07-15 19:01:18.898 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:443 | 🔧 [强化调试] 最终ID字段 工号: 样例=['19990089', '20161565', '20191782']
2025-07-15 19:01:18.899 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:454 | 🔧 [验证成功] ID字段 工号 格式化完成，无小数点问题
2025-07-15 19:01:18.900 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:377 | 🔧 [强化调试] 处理ID字段 人员类别代码: 原始样例=['1.0', '1.0', '17.0'], 原始类型=object
2025-07-15 19:01:18.902 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:426 | 🔧 [强化调试] 清理后ID字段 人员类别代码: 样例=['1', '1', '17']
2025-07-15 19:01:18.904 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:443 | 🔧 [强化调试] 最终ID字段 人员类别代码: 样例=['01', '01', '17']
2025-07-15 19:01:18.904 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:454 | 🔧 [验证成功] ID字段 人员类别代码 格式化完成，无小数点问题
2025-07-15 19:01:18.905 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:274 | 🔧 [格式化调试] 处理后 工号 字段样例: ['19990089', '20161565', '20191782']
2025-07-15 19:01:18.911 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:274 | 🔧 [格式化调试] 处理后 人员类别代码 字段样例: ['01', '01', '17']
2025-07-15 19:01:18.911 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:472 | 🔧 [强化调试] 开始格式化字符串字段: ['employee_id', 'employee_name', 'department', 'employee_type_code', 'employee_type', '工号', '姓名', '部门名称', '人员类别代码', '人员类别']
2025-07-15 19:01:18.912 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 employee_id 不存在于数据框中
2025-07-15 19:01:18.912 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 employee_name 不存在于数据框中
2025-07-15 19:01:18.913 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 department 不存在于数据框中
2025-07-15 19:01:18.914 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 employee_type_code 不存在于数据框中
2025-07-15 19:01:18.917 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 employee_type 不存在于数据框中
2025-07-15 19:01:18.919 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 工号: 原始样例=['19990089', '20161565', '20191782'], 原始类型=object
2025-07-15 19:01:18.924 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:504 | 🔧 [修复] 工号字段 工号 已特殊处理，去除小数点格式
2025-07-15 19:01:18.926 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 工号: 样例=['19990089', '20161565', '20191782'], 类型=object
2025-07-15 19:01:18.926 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 工号 格式化完成，无小数点问题
2025-07-15 19:01:18.927 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 姓名: 原始样例=['杨胜', '胡四平', '肖啸'], 原始类型=object
2025-07-15 19:01:18.930 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 姓名: 样例=['杨胜', '胡四平', '肖啸'], 类型=object
2025-07-15 19:01:18.931 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 姓名 格式化完成，无小数点问题
2025-07-15 19:01:18.932 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 部门名称: 原始样例=['自动化学院', '自动化学院', '自动化学院'], 原始类型=object
2025-07-15 19:01:18.935 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 部门名称: 样例=['自动化学院', '自动化学院', '自动化学院'], 类型=object
2025-07-15 19:01:18.941 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 部门名称 格式化完成，无小数点问题
2025-07-15 19:01:18.942 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 人员类别代码: 原始样例=['01', '01', '17'], 原始类型=object
2025-07-15 19:01:18.945 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 人员类别代码: 样例=['01', '01', '17'], 类型=object
2025-07-15 19:01:18.946 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 人员类别代码 格式化完成，无小数点问题
2025-07-15 19:01:18.961 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 人员类别: 原始样例=['教学院其它人员', '教学单位专技人员', '科研单位人员'], 原始类型=object
2025-07-15 19:01:18.969 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 人员类别: 样例=['教学院其它人员', '教学单位专技人员', '科研单位人员'], 类型=object
2025-07-15 19:01:18.970 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 人员类别 格式化完成，无小数点问题
2025-07-15 19:01:18.971 | INFO     | src.modules.data_storage.salary_data_formatter:_format_integer_fields:604 | 🔧 [强化调试] 开始格式化整型字段: []
2025-07-15 19:01:18.973 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:655 | 🔧 [强化调试] 开始格式化浮点型字段: ['position_salary_2025', 'grade_salary_2025', 'allowance', 'balance_allowance', 'basic_performance_2025', 'health_fee', 'transport_allowance', 'property_allowance', 'communication_allowance', 'performance_bonus_2025', 'provident_fund_2025', 'housing_allowance', 'car_allowance', 'supplement', 'advance', 'total_salary', 'pension_insurance', '2025年岗位工资', '2025年薪级工资', '津贴', '结余津贴', '2025年基础性绩效', '卫生费', '交通补贴', '物业补贴', '通讯补贴', '2025年奖励性绩效预发', '2025公积金', '住房补贴', '车补', '补发', '借支', '应发工资', '代扣代存养老保险']
2025-07-15 19:01:18.974 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 position_salary_2025 不存在于数据框中
2025-07-15 19:01:18.975 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 grade_salary_2025 不存在于数据框中
2025-07-15 19:01:19.004 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 allowance 不存在于数据框中
2025-07-15 19:01:19.005 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 balance_allowance 不存在于数据框中
2025-07-15 19:01:19.006 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 basic_performance_2025 不存在于数据框中
2025-07-15 19:01:19.008 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 health_fee 不存在于数据框中
2025-07-15 19:01:19.023 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 transport_allowance 不存在于数据框中
2025-07-15 19:01:19.029 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 property_allowance 不存在于数据框中
2025-07-15 19:01:19.030 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 communication_allowance 不存在于数据框中
2025-07-15 19:01:19.031 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 performance_bonus_2025 不存在于数据框中
2025-07-15 19:01:19.032 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 provident_fund_2025 不存在于数据框中
2025-07-15 19:01:19.033 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 housing_allowance 不存在于数据框中
2025-07-15 19:01:19.034 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 car_allowance 不存在于数据框中
2025-07-15 19:01:19.034 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 supplement 不存在于数据框中
2025-07-15 19:01:19.035 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 advance 不存在于数据框中
2025-07-15 19:01:19.036 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 total_salary 不存在于数据框中
2025-07-15 19:01:19.037 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 pension_insurance 不存在于数据框中
2025-07-15 19:01:19.047 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025年岗位工资: 原始样例=[2880.0, 3030.0, 2185.0], 原始类型=float64
2025-07-15 19:01:19.048 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025年岗位工资: 样例=[2880.0, 3030.0, 2185.0], 类型=float64
2025-07-15 19:01:19.049 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025年岗位工资 格式化完成，保留两位小数
2025-07-15 19:01:19.050 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025年薪级工资: 原始样例=[2375.0, 1696.0, 1427.0], 原始类型=float64
2025-07-15 19:01:19.053 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025年薪级工资: 样例=[2375.0, 1696.0, 1427.0], 类型=float64
2025-07-15 19:01:19.053 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025年薪级工资 格式化完成，保留两位小数
2025-07-15 19:01:19.054 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 津贴: 原始样例=[102.0, 0.0, 0.0], 原始类型=float64
2025-07-15 19:01:19.062 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 津贴: 样例=[102.0, 0.0, 0.0], 类型=float64
2025-07-15 19:01:19.063 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 津贴 格式化完成，保留两位小数
2025-07-15 19:01:19.064 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 结余津贴: 原始样例=[56.0, 56.0, 56.0], 原始类型=float64
2025-07-15 19:01:19.065 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 结余津贴: 样例=[56.0, 56.0, 56.0], 类型=float64
2025-07-15 19:01:19.065 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 结余津贴 格式化完成，保留两位小数
2025-07-15 19:01:19.066 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025年基础性绩效: 原始样例=[3594.0, 3466.0, 2978.0], 原始类型=float64
2025-07-15 19:01:19.069 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025年基础性绩效: 样例=[3594.0, 3466.0, 2978.0], 类型=float64
2025-07-15 19:01:19.077 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025年基础性绩效 格式化完成，保留两位小数
2025-07-15 19:01:19.077 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 卫生费: 原始样例=[0.0, 0.0, 0.0], 原始类型=float64
2025-07-15 19:01:19.077 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 卫生费: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-15 19:01:19.082 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 卫生费 格式化完成，保留两位小数
2025-07-15 19:01:19.082 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 交通补贴: 原始样例=[220.0, 220.0, 220.0], 原始类型=float64
2025-07-15 19:01:19.082 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 交通补贴: 样例=[220.0, 220.0, 220.0], 类型=float64
2025-07-15 19:01:19.082 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 交通补贴 格式化完成，保留两位小数
2025-07-15 19:01:19.082 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 物业补贴: 原始样例=[240.0, 240.0, 200.0], 原始类型=float64
2025-07-15 19:01:19.082 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 物业补贴: 样例=[240.0, 240.0, 200.0], 类型=float64
2025-07-15 19:01:19.082 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 物业补贴 格式化完成，保留两位小数
2025-07-15 19:01:19.082 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 通讯补贴: 原始样例=[50.0, nan, nan], 原始类型=float64
2025-07-15 19:01:19.082 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 通讯补贴: 样例=[50.0, 0.0, 0.0], 类型=float64
2025-07-15 19:01:19.082 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 通讯补贴 格式化完成，保留两位小数
2025-07-15 19:01:19.082 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025年奖励性绩效预发: 原始样例=[2500.0, 1000.0, 1000.0], 原始类型=float64
2025-07-15 19:01:19.082 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025年奖励性绩效预发: 样例=[2500.0, 1000.0, 1000.0], 类型=float64
2025-07-15 19:01:19.098 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025年奖励性绩效预发 格式化完成，保留两位小数
2025-07-15 19:01:19.098 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025公积金: 原始样例=[2097.0, 1860.0, 1984.0], 原始类型=float64
2025-07-15 19:01:19.098 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025公积金: 样例=[2097.0, 1860.0, 1984.0], 类型=float64
2025-07-15 19:01:19.098 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025公积金 格式化完成，保留两位小数
2025-07-15 19:01:19.098 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 住房补贴: 原始样例=[271.9745083294993, 189.0, 174.16354166666667], 原始类型=float64
2025-07-15 19:01:19.098 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 住房补贴: 样例=[271.97, 189.0, 174.16], 类型=float64
2025-07-15 19:01:19.098 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 住房补贴 格式化完成，保留两位小数
2025-07-15 19:01:19.098 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 车补: 原始样例=[None, None, None], 原始类型=object
2025-07-15 19:01:19.098 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 车补: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-15 19:01:19.119 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 车补 格式化完成，保留两位小数
2025-07-15 19:01:19.119 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 补发: 原始样例=[None, None, None], 原始类型=object
2025-07-15 19:01:19.119 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 补发: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-15 19:01:19.119 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 补发 格式化完成，保留两位小数
2025-07-15 19:01:19.119 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 借支: 原始样例=[nan, nan, 2000.0], 原始类型=float64
2025-07-15 19:01:19.129 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 借支: 样例=[0.0, 0.0, 2000.0], 类型=float64
2025-07-15 19:01:19.129 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 借支 格式化完成，保留两位小数
2025-07-15 19:01:19.129 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 应发工资: 原始样例=[12288.9745083295, 9897.0, 6240.163541666667], 原始类型=float64
2025-07-15 19:01:19.129 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 应发工资: 样例=[12288.97, 9897.0, 6240.16], 类型=float64
2025-07-15 19:01:19.129 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 应发工资 格式化完成，保留两位小数
2025-07-15 19:01:19.129 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 代扣代存养老保险: 原始样例=[1525.8000000000002, 1140.53, 1113.75], 原始类型=float64
2025-07-15 19:01:19.129 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 代扣代存养老保险: 样例=[1525.8, 1140.53, 1113.75], 类型=float64
2025-07-15 19:01:19.129 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 代扣代存养老保险 格式化完成，保留两位小数
2025-07-15 19:01:19.129 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:711 | 🔧 [特殊字段] 开始处理表类型 active_employees 的月份、年份字段
2025-07-15 19:01:19.129 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:866 | 🔧 [特殊字段] 月份、年份字段处理完成
2025-07-15 19:01:19.129 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:291 | 数据格式化完成: 50行, 24列
2025-07-15 19:01:19.129 | INFO     | src.gui.prototype.prototype_main_window:run:215 | 数据格式化成功: 50行, 24列
2025-07-15 19:01:19.145 | INFO     | src.gui.prototype.prototype_main_window:run:239 | 最终数据: 50行, 24列, 总记录数: 1396
2025-07-15 19:01:19.155 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:5131 | 分页数据加载成功（数据库）: 50条数据，第1页，总计1396条
2025-07-15 19:01:19.155 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:439 | 正在从表 salary_data_2025_07_active_employees 分页获取数据: 第2页, 每页50条
2025-07-15 19:01:19.155 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:5164 | 🔧 [异步分页] 开始应用数据格式化处理
2025-07-15 19:01:19.155 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:238 | 开始格式化工资表数据 - 表类型: salary_data_2025_07_active_employees
2025-07-15 19:01:19.155 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:251 | 原始数据: 50行, 24列
2025-07-15 19:01:19.155 | INFO     | src.modules.data_storage.salary_data_formatter:_extract_table_type_from_name:335 | 🔧 [表名映射] 通过关键词 'active_employees' 匹配到表类型: active_employees
2025-07-15 19:01:19.155 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:462 | 成功从表 salary_data_2025_07_active_employees 获取第2页数据: 50 行，总计1396行
2025-07-15 19:01:19.155 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:255 | 🔧 [表名映射] 输入表名: salary_data_2025_07_active_employees, 提取的表类型: active_employees
2025-07-15 19:01:19.173 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:259 | 使用表类型配置: active_employees, 配置字段: ['id_fields', 'string_fields', 'integer_fields', 'float_fields', 'special_fields']
2025-07-15 19:01:19.173 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:263 | 🔧 [格式化调试] 开始处理ID字段: ['employee_id', 'employee_type_code', '工号', '人员类别代码']
2025-07-15 19:01:19.173 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:268 | 🔧 [格式化调试] 处理前 工号 字段样例: ['19990089', '20161565', '20191782']
2025-07-15 19:01:19.173 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:268 | 🔧 [格式化调试] 处理前 人员类别代码 字段样例: ['01', '01', '17']
2025-07-15 19:01:19.173 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_id_fields:461 | 🔧 [警告] ID字段 employee_id 不存在于数据框中
2025-07-15 19:01:19.173 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_id_fields:461 | 🔧 [警告] ID字段 employee_type_code 不存在于数据框中
2025-07-15 19:01:19.173 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:377 | 🔧 [强化调试] 处理ID字段 工号: 原始样例=['19990089', '20161565', '20191782'], 原始类型=object
2025-07-15 19:01:19.173 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:426 | 🔧 [强化调试] 清理后ID字段 工号: 样例=['19990089', '20161565', '20191782']
2025-07-15 19:01:19.173 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:443 | 🔧 [强化调试] 最终ID字段 工号: 样例=['19990089', '20161565', '20191782']
2025-07-15 19:01:19.187 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:454 | 🔧 [验证成功] ID字段 工号 格式化完成，无小数点问题
2025-07-15 19:01:19.187 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:377 | 🔧 [强化调试] 处理ID字段 人员类别代码: 原始样例=['01', '01', '17'], 原始类型=object
2025-07-15 19:01:19.187 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:426 | 🔧 [强化调试] 清理后ID字段 人员类别代码: 样例=['01', '01', '17']
2025-07-15 19:01:19.187 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:443 | 🔧 [强化调试] 最终ID字段 人员类别代码: 样例=['01', '01', '17']
2025-07-15 19:01:19.187 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:454 | 🔧 [验证成功] ID字段 人员类别代码 格式化完成，无小数点问题
2025-07-15 19:01:19.187 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:274 | 🔧 [格式化调试] 处理后 工号 字段样例: ['19990089', '20161565', '20191782']
2025-07-15 19:01:19.187 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:274 | 🔧 [格式化调试] 处理后 人员类别代码 字段样例: ['01', '01', '17']
2025-07-15 19:01:19.187 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:472 | 🔧 [强化调试] 开始格式化字符串字段: ['employee_id', 'employee_name', 'department', 'employee_type_code', 'employee_type', '工号', '姓名', '部门名称', '人员类别代码', '人员类别']
2025-07-15 19:01:19.187 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 employee_id 不存在于数据框中
2025-07-15 19:01:19.187 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 employee_name 不存在于数据框中
2025-07-15 19:01:19.187 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 department 不存在于数据框中
2025-07-15 19:01:19.187 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 employee_type_code 不存在于数据框中
2025-07-15 19:01:19.187 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 employee_type 不存在于数据框中
2025-07-15 19:01:19.187 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 工号: 原始样例=['19990089', '20161565', '20191782'], 原始类型=object
2025-07-15 19:01:19.187 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:504 | 🔧 [修复] 工号字段 工号 已特殊处理，去除小数点格式
2025-07-15 19:01:19.187 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 工号: 样例=['19990089', '20161565', '20191782'], 类型=object
2025-07-15 19:01:19.187 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 工号 格式化完成，无小数点问题
2025-07-15 19:01:19.202 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 姓名: 原始样例=['杨胜', '胡四平', '肖啸'], 原始类型=object
2025-07-15 19:01:19.217 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 姓名: 样例=['杨胜', '胡四平', '肖啸'], 类型=object
2025-07-15 19:01:19.217 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 姓名 格式化完成，无小数点问题
2025-07-15 19:01:19.217 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 部门名称: 原始样例=['自动化学院', '自动化学院', '自动化学院'], 原始类型=object
2025-07-15 19:01:19.217 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 部门名称: 样例=['自动化学院', '自动化学院', '自动化学院'], 类型=object
2025-07-15 19:01:19.217 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 部门名称 格式化完成，无小数点问题
2025-07-15 19:01:19.217 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 人员类别代码: 原始样例=['01', '01', '17'], 原始类型=object
2025-07-15 19:01:19.217 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 人员类别代码: 样例=['01', '01', '17'], 类型=object
2025-07-15 19:01:19.217 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 人员类别代码 格式化完成，无小数点问题
2025-07-15 19:01:19.217 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 人员类别: 原始样例=['教学院其它人员', '教学单位专技人员', '科研单位人员'], 原始类型=object
2025-07-15 19:01:19.217 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 人员类别: 样例=['教学院其它人员', '教学单位专技人员', '科研单位人员'], 类型=object
2025-07-15 19:01:19.217 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 人员类别 格式化完成，无小数点问题
2025-07-15 19:01:19.217 | INFO     | src.modules.data_storage.salary_data_formatter:_format_integer_fields:604 | 🔧 [强化调试] 开始格式化整型字段: []
2025-07-15 19:01:19.217 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:655 | 🔧 [强化调试] 开始格式化浮点型字段: ['position_salary_2025', 'grade_salary_2025', 'allowance', 'balance_allowance', 'basic_performance_2025', 'health_fee', 'transport_allowance', 'property_allowance', 'communication_allowance', 'performance_bonus_2025', 'provident_fund_2025', 'housing_allowance', 'car_allowance', 'supplement', 'advance', 'total_salary', 'pension_insurance', '2025年岗位工资', '2025年薪级工资', '津贴', '结余津贴', '2025年基础性绩效', '卫生费', '交通补贴', '物业补贴', '通讯补贴', '2025年奖励性绩效预发', '2025公积金', '住房补贴', '车补', '补发', '借支', '应发工资', '代扣代存养老保险']
2025-07-15 19:01:19.217 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 position_salary_2025 不存在于数据框中
2025-07-15 19:01:19.217 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 grade_salary_2025 不存在于数据框中
2025-07-15 19:01:19.233 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 allowance 不存在于数据框中
2025-07-15 19:01:19.233 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 balance_allowance 不存在于数据框中
2025-07-15 19:01:19.233 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 basic_performance_2025 不存在于数据框中
2025-07-15 19:01:19.233 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 health_fee 不存在于数据框中
2025-07-15 19:01:19.233 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 transport_allowance 不存在于数据框中
2025-07-15 19:01:19.233 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 property_allowance 不存在于数据框中
2025-07-15 19:01:19.249 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 communication_allowance 不存在于数据框中
2025-07-15 19:01:19.249 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 performance_bonus_2025 不存在于数据框中
2025-07-15 19:01:19.249 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 provident_fund_2025 不存在于数据框中
2025-07-15 19:01:19.249 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 housing_allowance 不存在于数据框中
2025-07-15 19:01:19.249 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 car_allowance 不存在于数据框中
2025-07-15 19:01:19.249 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 supplement 不存在于数据框中
2025-07-15 19:01:19.249 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 advance 不存在于数据框中
2025-07-15 19:01:19.249 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 total_salary 不存在于数据框中
2025-07-15 19:01:19.249 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 pension_insurance 不存在于数据框中
2025-07-15 19:01:19.249 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025年岗位工资: 原始样例=[2880.0, 3030.0, 2185.0], 原始类型=float64
2025-07-15 19:01:19.249 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025年岗位工资: 样例=[2880.0, 3030.0, 2185.0], 类型=float64
2025-07-15 19:01:19.249 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025年岗位工资 格式化完成，保留两位小数
2025-07-15 19:01:19.249 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025年薪级工资: 原始样例=[2375.0, 1696.0, 1427.0], 原始类型=float64
2025-07-15 19:01:19.249 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025年薪级工资: 样例=[2375.0, 1696.0, 1427.0], 类型=float64
2025-07-15 19:01:19.249 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025年薪级工资 格式化完成，保留两位小数
2025-07-15 19:01:19.249 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 津贴: 原始样例=[102.0, 0.0, 0.0], 原始类型=float64
2025-07-15 19:01:19.249 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 津贴: 样例=[102.0, 0.0, 0.0], 类型=float64
2025-07-15 19:01:19.249 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 津贴 格式化完成，保留两位小数
2025-07-15 19:01:19.265 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 结余津贴: 原始样例=[56.0, 56.0, 56.0], 原始类型=float64
2025-07-15 19:01:19.265 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 结余津贴: 样例=[56.0, 56.0, 56.0], 类型=float64
2025-07-15 19:01:19.265 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 结余津贴 格式化完成，保留两位小数
2025-07-15 19:01:19.280 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025年基础性绩效: 原始样例=[3594.0, 3466.0, 2978.0], 原始类型=float64
2025-07-15 19:01:19.280 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025年基础性绩效: 样例=[3594.0, 3466.0, 2978.0], 类型=float64
2025-07-15 19:01:19.280 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025年基础性绩效 格式化完成，保留两位小数
2025-07-15 19:01:19.280 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 卫生费: 原始样例=[0.0, 0.0, 0.0], 原始类型=float64
2025-07-15 19:01:19.280 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 卫生费: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-15 19:01:19.280 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 卫生费 格式化完成，保留两位小数
2025-07-15 19:01:19.280 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 交通补贴: 原始样例=[220.0, 220.0, 220.0], 原始类型=float64
2025-07-15 19:01:19.280 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 交通补贴: 样例=[220.0, 220.0, 220.0], 类型=float64
2025-07-15 19:01:19.280 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 交通补贴 格式化完成，保留两位小数
2025-07-15 19:01:19.280 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 物业补贴: 原始样例=[240.0, 240.0, 200.0], 原始类型=float64
2025-07-15 19:01:19.280 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 物业补贴: 样例=[240.0, 240.0, 200.0], 类型=float64
2025-07-15 19:01:19.280 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 物业补贴 格式化完成，保留两位小数
2025-07-15 19:01:19.280 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 通讯补贴: 原始样例=[50.0, 0.0, 0.0], 原始类型=float64
2025-07-15 19:01:19.280 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 通讯补贴: 样例=[50.0, 0.0, 0.0], 类型=float64
2025-07-15 19:01:19.280 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 通讯补贴 格式化完成，保留两位小数
2025-07-15 19:01:19.280 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025年奖励性绩效预发: 原始样例=[2500.0, 1000.0, 1000.0], 原始类型=float64
2025-07-15 19:01:19.280 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025年奖励性绩效预发: 样例=[2500.0, 1000.0, 1000.0], 类型=float64
2025-07-15 19:01:19.296 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025年奖励性绩效预发 格式化完成，保留两位小数
2025-07-15 19:01:19.296 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025公积金: 原始样例=[2097.0, 1860.0, 1984.0], 原始类型=float64
2025-07-15 19:01:19.296 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025公积金: 样例=[2097.0, 1860.0, 1984.0], 类型=float64
2025-07-15 19:01:19.296 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025公积金 格式化完成，保留两位小数
2025-07-15 19:01:19.296 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 住房补贴: 原始样例=[271.97, 189.0, 174.16], 原始类型=float64
2025-07-15 19:01:19.312 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 住房补贴: 样例=[271.97, 189.0, 174.16], 类型=float64
2025-07-15 19:01:19.312 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 住房补贴 格式化完成，保留两位小数
2025-07-15 19:01:19.312 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 车补: 原始样例=[0.0, 0.0, 0.0], 原始类型=float64
2025-07-15 19:01:19.312 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 车补: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-15 19:01:19.312 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 车补 格式化完成，保留两位小数
2025-07-15 19:01:19.312 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 补发: 原始样例=[0.0, 0.0, 0.0], 原始类型=float64
2025-07-15 19:01:19.312 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 补发: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-15 19:01:19.312 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 补发 格式化完成，保留两位小数
2025-07-15 19:01:19.312 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 借支: 原始样例=[0.0, 0.0, 2000.0], 原始类型=float64
2025-07-15 19:01:19.312 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 借支: 样例=[0.0, 0.0, 2000.0], 类型=float64
2025-07-15 19:01:19.312 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 借支 格式化完成，保留两位小数
2025-07-15 19:01:19.312 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 应发工资: 原始样例=[12288.97, 9897.0, 6240.16], 原始类型=float64
2025-07-15 19:01:19.312 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 应发工资: 样例=[12288.97, 9897.0, 6240.16], 类型=float64
2025-07-15 19:01:19.312 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 应发工资 格式化完成，保留两位小数
2025-07-15 19:01:19.312 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 代扣代存养老保险: 原始样例=[1525.8, 1140.53, 1113.75], 原始类型=float64
2025-07-15 19:01:19.327 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 代扣代存养老保险: 样例=[1525.8, 1140.53, 1113.75], 类型=float64
2025-07-15 19:01:19.342 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 代扣代存养老保险 格式化完成，保留两位小数
2025-07-15 19:01:19.342 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:711 | 🔧 [特殊字段] 开始处理表类型 active_employees 的月份、年份字段
2025-07-15 19:01:19.342 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:866 | 🔧 [特殊字段] 月份、年份字段处理完成
2025-07-15 19:01:19.360 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:291 | 数据格式化完成: 50行, 24列
2025-07-15 19:01:19.374 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:5166 | 🔧 [异步分页] 数据格式化成功: 50行, 24列
2025-07-15 19:01:19.374 | INFO     | src.gui.prototype.prototype_main_window:set_data:642 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-07-15 19:01:19.490 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:133 | 使用已存在的配置文件: state/data/field_mappings.json
2025-07-15 19:01:19.490 | INFO     | src.modules.data_import.config_sync_manager:__init__:70 | 配置同步管理器初始化完成
2025-07-15 19:01:19.490 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:1157 | 🔧 [修复] 应用字段映射: 24个字段
2025-07-15 19:01:19.602 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:133 | 使用已存在的配置文件: state/data/field_mappings.json
2025-07-15 19:01:19.602 | INFO     | src.modules.data_import.config_sync_manager:__init__:70 | 配置同步管理器初始化完成
2025-07-15 19:01:19.602 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:1231 | 🔧 [修复] 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-15 19:01:19.618 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3558 | 最大可见行数已更新: 1000 -> 50
2025-07-15 19:01:19.618 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:3611 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-15 19:01:19.618 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第0行数据工号: 19990089
2025-07-15 19:01:19.618 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第1行数据工号: 20161565
2025-07-15 19:01:19.618 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第2行数据工号: 20191782
2025-07-15 19:01:19.618 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第3行数据工号: 20151515
2025-07-15 19:01:19.618 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第4行数据工号: 20181640
2025-07-15 19:01:19.618 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:457 | 🔧 [排序修复] 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-15 19:01:19.618 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:424 | 表格数据已设置: 50 行, 24 列
2025-07-15 19:01:19.649 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2172 | 🔧 [性能分析] 表格数据设置完成: 50 行, 最大可见行数: 50, 总耗时: 47.05ms
2025-07-15 19:01:19.649 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 初始化和状态: 15.74ms (33.5%)
2025-07-15 19:01:19.649 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 字段映射: 0.00ms (0.0%)
2025-07-15 19:01:19.649 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 数据模型更新: 0.00ms (0.0%)
2025-07-15 19:01:19.649 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 表头设置: 0.00ms (0.0%)
2025-07-15 19:01:19.649 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 设置行数: 0.00ms (0.0%)
2025-07-15 19:01:19.649 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 填充可见数据: 31.31ms (66.5%)
2025-07-15 19:01:19.649 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 调整列宽: 0.00ms (0.0%)
2025-07-15 19:01:19.649 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 设置行号: 0.00ms (0.0%)
2025-07-15 19:01:19.649 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2180 | 🔧 [性能分析] 最耗时步骤: 填充可见数据 (31.31ms)
2025-07-15 19:01:19.649 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 50
2025-07-15 19:01:19.649 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-15 19:01:27.087 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_changed:5399 | 排序变化: 1 列
2025-07-15 19:01:27.087 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_changed:5411 | 🔧 [调试] 数据重载状态正常，继续处理排序
2025-07-15 19:01:27.087 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_changed:5415 | 🔧 [调试] 开始更新排序指示器
2025-07-15 19:01:27.087 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_sort_indicators:5794 | 🔧 [排序指示器修复] 设置排序指示器: 列6, 顺序ascending
2025-07-15 19:01:27.087 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_changed:5417 | 🔧 [调试] 排序指示器更新完成
2025-07-15 19:01:27.087 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_changed:5423 | 🔧 [调试] 准备调用_save_and_apply_sort_state
2025-07-15 19:01:27.087 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5439 | 🔧 [调试] 开始保存排序状态: 1 列
2025-07-15 19:01:27.087 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5455 | 🔧 [调试] 已获取主窗口: PrototypeMainWindow
2025-07-15 19:01:27.087 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5461 | 🔧 [调试] 表名: , 表类型: 
2025-07-15 19:01:27.087 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5464 | 🔧 [调试] 无法获取当前表名，跳过全表排序
2025-07-15 19:01:27.087 | INFO     | src.gui.multi_column_sort_manager:add_sort_column:274 | 添加排序列: 6(grade_salary_2025) -> ascending, 当前排序列数: 1
2025-07-15 19:01:27.103 | INFO     | src.gui.multi_column_sort_manager:_on_sort_indicator_changed:211 | 排序指示器变化: 列6(grade_salary_2025) -> ascending
2025-07-15 19:01:27.103 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_header_sort_changed:5887 | 🔧 [表格排序] 表头排序变化: 列6, 顺序ASC
2025-07-15 19:01:27.103 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_changed:5399 | 排序变化: 1 列
2025-07-15 19:01:27.103 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_changed:5411 | 🔧 [调试] 数据重载状态正常，继续处理排序
2025-07-15 19:01:27.103 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_changed:5415 | 🔧 [调试] 开始更新排序指示器
2025-07-15 19:01:27.103 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_sort_indicators:5794 | 🔧 [排序指示器修复] 设置排序指示器: 列6, 顺序ascending
2025-07-15 19:01:27.103 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_changed:5417 | 🔧 [调试] 排序指示器更新完成
2025-07-15 19:01:27.103 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_changed:5423 | 🔧 [调试] 准备调用_save_and_apply_sort_state
2025-07-15 19:01:27.103 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5439 | 🔧 [调试] 开始保存排序状态: 1 列
2025-07-15 19:01:27.103 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5455 | 🔧 [调试] 已获取主窗口: PrototypeMainWindow
2025-07-15 19:01:27.103 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5461 | 🔧 [调试] 表名: , 表类型: 
2025-07-15 19:01:27.103 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5464 | 🔧 [调试] 无法获取当前表名，跳过全表排序
2025-07-15 19:01:27.103 | INFO     | src.gui.multi_column_sort_manager:add_sort_column:274 | 添加排序列: 6(grade_salary_2025) -> ascending, 当前排序列数: 1
2025-07-15 19:01:27.103 | INFO     | src.gui.multi_column_sort_manager:_on_sort_indicator_changed:211 | 排序指示器变化: 列6(grade_salary_2025) -> ascending
2025-07-15 19:01:27.103 | INFO     | src.gui.prototype.prototype_main_window:_on_sort_indicator_changed:3148 | 🔧 [全局排序] 排序请求: 列6, 顺序ASC
2025-07-15 19:01:27.103 | INFO     | src.gui.prototype.prototype_main_window:_get_table_field_mapping:3384 | 🔧 [字段映射] 加载映射成功: salary_data_2025_07_active_employees, 28个字段
2025-07-15 19:01:27.103 | WARNING  | src.gui.prototype.prototype_main_window:_convert_column_name_to_db_field:3366 | 🔧 [字段转换] 未找到字段映射: grade_salary_2025
2025-07-15 19:01:27.103 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:3315 | 🔧 [排序] 字段转换: grade_salary_2025 -> grade_salary_2025
2025-07-15 19:01:27.103 | INFO     | src.services.table_data_service:_handle_sort_request:135 | 处理排序请求: salary_data_2025_07_active_employees
2025-07-15 19:01:27.103 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:3334 | 🔧 [排序] 已发布排序请求事件: salary_data_2025_07_active_employees, 1列
2025-07-15 19:01:27.103 | INFO     | src.gui.prototype.prototype_main_window:_on_sort_indicator_changed:3181 | 🔧 [新架构排序] 排序完成: grade_salary_2025 ascending, 保持第1页
2025-07-15 19:01:27.119 | INFO     | src.core.unified_data_request_manager:request_table_data:177 | 开始处理数据请求: salary_data_2025_07_active_employees, 类型: sort_change
2025-07-15 19:01:27.119 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:634 | 🔧 [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_07_active_employees' 中（类型: REAL）
2025-07-15 19:01:27.119 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:487 | 正在从表 salary_data_2025_07_active_employees 分页获取数据（支持排序）: 第1页, 每页50条, 排序=1列
2025-07-15 19:01:27.119 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:516 | 🔧 [排序修复] 准备排序列: grade_salary_2025
2025-07-15 19:01:27.135 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:634 | 🔧 [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_07_active_employees' 中（类型: REAL）
2025-07-15 19:01:27.135 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:523 | 🔧 [排序修复] 列 'grade_salary_2025' 验证通过，开始排序
2025-07-15 19:01:27.135 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:532 | 🔧 [排序修复] 数值列排序: grade_salary_2025 ASC
2025-07-15 19:01:27.135 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:539 | 🔧 [排序修复] 添加排序子句: CAST("grade_salary_2025" AS REAL) ASC
2025-07-15 19:01:27.135 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:547 | 🔧 [排序调试] 执行的完整SQL查询: SELECT * FROM "salary_data_2025_07_active_employees" ORDER BY CAST("grade_salary_2025" AS REAL) ASC LIMIT 50 OFFSET 0
2025-07-15 19:01:27.135 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:559 | 🔧 [排序调试] 查询结果中 grade_salary_2025 的前10个值: [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 596.0]
2025-07-15 19:01:27.135 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:561 | 成功从表 salary_data_2025_07_active_employees 获取第1页数据（含排序）: 50 行，总计1396行
2025-07-15 19:01:27.135 | INFO     | src.core.unified_data_request_manager:request_table_data:226 | 数据请求处理完成: 28字段, 50行, 耗时15.9ms
2025-07-15 19:01:27.135 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3004 | 🆕 接收到新架构数据更新事件: salary_data_2025_07_active_employees
2025-07-15 19:01:27.135 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3029 | 数据内容: 50行 x 28列
2025-07-15 19:01:27.135 | INFO     | src.gui.prototype.prototype_main_window:set_data:642 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-07-15 19:01:27.262 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:133 | 使用已存在的配置文件: state/data/field_mappings.json
2025-07-15 19:01:27.262 | INFO     | src.modules.data_import.config_sync_manager:__init__:70 | 配置同步管理器初始化完成
2025-07-15 19:01:27.262 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:1162 | 🔧 [修复] 没有找到匹配的字段映射
2025-07-15 19:01:27.262 | INFO     | src.gui.prototype.prototype_main_window:_apply_system_field_filtering:1194 | 🔧 [修复] 过滤了 4 个系统字段
2025-07-15 19:01:27.376 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:133 | 使用已存在的配置文件: state/data/field_mappings.json
2025-07-15 19:01:27.376 | INFO     | src.modules.data_import.config_sync_manager:__init__:70 | 配置同步管理器初始化完成
2025-07-15 19:01:27.376 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:1231 | 🔧 [修复] 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-15 19:01:27.407 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3558 | 最大可见行数已更新: 50 -> 50
2025-07-15 19:01:27.407 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:3611 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-15 19:01:27.407 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第0行数据工号: 19961347.0
2025-07-15 19:01:27.407 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第1行数据工号: 20251003.0
2025-07-15 19:01:27.407 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第2行数据工号: 20251006.0
2025-07-15 19:01:27.426 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第3行数据工号: 20251007.0
2025-07-15 19:01:27.426 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第4行数据工号: 20251008.0
2025-07-15 19:01:27.426 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:457 | 🔧 [排序修复] 可见行数据顺序: ['19961347.0', '20251003.0', '20251006.0', '20251007.0', '20251008.0']
2025-07-15 19:01:27.426 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:424 | 表格数据已设置: 50 行, 24 列
2025-07-15 19:01:27.533 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2172 | 🔧 [性能分析] 表格数据设置完成: 50 行, 最大可见行数: 50, 总耗时: 156.82ms
2025-07-15 19:01:27.533 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 初始化和状态: 31.36ms (20.0%)
2025-07-15 19:01:27.533 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 字段映射: 0.00ms (0.0%)
2025-07-15 19:01:27.533 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 数据模型更新: 18.66ms (11.9%)
2025-07-15 19:01:27.533 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 表头设置: 44.08ms (28.1%)
2025-07-15 19:01:27.533 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 设置行数: 0.00ms (0.0%)
2025-07-15 19:01:27.552 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 填充可见数据: 62.73ms (40.0%)
2025-07-15 19:01:27.552 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 调整列宽: 0.00ms (0.0%)
2025-07-15 19:01:27.552 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 设置行号: 0.00ms (0.0%)
2025-07-15 19:01:27.552 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2180 | 🔧 [性能分析] 最耗时步骤: 填充可见数据 (62.73ms)
2025-07-15 19:01:27.552 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 50
2025-07-15 19:01:27.552 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-15 19:01:27.552 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3055 | 🔧 [分页修复] 数据更新事件设置总记录数: 1396
2025-07-15 19:01:27.552 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3066 | 🔧 [分页修复] 分页状态: 当前页=1, 总页数=28, 总记录数=1396
2025-07-15 19:01:27.552 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3072 | 🔧 [分页修复] 按钮可用性: 下一页=True, 上一页=False
2025-07-15 19:01:36.860 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:975 | 🔧 [紧急修复] 开始分页处理: 第2页, 表名: salary_data_2025_07_active_employees
2025-07-15 19:01:36.860 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:439 | 正在从表 salary_data_2025_07_active_employees 分页获取数据: 第2页, 每页50条
2025-07-15 19:01:36.876 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:462 | 成功从表 salary_data_2025_07_active_employees 获取第2页数据: 50 行，总计1396行
2025-07-15 19:01:36.984 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:133 | 使用已存在的配置文件: state/data/field_mappings.json
2025-07-15 19:01:36.984 | INFO     | src.modules.data_import.config_sync_manager:__init__:70 | 配置同步管理器初始化完成
2025-07-15 19:01:36.984 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:1162 | 🔧 [修复] 没有找到匹配的字段映射
2025-07-15 19:01:36.984 | INFO     | src.gui.prototype.prototype_main_window:_apply_system_field_filtering:1194 | 🔧 [修复] 过滤了 4 个系统字段
2025-07-15 19:01:37.096 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:133 | 使用已存在的配置文件: state/data/field_mappings.json
2025-07-15 19:01:37.096 | INFO     | src.modules.data_import.config_sync_manager:__init__:70 | 配置同步管理器初始化完成
2025-07-15 19:01:37.096 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:1231 | 🔧 [修复] 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-15 19:01:37.096 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:238 | 开始格式化工资表数据 - 表类型: salary_data_2025_07_active_employees
2025-07-15 19:01:37.096 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:251 | 原始数据: 50行, 24列
2025-07-15 19:01:37.096 | INFO     | src.modules.data_storage.salary_data_formatter:_extract_table_type_from_name:335 | 🔧 [表名映射] 通过关键词 'active_employees' 匹配到表类型: active_employees
2025-07-15 19:01:37.096 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:255 | 🔧 [表名映射] 输入表名: salary_data_2025_07_active_employees, 提取的表类型: active_employees
2025-07-15 19:01:37.096 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:259 | 使用表类型配置: active_employees, 配置字段: ['id_fields', 'string_fields', 'integer_fields', 'float_fields', 'special_fields']
2025-07-15 19:01:37.096 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:263 | 🔧 [格式化调试] 开始处理ID字段: ['employee_id', 'employee_type_code', '工号', '人员类别代码']
2025-07-15 19:01:37.096 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:268 | 🔧 [格式化调试] 处理前 employee_id 字段样例: ['20171604.0', '20181638.0', '19930191.0']
2025-07-15 19:01:37.096 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:268 | 🔧 [格式化调试] 处理前 employee_type_code 字段样例: ['1.0', '1.0', '1.0']
2025-07-15 19:01:37.096 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:377 | 🔧 [强化调试] 处理ID字段 employee_id: 原始样例=['20171604.0', '20181638.0', '19930191.0'], 原始类型=object
2025-07-15 19:01:37.096 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:426 | 🔧 [强化调试] 清理后ID字段 employee_id: 样例=['20171604', '20181638', '19930191']
2025-07-15 19:01:37.096 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:443 | 🔧 [强化调试] 最终ID字段 employee_id: 样例=['20171604', '20181638', '19930191']
2025-07-15 19:01:37.096 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:454 | 🔧 [验证成功] ID字段 employee_id 格式化完成，无小数点问题
2025-07-15 19:01:37.096 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:377 | 🔧 [强化调试] 处理ID字段 employee_type_code: 原始样例=['1.0', '1.0', '1.0'], 原始类型=object
2025-07-15 19:01:37.113 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:426 | 🔧 [强化调试] 清理后ID字段 employee_type_code: 样例=['1', '1', '1']
2025-07-15 19:01:37.113 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:443 | 🔧 [强化调试] 最终ID字段 employee_type_code: 样例=['01', '01', '01']
2025-07-15 19:01:37.113 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:454 | 🔧 [验证成功] ID字段 employee_type_code 格式化完成，无小数点问题
2025-07-15 19:01:37.113 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_id_fields:461 | 🔧 [警告] ID字段 工号 不存在于数据框中
2025-07-15 19:01:37.113 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_id_fields:461 | 🔧 [警告] ID字段 人员类别代码 不存在于数据框中
2025-07-15 19:01:37.113 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:274 | 🔧 [格式化调试] 处理后 employee_id 字段样例: ['20171604', '20181638', '19930191']
2025-07-15 19:01:37.113 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:274 | 🔧 [格式化调试] 处理后 employee_type_code 字段样例: ['01', '01', '01']
2025-07-15 19:01:37.113 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:472 | 🔧 [强化调试] 开始格式化字符串字段: ['employee_id', 'employee_name', 'department', 'employee_type_code', 'employee_type', '工号', '姓名', '部门名称', '人员类别代码', '人员类别']
2025-07-15 19:01:37.128 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 employee_id: 原始样例=['20171604', '20181638', '19930191'], 原始类型=object
2025-07-15 19:01:37.128 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:504 | 🔧 [修复] 工号字段 employee_id 已特殊处理，去除小数点格式
2025-07-15 19:01:37.128 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 employee_id: 样例=['20171604', '20181638', '19930191'], 类型=object
2025-07-15 19:01:37.128 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 employee_id 格式化完成，无小数点问题
2025-07-15 19:01:37.128 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 employee_name: 原始样例=['周荔', '申子宇', '周浩'], 原始类型=object
2025-07-15 19:01:37.128 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 employee_name: 样例=['周荔', '申子宇', '周浩'], 类型=object
2025-07-15 19:01:37.128 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 employee_name 格式化完成，无小数点问题
2025-07-15 19:01:37.128 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 department: 原始样例=['资源环境科学与工程学院', '资源环境科学与工程学院', '资源环境科学与工程学院'], 原始类型=object
2025-07-15 19:01:37.128 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 department: 样例=['资源环境科学与工程学院', '资源环境科学与工程学院', '资源环境科学与工程学院'], 类型=object
2025-07-15 19:01:37.128 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 department 格式化完成，无小数点问题
2025-07-15 19:01:37.128 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 employee_type_code: 原始样例=['01', '01', '01'], 原始类型=object
2025-07-15 19:01:37.128 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 employee_type_code: 样例=['01', '01', '01'], 类型=object
2025-07-15 19:01:37.128 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 employee_type_code 格式化完成，无小数点问题
2025-07-15 19:01:37.128 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 employee_type: 原始样例=['教学单位专技人员', '教学单位专技人员', '教学院其它人员'], 原始类型=object
2025-07-15 19:01:37.144 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 employee_type: 样例=['教学单位专技人员', '教学单位专技人员', '教学院其它人员'], 类型=object
2025-07-15 19:01:37.144 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 employee_type 格式化完成，无小数点问题
2025-07-15 19:01:37.144 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 工号 不存在于数据框中
2025-07-15 19:01:37.144 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 姓名 不存在于数据框中
2025-07-15 19:01:37.144 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 部门名称 不存在于数据框中
2025-07-15 19:01:37.144 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 人员类别代码 不存在于数据框中
2025-07-15 19:01:37.144 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 人员类别 不存在于数据框中
2025-07-15 19:01:37.159 | INFO     | src.modules.data_storage.salary_data_formatter:_format_integer_fields:604 | 🔧 [强化调试] 开始格式化整型字段: []
2025-07-15 19:01:37.159 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:655 | 🔧 [强化调试] 开始格式化浮点型字段: ['position_salary_2025', 'grade_salary_2025', 'allowance', 'balance_allowance', 'basic_performance_2025', 'health_fee', 'transport_allowance', 'property_allowance', 'communication_allowance', 'performance_bonus_2025', 'provident_fund_2025', 'housing_allowance', 'car_allowance', 'supplement', 'advance', 'total_salary', 'pension_insurance', '2025年岗位工资', '2025年薪级工资', '津贴', '结余津贴', '2025年基础性绩效', '卫生费', '交通补贴', '物业补贴', '通讯补贴', '2025年奖励性绩效预发', '2025公积金', '住房补贴', '车补', '补发', '借支', '应发工资', '代扣代存养老保险']
2025-07-15 19:01:37.159 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 position_salary_2025: 原始样例=[1925.0, 3030.0, 3455.0], 原始类型=float64
2025-07-15 19:01:37.159 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 position_salary_2025: 样例=[1925.0, 3030.0, 3455.0], 类型=float64
2025-07-15 19:01:37.159 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 position_salary_2025 格式化完成，保留两位小数
2025-07-15 19:01:37.159 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 grade_salary_2025: 原始样例=[1427.0, 1515.0, 3391.0], 原始类型=float64
2025-07-15 19:01:37.159 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 grade_salary_2025: 样例=[1427.0, 1515.0, 3391.0], 类型=float64
2025-07-15 19:01:37.159 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 grade_salary_2025 格式化完成，保留两位小数
2025-07-15 19:01:37.159 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 allowance: 原始样例=[0.0, 0.0, 134.0], 原始类型=float64
2025-07-15 19:01:37.159 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 allowance: 样例=[0.0, 0.0, 134.0], 类型=float64
2025-07-15 19:01:37.159 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 allowance 格式化完成，保留两位小数
2025-07-15 19:01:37.159 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 balance_allowance: 原始样例=[56.0, 56.0, 56.0], 原始类型=float64
2025-07-15 19:01:37.159 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 balance_allowance: 样例=[56.0, 56.0, 56.0], 类型=float64
2025-07-15 19:01:37.159 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 balance_allowance 格式化完成，保留两位小数
2025-07-15 19:01:37.159 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 basic_performance_2025: 原始样例=[2824.0, 3466.0, 4108.0], 原始类型=float64
2025-07-15 19:01:37.159 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 basic_performance_2025: 样例=[2824.0, 3466.0, 4108.0], 类型=float64
2025-07-15 19:01:37.159 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 basic_performance_2025 格式化完成，保留两位小数
2025-07-15 19:01:37.159 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 health_fee: 原始样例=[0.0, nan, 0.0], 原始类型=float64
2025-07-15 19:01:37.175 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 health_fee: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-15 19:01:37.175 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 health_fee 格式化完成，保留两位小数
2025-07-15 19:01:37.175 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 transport_allowance: 原始样例=[220.0, 220.0, 220.0], 原始类型=float64
2025-07-15 19:01:37.175 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 transport_allowance: 样例=[220.0, 220.0, 220.0], 类型=float64
2025-07-15 19:01:37.175 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 transport_allowance 格式化完成，保留两位小数
2025-07-15 19:01:37.175 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 property_allowance: 原始样例=[200.0, 240.0, 240.0], 原始类型=float64
2025-07-15 19:01:37.191 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 property_allowance: 样例=[200.0, 240.0, 240.0], 类型=float64
2025-07-15 19:01:37.191 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 property_allowance 格式化完成，保留两位小数
2025-07-15 19:01:37.191 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 communication_allowance: 原始样例=[nan, nan, 50.0], 原始类型=float64
2025-07-15 19:01:37.191 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 communication_allowance: 样例=[0.0, 0.0, 50.0], 类型=float64
2025-07-15 19:01:37.191 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 communication_allowance 格式化完成，保留两位小数
2025-07-15 19:01:37.191 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 performance_bonus_2025: 原始样例=[1000.0, 0.0, 2400.0], 原始类型=float64
2025-07-15 19:01:37.191 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 performance_bonus_2025: 样例=[1000.0, 0.0, 2400.0], 类型=float64
2025-07-15 19:01:37.191 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 performance_bonus_2025 格式化完成，保留两位小数
2025-07-15 19:01:37.191 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 provident_fund_2025: 原始样例=[1428.0, 2216.0, 2274.0], 原始类型=float64
2025-07-15 19:01:37.191 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 provident_fund_2025: 样例=[1428.0, 2216.0, 2274.0], 类型=float64
2025-07-15 19:01:37.191 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 provident_fund_2025 格式化完成，保留两位小数
2025-07-15 19:01:37.191 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 housing_allowance: 原始样例=[188.76571428819443, 204.8512760416667, 315.672219058336], 原始类型=float64
2025-07-15 19:01:37.191 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 housing_allowance: 样例=[188.77, 204.85, 315.67], 类型=float64
2025-07-15 19:01:37.191 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 housing_allowance 格式化完成，保留两位小数
2025-07-15 19:01:37.191 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 car_allowance: 原始样例=[None, None, None], 原始类型=object
2025-07-15 19:01:37.191 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 car_allowance: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-15 19:01:37.191 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 car_allowance 格式化完成，保留两位小数
2025-07-15 19:01:37.207 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 supplement: 原始样例=[None, None, None], 原始类型=object
2025-07-15 19:01:37.207 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 supplement: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-15 19:01:37.207 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 supplement 格式化完成，保留两位小数
2025-07-15 19:01:37.207 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 advance: 原始样例=[nan, 2799.4, nan], 原始类型=float64
2025-07-15 19:01:37.221 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 advance: 样例=[0.0, 2799.4, 0.0], 类型=float64
2025-07-15 19:01:37.221 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 advance 格式化完成，保留两位小数
2025-07-15 19:01:37.221 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 total_salary: 原始样例=[7840.765714288194, 5932.451276041667, 14369.672219058337], 原始类型=float64
2025-07-15 19:01:37.221 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 total_salary: 样例=[7840.77, 5932.45, 14369.67], 类型=float64
2025-07-15 19:01:37.221 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 total_salary 格式化完成，保留两位小数
2025-07-15 19:01:37.221 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 pension_insurance: 原始样例=[977.64, 1416.96, 1867.56], 原始类型=float64
2025-07-15 19:01:37.221 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 pension_insurance: 样例=[977.64, 1416.96, 1867.56], 类型=float64
2025-07-15 19:01:37.221 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 pension_insurance 格式化完成，保留两位小数
2025-07-15 19:01:37.221 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 2025年岗位工资 不存在于数据框中
2025-07-15 19:01:37.221 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 2025年薪级工资 不存在于数据框中
2025-07-15 19:01:37.221 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 津贴 不存在于数据框中
2025-07-15 19:01:37.221 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 结余津贴 不存在于数据框中
2025-07-15 19:01:37.221 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 2025年基础性绩效 不存在于数据框中
2025-07-15 19:01:37.221 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 卫生费 不存在于数据框中
2025-07-15 19:01:37.221 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 交通补贴 不存在于数据框中
2025-07-15 19:01:37.221 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 物业补贴 不存在于数据框中
2025-07-15 19:01:37.221 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 通讯补贴 不存在于数据框中
2025-07-15 19:01:37.221 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 2025年奖励性绩效预发 不存在于数据框中
2025-07-15 19:01:37.221 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 2025公积金 不存在于数据框中
2025-07-15 19:01:37.221 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 住房补贴 不存在于数据框中
2025-07-15 19:01:37.237 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 车补 不存在于数据框中
2025-07-15 19:01:37.237 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 补发 不存在于数据框中
2025-07-15 19:01:37.237 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 借支 不存在于数据框中
2025-07-15 19:01:37.237 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 应发工资 不存在于数据框中
2025-07-15 19:01:37.237 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 代扣代存养老保险 不存在于数据框中
2025-07-15 19:01:37.237 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:711 | 🔧 [特殊字段] 开始处理表类型 active_employees 的月份、年份字段
2025-07-15 19:01:37.253 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:731 | 🔧 [特殊字段] 处理月份字段 month: 原始样例=['2025-07', '2025-07', '2025-07'], 原始类型=object
2025-07-15 19:01:37.253 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:774 | 🔧 [特殊字段] 处理后月份字段 month: 样例=['07', '07', '07']
2025-07-15 19:01:37.253 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:785 | 🔧 [验证成功] 月份字段 month 格式化完成，所有值为有效月份格式
2025-07-15 19:01:37.253 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:805 | 🔧 [特殊字段] 处理年份字段 year: 原始样例=[2025, 2025, 2025], 原始类型=int64
2025-07-15 19:01:37.253 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:845 | 🔧 [特殊字段] 处理后年份字段 year: 样例=['2025', '2025', '2025']
2025-07-15 19:01:37.253 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:856 | 🔧 [验证成功] 年份字段 year 格式化完成，所有值为有效年份格式
2025-07-15 19:01:37.253 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:866 | 🔧 [特殊字段] 月份、年份字段处理完成
2025-07-15 19:01:37.253 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:291 | 数据格式化完成: 50行, 24列
2025-07-15 19:01:37.284 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3558 | 最大可见行数已更新: 50 -> 50
2025-07-15 19:01:37.284 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:3611 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-15 19:01:37.284 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2235 | 🔧 [修复标识] 字段映射生效，样例: ['employee_id', 'employee_name', 'department'] -> ['工号', '姓名', '部门名称']
2025-07-15 19:01:37.284 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第0行数据工号: 20171604
2025-07-15 19:01:37.284 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第1行数据工号: 20181638
2025-07-15 19:01:37.301 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第2行数据工号: 19930191
2025-07-15 19:01:37.301 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第3行数据工号: 20181669
2025-07-15 19:01:37.301 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第4行数据工号: 20191722
2025-07-15 19:01:37.301 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:457 | 🔧 [排序修复] 可见行数据顺序: ['20171604', '20181638', '19930191', '20181669', '20191722']
2025-07-15 19:01:37.301 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:424 | 表格数据已设置: 50 行, 24 列
2025-07-15 19:01:37.301 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2361 | 🔧 [表格调试] 开始格式化ID字段: 列=工号, 原值=20171604, 类型=<class 'str'>
2025-07-15 19:01:37.301 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2368 | 🔧 [表格调试] ID字段格式化完成: 列=工号, 原值=20171604, 格式化后=20171604
2025-07-15 19:01:37.301 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2361 | 🔧 [表格调试] 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-15 19:01:37.301 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2368 | 🔧 [表格调试] ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-15 19:01:37.331 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2172 | 🔧 [性能分析] 表格数据设置完成: 50 行, 最大可见行数: 50, 总耗时: 78.31ms
2025-07-15 19:01:37.331 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 初始化和状态: 31.33ms (40.0%)
2025-07-15 19:01:37.331 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 字段映射: 0.00ms (0.0%)
2025-07-15 19:01:37.331 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 数据模型更新: 17.14ms (21.9%)
2025-07-15 19:01:37.331 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 表头设置: 0.00ms (0.0%)
2025-07-15 19:01:37.331 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 设置行数: 0.00ms (0.0%)
2025-07-15 19:01:37.331 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 填充可见数据: 29.85ms (38.1%)
2025-07-15 19:01:37.331 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 调整列宽: 0.00ms (0.0%)
2025-07-15 19:01:37.331 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 设置行号: 0.00ms (0.0%)
2025-07-15 19:01:37.331 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2180 | 🔧 [性能分析] 最耗时步骤: 初始化和状态 (31.33ms)
2025-07-15 19:01:37.331 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:1049 | 🔧 [紧急修复] 分页处理完成: 第2页, 50条记录
2025-07-15 19:01:37.348 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3464 | 🔧 [紧急修复] 新架构分页: 第2页
2025-07-15 19:01:37.348 | INFO     | src.services.table_data_service:load_table_data:291 | 加载表格数据: salary_data_2025_07_active_employees, 页码: 2
2025-07-15 19:01:37.348 | INFO     | src.core.unified_data_request_manager:request_table_data:177 | 开始处理数据请求: salary_data_2025_07_active_employees, 类型: initial_load
2025-07-15 19:01:37.348 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:487 | 正在从表 salary_data_2025_07_active_employees 分页获取数据（支持排序）: 第2页, 每页50条, 排序=0列
2025-07-15 19:01:37.348 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:547 | 🔧 [排序调试] 执行的完整SQL查询: SELECT * FROM "salary_data_2025_07_active_employees" LIMIT 50 OFFSET 50
2025-07-15 19:01:37.362 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:561 | 成功从表 salary_data_2025_07_active_employees 获取第2页数据（含排序）: 50 行，总计1396行
2025-07-15 19:01:37.362 | INFO     | src.core.unified_data_request_manager:request_table_data:226 | 数据请求处理完成: 28字段, 50行, 耗时14.7ms
2025-07-15 19:01:37.362 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3004 | 🆕 接收到新架构数据更新事件: salary_data_2025_07_active_employees
2025-07-15 19:01:37.362 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3029 | 数据内容: 50行 x 28列
2025-07-15 19:01:37.362 | INFO     | src.gui.prototype.prototype_main_window:set_data:642 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-07-15 19:01:37.470 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:133 | 使用已存在的配置文件: state/data/field_mappings.json
2025-07-15 19:01:37.470 | INFO     | src.modules.data_import.config_sync_manager:__init__:70 | 配置同步管理器初始化完成
2025-07-15 19:01:37.470 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:1162 | 🔧 [修复] 没有找到匹配的字段映射
2025-07-15 19:01:37.470 | INFO     | src.gui.prototype.prototype_main_window:_apply_system_field_filtering:1194 | 🔧 [修复] 过滤了 4 个系统字段
2025-07-15 19:01:37.583 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:133 | 使用已存在的配置文件: state/data/field_mappings.json
2025-07-15 19:01:37.583 | INFO     | src.modules.data_import.config_sync_manager:__init__:70 | 配置同步管理器初始化完成
2025-07-15 19:01:37.583 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:1231 | 🔧 [修复] 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-15 19:01:37.614 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3558 | 最大可见行数已更新: 50 -> 50
2025-07-15 19:01:37.614 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:3611 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-15 19:01:37.614 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2235 | 🔧 [修复标识] 字段映射生效，样例: ['employee_id', 'employee_name', 'department'] -> ['工号', '姓名', '部门名称']
2025-07-15 19:01:37.614 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第0行数据工号: 20171604.0
2025-07-15 19:01:37.614 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第1行数据工号: 20181638.0
2025-07-15 19:01:37.614 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第2行数据工号: 19930191.0
2025-07-15 19:01:37.614 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第3行数据工号: 20181669.0
2025-07-15 19:01:37.614 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第4行数据工号: 20191722.0
2025-07-15 19:01:37.614 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:457 | 🔧 [排序修复] 可见行数据顺序: ['20171604.0', '20181638.0', '19930191.0', '20181669.0', '20191722.0']
2025-07-15 19:01:37.614 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:424 | 表格数据已设置: 50 行, 24 列
2025-07-15 19:01:37.614 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2361 | 🔧 [表格调试] 开始格式化ID字段: 列=工号, 原值=20171604.0, 类型=<class 'str'>
2025-07-15 19:01:37.614 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2368 | 🔧 [表格调试] ID字段格式化完成: 列=工号, 原值=20171604.0, 格式化后=20171604.0
2025-07-15 19:01:37.614 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2361 | 🔧 [表格调试] 开始格式化ID字段: 列=人员类别代码, 原值=1.0, 类型=<class 'str'>
2025-07-15 19:01:37.614 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2368 | 🔧 [表格调试] ID字段格式化完成: 列=人员类别代码, 原值=1.0, 格式化后=1.0
2025-07-15 19:01:37.652 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2172 | 🔧 [性能分析] 表格数据设置完成: 50 行, 最大可见行数: 50, 总耗时: 69.11ms
2025-07-15 19:01:37.652 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 初始化和状态: 31.39ms (45.4%)
2025-07-15 19:01:37.661 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 字段映射: 0.00ms (0.0%)
2025-07-15 19:01:37.661 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 数据模型更新: 0.00ms (0.0%)
2025-07-15 19:01:37.661 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 表头设置: 0.00ms (0.0%)
2025-07-15 19:01:37.661 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 设置行数: 0.00ms (0.0%)
2025-07-15 19:01:37.661 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 填充可见数据: 37.72ms (54.6%)
2025-07-15 19:01:37.661 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 调整列宽: 0.00ms (0.0%)
2025-07-15 19:01:37.661 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 设置行号: 0.00ms (0.0%)
2025-07-15 19:01:37.661 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2180 | 🔧 [性能分析] 最耗时步骤: 填充可见数据 (37.72ms)
2025-07-15 19:01:37.661 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 50
2025-07-15 19:01:37.661 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:975 | 🔧 [紧急修复] 开始分页处理: 第1页, 表名: salary_data_2025_07_active_employees
2025-07-15 19:01:37.661 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:439 | 正在从表 salary_data_2025_07_active_employees 分页获取数据: 第1页, 每页50条
2025-07-15 19:01:37.661 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:462 | 成功从表 salary_data_2025_07_active_employees 获取第1页数据: 50 行，总计1396行
2025-07-15 19:01:37.778 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:133 | 使用已存在的配置文件: state/data/field_mappings.json
2025-07-15 19:01:37.810 | INFO     | src.modules.data_import.config_sync_manager:__init__:70 | 配置同步管理器初始化完成
2025-07-15 19:01:37.826 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:1162 | 🔧 [修复] 没有找到匹配的字段映射
2025-07-15 19:01:37.826 | INFO     | src.gui.prototype.prototype_main_window:_apply_system_field_filtering:1194 | 🔧 [修复] 过滤了 4 个系统字段
2025-07-15 19:01:37.934 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:133 | 使用已存在的配置文件: state/data/field_mappings.json
2025-07-15 19:01:37.934 | INFO     | src.modules.data_import.config_sync_manager:__init__:70 | 配置同步管理器初始化完成
2025-07-15 19:01:37.934 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:1231 | 🔧 [修复] 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-15 19:01:37.934 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:238 | 开始格式化工资表数据 - 表类型: salary_data_2025_07_active_employees
2025-07-15 19:01:37.934 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:251 | 原始数据: 50行, 24列
2025-07-15 19:01:37.934 | INFO     | src.modules.data_storage.salary_data_formatter:_extract_table_type_from_name:335 | 🔧 [表名映射] 通过关键词 'active_employees' 匹配到表类型: active_employees
2025-07-15 19:01:37.934 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:255 | 🔧 [表名映射] 输入表名: salary_data_2025_07_active_employees, 提取的表类型: active_employees
2025-07-15 19:01:37.934 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:259 | 使用表类型配置: active_employees, 配置字段: ['id_fields', 'string_fields', 'integer_fields', 'float_fields', 'special_fields']
2025-07-15 19:01:37.934 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:263 | 🔧 [格式化调试] 开始处理ID字段: ['employee_id', 'employee_type_code', '工号', '人员类别代码']
2025-07-15 19:01:37.934 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:268 | 🔧 [格式化调试] 处理前 employee_id 字段样例: ['19990089.0', '20161565.0', '20191782.0']
2025-07-15 19:01:37.934 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:268 | 🔧 [格式化调试] 处理前 employee_type_code 字段样例: ['1.0', '1.0', '17.0']
2025-07-15 19:01:37.934 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:377 | 🔧 [强化调试] 处理ID字段 employee_id: 原始样例=['19990089.0', '20161565.0', '20191782.0'], 原始类型=object
2025-07-15 19:01:37.934 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:426 | 🔧 [强化调试] 清理后ID字段 employee_id: 样例=['19990089', '20161565', '20191782']
2025-07-15 19:01:37.934 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:443 | 🔧 [强化调试] 最终ID字段 employee_id: 样例=['19990089', '20161565', '20191782']
2025-07-15 19:01:37.934 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:454 | 🔧 [验证成功] ID字段 employee_id 格式化完成，无小数点问题
2025-07-15 19:01:37.934 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:377 | 🔧 [强化调试] 处理ID字段 employee_type_code: 原始样例=['1.0', '1.0', '17.0'], 原始类型=object
2025-07-15 19:01:37.951 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:426 | 🔧 [强化调试] 清理后ID字段 employee_type_code: 样例=['1', '1', '17']
2025-07-15 19:01:37.951 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:443 | 🔧 [强化调试] 最终ID字段 employee_type_code: 样例=['01', '01', '17']
2025-07-15 19:01:37.951 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:454 | 🔧 [验证成功] ID字段 employee_type_code 格式化完成，无小数点问题
2025-07-15 19:01:37.951 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_id_fields:461 | 🔧 [警告] ID字段 工号 不存在于数据框中
2025-07-15 19:01:37.951 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_id_fields:461 | 🔧 [警告] ID字段 人员类别代码 不存在于数据框中
2025-07-15 19:01:37.951 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:274 | 🔧 [格式化调试] 处理后 employee_id 字段样例: ['19990089', '20161565', '20191782']
2025-07-15 19:01:37.951 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:274 | 🔧 [格式化调试] 处理后 employee_type_code 字段样例: ['01', '01', '17']
2025-07-15 19:01:37.965 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:472 | 🔧 [强化调试] 开始格式化字符串字段: ['employee_id', 'employee_name', 'department', 'employee_type_code', 'employee_type', '工号', '姓名', '部门名称', '人员类别代码', '人员类别']
2025-07-15 19:01:37.965 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 employee_id: 原始样例=['19990089', '20161565', '20191782'], 原始类型=object
2025-07-15 19:01:37.965 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:504 | 🔧 [修复] 工号字段 employee_id 已特殊处理，去除小数点格式
2025-07-15 19:01:37.965 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 employee_id: 样例=['19990089', '20161565', '20191782'], 类型=object
2025-07-15 19:01:37.965 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 employee_id 格式化完成，无小数点问题
2025-07-15 19:01:37.965 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 employee_name: 原始样例=['杨胜', '胡四平', '肖啸'], 原始类型=object
2025-07-15 19:01:37.965 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 employee_name: 样例=['杨胜', '胡四平', '肖啸'], 类型=object
2025-07-15 19:01:37.965 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 employee_name 格式化完成，无小数点问题
2025-07-15 19:01:37.965 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 department: 原始样例=['自动化学院', '自动化学院', '自动化学院'], 原始类型=object
2025-07-15 19:01:37.965 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 department: 样例=['自动化学院', '自动化学院', '自动化学院'], 类型=object
2025-07-15 19:01:37.965 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 department 格式化完成，无小数点问题
2025-07-15 19:01:37.965 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 employee_type_code: 原始样例=['01', '01', '17'], 原始类型=object
2025-07-15 19:01:37.965 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 employee_type_code: 样例=['01', '01', '17'], 类型=object
2025-07-15 19:01:37.965 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 employee_type_code 格式化完成，无小数点问题
2025-07-15 19:01:37.965 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 employee_type: 原始样例=['教学院其它人员', '教学单位专技人员', '科研单位人员'], 原始类型=object
2025-07-15 19:01:37.981 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 employee_type: 样例=['教学院其它人员', '教学单位专技人员', '科研单位人员'], 类型=object
2025-07-15 19:01:37.981 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 employee_type 格式化完成，无小数点问题
2025-07-15 19:01:37.981 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 工号 不存在于数据框中
2025-07-15 19:01:37.981 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 姓名 不存在于数据框中
2025-07-15 19:01:37.981 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 部门名称 不存在于数据框中
2025-07-15 19:01:37.981 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 人员类别代码 不存在于数据框中
2025-07-15 19:01:37.981 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 人员类别 不存在于数据框中
2025-07-15 19:01:37.981 | INFO     | src.modules.data_storage.salary_data_formatter:_format_integer_fields:604 | 🔧 [强化调试] 开始格式化整型字段: []
2025-07-15 19:01:37.981 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:655 | 🔧 [强化调试] 开始格式化浮点型字段: ['position_salary_2025', 'grade_salary_2025', 'allowance', 'balance_allowance', 'basic_performance_2025', 'health_fee', 'transport_allowance', 'property_allowance', 'communication_allowance', 'performance_bonus_2025', 'provident_fund_2025', 'housing_allowance', 'car_allowance', 'supplement', 'advance', 'total_salary', 'pension_insurance', '2025年岗位工资', '2025年薪级工资', '津贴', '结余津贴', '2025年基础性绩效', '卫生费', '交通补贴', '物业补贴', '通讯补贴', '2025年奖励性绩效预发', '2025公积金', '住房补贴', '车补', '补发', '借支', '应发工资', '代扣代存养老保险']
2025-07-15 19:01:37.998 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 position_salary_2025: 原始样例=[2880.0, 3030.0, 2185.0], 原始类型=float64
2025-07-15 19:01:37.998 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 position_salary_2025: 样例=[2880.0, 3030.0, 2185.0], 类型=float64
2025-07-15 19:01:37.998 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 position_salary_2025 格式化完成，保留两位小数
2025-07-15 19:01:37.998 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 grade_salary_2025: 原始样例=[2375.0, 1696.0, 1427.0], 原始类型=float64
2025-07-15 19:01:37.998 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 grade_salary_2025: 样例=[2375.0, 1696.0, 1427.0], 类型=float64
2025-07-15 19:01:37.998 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 grade_salary_2025 格式化完成，保留两位小数
2025-07-15 19:01:37.998 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 allowance: 原始样例=[102.0, 0.0, 0.0], 原始类型=float64
2025-07-15 19:01:37.998 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 allowance: 样例=[102.0, 0.0, 0.0], 类型=float64
2025-07-15 19:01:37.998 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 allowance 格式化完成，保留两位小数
2025-07-15 19:01:37.998 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 balance_allowance: 原始样例=[56.0, 56.0, 56.0], 原始类型=float64
2025-07-15 19:01:37.998 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 balance_allowance: 样例=[56.0, 56.0, 56.0], 类型=float64
2025-07-15 19:01:37.998 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 balance_allowance 格式化完成，保留两位小数
2025-07-15 19:01:37.998 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 basic_performance_2025: 原始样例=[3594.0, 3466.0, 2978.0], 原始类型=float64
2025-07-15 19:01:37.998 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 basic_performance_2025: 样例=[3594.0, 3466.0, 2978.0], 类型=float64
2025-07-15 19:01:37.998 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 basic_performance_2025 格式化完成，保留两位小数
2025-07-15 19:01:37.998 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 health_fee: 原始样例=[0.0, 0.0, 0.0], 原始类型=float64
2025-07-15 19:01:38.012 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 health_fee: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-15 19:01:38.012 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 health_fee 格式化完成，保留两位小数
2025-07-15 19:01:38.012 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 transport_allowance: 原始样例=[220.0, 220.0, 220.0], 原始类型=float64
2025-07-15 19:01:38.012 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 transport_allowance: 样例=[220.0, 220.0, 220.0], 类型=float64
2025-07-15 19:01:38.012 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 transport_allowance 格式化完成，保留两位小数
2025-07-15 19:01:38.012 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 property_allowance: 原始样例=[240.0, 240.0, 200.0], 原始类型=float64
2025-07-15 19:01:38.012 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 property_allowance: 样例=[240.0, 240.0, 200.0], 类型=float64
2025-07-15 19:01:38.012 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 property_allowance 格式化完成，保留两位小数
2025-07-15 19:01:38.012 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 communication_allowance: 原始样例=[50.0, nan, nan], 原始类型=float64
2025-07-15 19:01:38.012 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 communication_allowance: 样例=[50.0, 0.0, 0.0], 类型=float64
2025-07-15 19:01:38.012 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 communication_allowance 格式化完成，保留两位小数
2025-07-15 19:01:38.012 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 performance_bonus_2025: 原始样例=[2500.0, 1000.0, 1000.0], 原始类型=float64
2025-07-15 19:01:38.012 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 performance_bonus_2025: 样例=[2500.0, 1000.0, 1000.0], 类型=float64
2025-07-15 19:01:38.012 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 performance_bonus_2025 格式化完成，保留两位小数
2025-07-15 19:01:38.012 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 provident_fund_2025: 原始样例=[2097.0, 1860.0, 1984.0], 原始类型=float64
2025-07-15 19:01:38.012 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 provident_fund_2025: 样例=[2097.0, 1860.0, 1984.0], 类型=float64
2025-07-15 19:01:38.012 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 provident_fund_2025 格式化完成，保留两位小数
2025-07-15 19:01:38.012 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 housing_allowance: 原始样例=[271.9745083294993, 189.0, 174.16354166666667], 原始类型=float64
2025-07-15 19:01:38.028 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 housing_allowance: 样例=[271.97, 189.0, 174.16], 类型=float64
2025-07-15 19:01:38.028 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 housing_allowance 格式化完成，保留两位小数
2025-07-15 19:01:38.028 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 car_allowance: 原始样例=[None, None, None], 原始类型=object
2025-07-15 19:01:38.028 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 car_allowance: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-15 19:01:38.028 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 car_allowance 格式化完成，保留两位小数
2025-07-15 19:01:38.044 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 supplement: 原始样例=[None, None, None], 原始类型=object
2025-07-15 19:01:38.044 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 supplement: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-15 19:01:38.044 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 supplement 格式化完成，保留两位小数
2025-07-15 19:01:38.044 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 advance: 原始样例=[nan, nan, 2000.0], 原始类型=float64
2025-07-15 19:01:38.044 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 advance: 样例=[0.0, 0.0, 2000.0], 类型=float64
2025-07-15 19:01:38.044 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 advance 格式化完成，保留两位小数
2025-07-15 19:01:38.044 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 total_salary: 原始样例=[12288.9745083295, 9897.0, 6240.163541666667], 原始类型=float64
2025-07-15 19:01:38.044 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 total_salary: 样例=[12288.97, 9897.0, 6240.16], 类型=float64
2025-07-15 19:01:38.044 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 total_salary 格式化完成，保留两位小数
2025-07-15 19:01:38.044 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 pension_insurance: 原始样例=[1525.8000000000002, 1140.53, 1113.75], 原始类型=float64
2025-07-15 19:01:38.044 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 pension_insurance: 样例=[1525.8, 1140.53, 1113.75], 类型=float64
2025-07-15 19:01:38.044 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 pension_insurance 格式化完成，保留两位小数
2025-07-15 19:01:38.044 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 2025年岗位工资 不存在于数据框中
2025-07-15 19:01:38.044 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 2025年薪级工资 不存在于数据框中
2025-07-15 19:01:38.044 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 津贴 不存在于数据框中
2025-07-15 19:01:38.044 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 结余津贴 不存在于数据框中
2025-07-15 19:01:38.044 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 2025年基础性绩效 不存在于数据框中
2025-07-15 19:01:38.059 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 卫生费 不存在于数据框中
2025-07-15 19:01:38.059 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 交通补贴 不存在于数据框中
2025-07-15 19:01:38.059 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 物业补贴 不存在于数据框中
2025-07-15 19:01:38.059 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 通讯补贴 不存在于数据框中
2025-07-15 19:01:38.059 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 2025年奖励性绩效预发 不存在于数据框中
2025-07-15 19:01:38.059 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 2025公积金 不存在于数据框中
2025-07-15 19:01:38.059 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 住房补贴 不存在于数据框中
2025-07-15 19:01:38.075 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 车补 不存在于数据框中
2025-07-15 19:01:38.075 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 补发 不存在于数据框中
2025-07-15 19:01:38.075 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 借支 不存在于数据框中
2025-07-15 19:01:38.075 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 应发工资 不存在于数据框中
2025-07-15 19:01:38.075 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 代扣代存养老保险 不存在于数据框中
2025-07-15 19:01:38.075 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:711 | 🔧 [特殊字段] 开始处理表类型 active_employees 的月份、年份字段
2025-07-15 19:01:38.075 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:731 | 🔧 [特殊字段] 处理月份字段 month: 原始样例=['2025-07', '2025-07', '2025-07'], 原始类型=object
2025-07-15 19:01:38.075 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:774 | 🔧 [特殊字段] 处理后月份字段 month: 样例=['07', '07', '07']
2025-07-15 19:01:38.075 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:785 | 🔧 [验证成功] 月份字段 month 格式化完成，所有值为有效月份格式
2025-07-15 19:01:38.075 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:805 | 🔧 [特殊字段] 处理年份字段 year: 原始样例=[2025, 2025, 2025], 原始类型=int64
2025-07-15 19:01:38.075 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:845 | 🔧 [特殊字段] 处理后年份字段 year: 样例=['2025', '2025', '2025']
2025-07-15 19:01:38.075 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:856 | 🔧 [验证成功] 年份字段 year 格式化完成，所有值为有效年份格式
2025-07-15 19:01:38.075 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:866 | 🔧 [特殊字段] 月份、年份字段处理完成
2025-07-15 19:01:38.075 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:291 | 数据格式化完成: 50行, 24列
2025-07-15 19:01:38.121 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3558 | 最大可见行数已更新: 50 -> 50
2025-07-15 19:01:38.121 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:3611 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-15 19:01:38.121 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2235 | 🔧 [修复标识] 字段映射生效，样例: ['employee_id', 'employee_name', 'department'] -> ['工号', '姓名', '部门名称']
2025-07-15 19:01:38.121 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第0行数据工号: 19990089
2025-07-15 19:01:38.121 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第1行数据工号: 20161565
2025-07-15 19:01:38.121 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第2行数据工号: 20191782
2025-07-15 19:01:38.121 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第3行数据工号: 20151515
2025-07-15 19:01:38.137 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第4行数据工号: 20181640
2025-07-15 19:01:38.137 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:457 | 🔧 [排序修复] 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-15 19:01:38.137 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:424 | 表格数据已设置: 50 行, 24 列
2025-07-15 19:01:38.137 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2361 | 🔧 [表格调试] 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-15 19:01:38.137 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2368 | 🔧 [表格调试] ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-15 19:01:38.137 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2361 | 🔧 [表格调试] 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-15 19:01:38.137 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2368 | 🔧 [表格调试] ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-15 19:01:38.168 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2172 | 🔧 [性能分析] 表格数据设置完成: 50 行, 最大可见行数: 50, 总耗时: 78.36ms
2025-07-15 19:01:38.168 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 初始化和状态: 31.34ms (40.0%)
2025-07-15 19:01:38.168 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 字段映射: 0.00ms (0.0%)
2025-07-15 19:01:38.168 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 数据模型更新: 15.69ms (20.0%)
2025-07-15 19:01:38.168 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 表头设置: 0.00ms (0.0%)
2025-07-15 19:01:38.168 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 设置行数: 0.00ms (0.0%)
2025-07-15 19:01:38.168 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 填充可见数据: 15.67ms (20.0%)
2025-07-15 19:01:38.168 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 调整列宽: 0.00ms (0.0%)
2025-07-15 19:01:38.168 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 设置行号: 15.67ms (20.0%)
2025-07-15 19:01:38.168 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2180 | 🔧 [性能分析] 最耗时步骤: 初始化和状态 (31.34ms)
2025-07-15 19:01:38.184 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-15 19:01:38.184 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:1049 | 🔧 [紧急修复] 分页处理完成: 第1页, 50条记录
2025-07-15 19:01:38.184 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3464 | 🔧 [紧急修复] 新架构分页: 第1页
2025-07-15 19:01:38.184 | INFO     | src.services.table_data_service:load_table_data:291 | 加载表格数据: salary_data_2025_07_active_employees, 页码: 1
2025-07-15 19:01:38.184 | INFO     | src.core.unified_data_request_manager:request_table_data:177 | 开始处理数据请求: salary_data_2025_07_active_employees, 类型: initial_load
2025-07-15 19:01:38.184 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:487 | 正在从表 salary_data_2025_07_active_employees 分页获取数据（支持排序）: 第1页, 每页50条, 排序=0列
2025-07-15 19:01:38.184 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:547 | 🔧 [排序调试] 执行的完整SQL查询: SELECT * FROM "salary_data_2025_07_active_employees" LIMIT 50 OFFSET 0
2025-07-15 19:01:38.184 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:561 | 成功从表 salary_data_2025_07_active_employees 获取第1页数据（含排序）: 50 行，总计1396行
2025-07-15 19:01:38.184 | INFO     | src.core.unified_data_request_manager:request_table_data:226 | 数据请求处理完成: 28字段, 50行, 耗时0.0ms
2025-07-15 19:01:38.184 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3004 | 🆕 接收到新架构数据更新事件: salary_data_2025_07_active_employees
2025-07-15 19:01:38.184 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3029 | 数据内容: 50行 x 28列
2025-07-15 19:01:38.184 | WARNING  | src.gui.prototype.prototype_main_window:set_data:595 | 🔧 [修复] 检测到递归设置数据，跳过
2025-07-15 19:01:38.200 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3066 | 🔧 [分页修复] 分页状态: 当前页=1, 总页数=28, 总记录数=1396
2025-07-15 19:01:38.200 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3072 | 🔧 [分页修复] 按钮可用性: 下一页=True, 上一页=False
2025-07-15 19:01:38.231 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:4784 | 🔧 [修复标识] 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-15 19:01:38.246 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3558 | 最大可见行数已更新: 50 -> 50
2025-07-15 19:01:38.246 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:3611 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-15 19:01:38.246 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第0行数据工号: 19990089.0
2025-07-15 19:01:38.246 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第1行数据工号: 20161565.0
2025-07-15 19:01:38.246 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第2行数据工号: 20191782.0
2025-07-15 19:01:38.246 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第3行数据工号: 20151515.0
2025-07-15 19:01:38.246 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第4行数据工号: 20181640.0
2025-07-15 19:01:38.246 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:457 | 🔧 [排序修复] 可见行数据顺序: ['19990089.0', '20161565.0', '20191782.0', '20151515.0', '20181640.0']
2025-07-15 19:01:38.246 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:424 | 表格数据已设置: 50 行, 24 列
2025-07-15 19:01:38.246 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2361 | 🔧 [表格调试] 开始格式化ID字段: 列=工号, 原值=19990089.0, 类型=<class 'str'>
2025-07-15 19:01:38.246 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2368 | 🔧 [表格调试] ID字段格式化完成: 列=工号, 原值=19990089.0, 格式化后=19990089.0
2025-07-15 19:01:38.262 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2361 | 🔧 [表格调试] 开始格式化ID字段: 列=人员类别代码, 原值=1.0, 类型=<class 'str'>
2025-07-15 19:01:38.262 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2368 | 🔧 [表格调试] ID字段格式化完成: 列=人员类别代码, 原值=1.0, 格式化后=1.0
2025-07-15 19:01:38.293 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2172 | 🔧 [性能分析] 表格数据设置完成: 50 行, 最大可见行数: 50, 总耗时: 47.03ms
2025-07-15 19:01:38.293 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 初始化和状态: 0.00ms (0.0%)
2025-07-15 19:01:38.293 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 字段映射: 0.00ms (0.0%)
2025-07-15 19:01:38.293 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 数据模型更新: 0.00ms (0.0%)
2025-07-15 19:01:38.293 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 表头设置: 0.00ms (0.0%)
2025-07-15 19:01:38.293 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 设置行数: 0.00ms (0.0%)
2025-07-15 19:01:38.303 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 填充可见数据: 47.03ms (100.0%)
2025-07-15 19:01:38.304 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 调整列宽: 0.00ms (0.0%)
2025-07-15 19:01:38.304 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 设置行号: 0.00ms (0.0%)
2025-07-15 19:01:38.305 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2180 | 🔧 [性能分析] 最耗时步骤: 填充可见数据 (47.03ms)
2025-07-15 19:01:38.306 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-15 19:01:38.307 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3526 | 🔧 [紧急修复] 新架构分页成功: 第1页, 50行
2025-07-15 19:01:38.313 | INFO     | src.gui.widgets.pagination_widget:set_current_page:330 | 页码切换到: 1
2025-07-15 19:01:38.314 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:975 | 🔧 [紧急修复] 开始分页处理: 第2页, 表名: salary_data_2025_07_active_employees
2025-07-15 19:01:38.315 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:439 | 正在从表 salary_data_2025_07_active_employees 分页获取数据: 第2页, 每页50条
2025-07-15 19:01:38.319 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:462 | 成功从表 salary_data_2025_07_active_employees 获取第2页数据: 50 行，总计1396行
2025-07-15 19:01:38.423 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:133 | 使用已存在的配置文件: state/data/field_mappings.json
2025-07-15 19:01:38.424 | INFO     | src.modules.data_import.config_sync_manager:__init__:70 | 配置同步管理器初始化完成
2025-07-15 19:01:38.428 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:1162 | 🔧 [修复] 没有找到匹配的字段映射
2025-07-15 19:01:38.430 | INFO     | src.gui.prototype.prototype_main_window:_apply_system_field_filtering:1194 | 🔧 [修复] 过滤了 4 个系统字段
2025-07-15 19:01:38.536 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:133 | 使用已存在的配置文件: state/data/field_mappings.json
2025-07-15 19:01:38.538 | INFO     | src.modules.data_import.config_sync_manager:__init__:70 | 配置同步管理器初始化完成
2025-07-15 19:01:38.540 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:1231 | 🔧 [修复] 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-15 19:01:38.542 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:238 | 开始格式化工资表数据 - 表类型: salary_data_2025_07_active_employees
2025-07-15 19:01:38.543 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:251 | 原始数据: 50行, 24列
2025-07-15 19:01:38.544 | INFO     | src.modules.data_storage.salary_data_formatter:_extract_table_type_from_name:335 | 🔧 [表名映射] 通过关键词 'active_employees' 匹配到表类型: active_employees
2025-07-15 19:01:38.546 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:255 | 🔧 [表名映射] 输入表名: salary_data_2025_07_active_employees, 提取的表类型: active_employees
2025-07-15 19:01:38.547 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:259 | 使用表类型配置: active_employees, 配置字段: ['id_fields', 'string_fields', 'integer_fields', 'float_fields', 'special_fields']
2025-07-15 19:01:38.547 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:263 | 🔧 [格式化调试] 开始处理ID字段: ['employee_id', 'employee_type_code', '工号', '人员类别代码']
2025-07-15 19:01:38.549 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:268 | 🔧 [格式化调试] 处理前 employee_id 字段样例: ['20171604.0', '20181638.0', '19930191.0']
2025-07-15 19:01:38.553 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:268 | 🔧 [格式化调试] 处理前 employee_type_code 字段样例: ['1.0', '1.0', '1.0']
2025-07-15 19:01:38.554 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:377 | 🔧 [强化调试] 处理ID字段 employee_id: 原始样例=['20171604.0', '20181638.0', '19930191.0'], 原始类型=object
2025-07-15 19:01:38.556 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:426 | 🔧 [强化调试] 清理后ID字段 employee_id: 样例=['20171604', '20181638', '19930191']
2025-07-15 19:01:38.557 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:443 | 🔧 [强化调试] 最终ID字段 employee_id: 样例=['20171604', '20181638', '19930191']
2025-07-15 19:01:38.557 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:454 | 🔧 [验证成功] ID字段 employee_id 格式化完成，无小数点问题
2025-07-15 19:01:38.558 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:377 | 🔧 [强化调试] 处理ID字段 employee_type_code: 原始样例=['1.0', '1.0', '1.0'], 原始类型=object
2025-07-15 19:01:38.559 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:426 | 🔧 [强化调试] 清理后ID字段 employee_type_code: 样例=['1', '1', '1']
2025-07-15 19:01:38.562 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:443 | 🔧 [强化调试] 最终ID字段 employee_type_code: 样例=['01', '01', '01']
2025-07-15 19:01:38.567 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:454 | 🔧 [验证成功] ID字段 employee_type_code 格式化完成，无小数点问题
2025-07-15 19:01:38.567 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_id_fields:461 | 🔧 [警告] ID字段 工号 不存在于数据框中
2025-07-15 19:01:38.568 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_id_fields:461 | 🔧 [警告] ID字段 人员类别代码 不存在于数据框中
2025-07-15 19:01:38.569 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:274 | 🔧 [格式化调试] 处理后 employee_id 字段样例: ['20171604', '20181638', '19930191']
2025-07-15 19:01:38.570 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:274 | 🔧 [格式化调试] 处理后 employee_type_code 字段样例: ['01', '01', '01']
2025-07-15 19:01:38.571 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:472 | 🔧 [强化调试] 开始格式化字符串字段: ['employee_id', 'employee_name', 'department', 'employee_type_code', 'employee_type', '工号', '姓名', '部门名称', '人员类别代码', '人员类别']
2025-07-15 19:01:38.572 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 employee_id: 原始样例=['20171604', '20181638', '19930191'], 原始类型=object
2025-07-15 19:01:38.573 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:504 | 🔧 [修复] 工号字段 employee_id 已特殊处理，去除小数点格式
2025-07-15 19:01:38.575 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 employee_id: 样例=['20171604', '20181638', '19930191'], 类型=object
2025-07-15 19:01:38.576 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 employee_id 格式化完成，无小数点问题
2025-07-15 19:01:38.582 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 employee_name: 原始样例=['周荔', '申子宇', '周浩'], 原始类型=object
2025-07-15 19:01:38.583 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 employee_name: 样例=['周荔', '申子宇', '周浩'], 类型=object
2025-07-15 19:01:38.584 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 employee_name 格式化完成，无小数点问题
2025-07-15 19:01:38.585 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 department: 原始样例=['资源环境科学与工程学院', '资源环境科学与工程学院', '资源环境科学与工程学院'], 原始类型=object
2025-07-15 19:01:38.588 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 department: 样例=['资源环境科学与工程学院', '资源环境科学与工程学院', '资源环境科学与工程学院'], 类型=object
2025-07-15 19:01:38.588 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 department 格式化完成，无小数点问题
2025-07-15 19:01:38.589 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 employee_type_code: 原始样例=['01', '01', '01'], 原始类型=object
2025-07-15 19:01:38.593 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 employee_type_code: 样例=['01', '01', '01'], 类型=object
2025-07-15 19:01:38.596 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 employee_type_code 格式化完成，无小数点问题
2025-07-15 19:01:38.597 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 employee_type: 原始样例=['教学单位专技人员', '教学单位专技人员', '教学院其它人员'], 原始类型=object
2025-07-15 19:01:38.598 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 employee_type: 样例=['教学单位专技人员', '教学单位专技人员', '教学院其它人员'], 类型=object
2025-07-15 19:01:38.600 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 employee_type 格式化完成，无小数点问题
2025-07-15 19:01:38.600 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 工号 不存在于数据框中
2025-07-15 19:01:38.601 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 姓名 不存在于数据框中
2025-07-15 19:01:38.602 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 部门名称 不存在于数据框中
2025-07-15 19:01:38.603 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 人员类别代码 不存在于数据框中
2025-07-15 19:01:38.604 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 人员类别 不存在于数据框中
2025-07-15 19:01:38.610 | INFO     | src.modules.data_storage.salary_data_formatter:_format_integer_fields:604 | 🔧 [强化调试] 开始格式化整型字段: []
2025-07-15 19:01:38.611 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:655 | 🔧 [强化调试] 开始格式化浮点型字段: ['position_salary_2025', 'grade_salary_2025', 'allowance', 'balance_allowance', 'basic_performance_2025', 'health_fee', 'transport_allowance', 'property_allowance', 'communication_allowance', 'performance_bonus_2025', 'provident_fund_2025', 'housing_allowance', 'car_allowance', 'supplement', 'advance', 'total_salary', 'pension_insurance', '2025年岗位工资', '2025年薪级工资', '津贴', '结余津贴', '2025年基础性绩效', '卫生费', '交通补贴', '物业补贴', '通讯补贴', '2025年奖励性绩效预发', '2025公积金', '住房补贴', '车补', '补发', '借支', '应发工资', '代扣代存养老保险']
2025-07-15 19:01:38.612 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 position_salary_2025: 原始样例=[1925.0, 3030.0, 3455.0], 原始类型=float64
2025-07-15 19:01:38.614 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 position_salary_2025: 样例=[1925.0, 3030.0, 3455.0], 类型=float64
2025-07-15 19:01:38.614 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 position_salary_2025 格式化完成，保留两位小数
2025-07-15 19:01:38.615 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 grade_salary_2025: 原始样例=[1427.0, 1515.0, 3391.0], 原始类型=float64
2025-07-15 19:01:38.616 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 grade_salary_2025: 样例=[1427.0, 1515.0, 3391.0], 类型=float64
2025-07-15 19:01:38.617 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 grade_salary_2025 格式化完成，保留两位小数
2025-07-15 19:01:38.618 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 allowance: 原始样例=[0.0, 0.0, 134.0], 原始类型=float64
2025-07-15 19:01:38.619 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 allowance: 样例=[0.0, 0.0, 134.0], 类型=float64
2025-07-15 19:01:38.625 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 allowance 格式化完成，保留两位小数
2025-07-15 19:01:38.626 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 balance_allowance: 原始样例=[56.0, 56.0, 56.0], 原始类型=float64
2025-07-15 19:01:38.626 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 balance_allowance: 样例=[56.0, 56.0, 56.0], 类型=float64
2025-07-15 19:01:38.629 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 balance_allowance 格式化完成，保留两位小数
2025-07-15 19:01:38.629 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 basic_performance_2025: 原始样例=[2824.0, 3466.0, 4108.0], 原始类型=float64
2025-07-15 19:01:38.630 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 basic_performance_2025: 样例=[2824.0, 3466.0, 4108.0], 类型=float64
2025-07-15 19:01:38.630 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 basic_performance_2025 格式化完成，保留两位小数
2025-07-15 19:01:38.631 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 health_fee: 原始样例=[0.0, nan, 0.0], 原始类型=float64
2025-07-15 19:01:38.633 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 health_fee: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-15 19:01:38.634 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 health_fee 格式化完成，保留两位小数
2025-07-15 19:01:38.639 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 transport_allowance: 原始样例=[220.0, 220.0, 220.0], 原始类型=float64
2025-07-15 19:01:38.640 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 transport_allowance: 样例=[220.0, 220.0, 220.0], 类型=float64
2025-07-15 19:01:38.642 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 transport_allowance 格式化完成，保留两位小数
2025-07-15 19:01:38.642 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 property_allowance: 原始样例=[200.0, 240.0, 240.0], 原始类型=float64
2025-07-15 19:01:38.644 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 property_allowance: 样例=[200.0, 240.0, 240.0], 类型=float64
2025-07-15 19:01:38.644 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 property_allowance 格式化完成，保留两位小数
2025-07-15 19:01:38.645 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 communication_allowance: 原始样例=[nan, nan, 50.0], 原始类型=float64
2025-07-15 19:01:38.646 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 communication_allowance: 样例=[0.0, 0.0, 50.0], 类型=float64
2025-07-15 19:01:38.647 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 communication_allowance 格式化完成，保留两位小数
2025-07-15 19:01:38.647 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 performance_bonus_2025: 原始样例=[1000.0, 0.0, 2400.0], 原始类型=float64
2025-07-15 19:01:38.655 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 performance_bonus_2025: 样例=[1000.0, 0.0, 2400.0], 类型=float64
2025-07-15 19:01:38.655 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 performance_bonus_2025 格式化完成，保留两位小数
2025-07-15 19:01:38.656 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 provident_fund_2025: 原始样例=[1428.0, 2216.0, 2274.0], 原始类型=float64
2025-07-15 19:01:38.657 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 provident_fund_2025: 样例=[1428.0, 2216.0, 2274.0], 类型=float64
2025-07-15 19:01:38.658 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 provident_fund_2025 格式化完成，保留两位小数
2025-07-15 19:01:38.659 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 housing_allowance: 原始样例=[188.76571428819443, 204.8512760416667, 315.672219058336], 原始类型=float64
2025-07-15 19:01:38.660 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 housing_allowance: 样例=[188.77, 204.85, 315.67], 类型=float64
2025-07-15 19:01:38.661 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 housing_allowance 格式化完成，保留两位小数
2025-07-15 19:01:38.662 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 car_allowance: 原始样例=[None, None, None], 原始类型=object
2025-07-15 19:01:38.664 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 car_allowance: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-15 19:01:38.669 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 car_allowance 格式化完成，保留两位小数
2025-07-15 19:01:38.670 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 supplement: 原始样例=[None, None, None], 原始类型=object
2025-07-15 19:01:38.671 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 supplement: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-15 19:01:38.671 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 supplement 格式化完成，保留两位小数
2025-07-15 19:01:38.672 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 advance: 原始样例=[nan, 2799.4, nan], 原始类型=float64
2025-07-15 19:01:38.673 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 advance: 样例=[0.0, 2799.4, 0.0], 类型=float64
2025-07-15 19:01:38.674 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 advance 格式化完成，保留两位小数
2025-07-15 19:01:38.674 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 total_salary: 原始样例=[7840.765714288194, 5932.451276041667, 14369.672219058337], 原始类型=float64
2025-07-15 19:01:38.677 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 total_salary: 样例=[7840.77, 5932.45, 14369.67], 类型=float64
2025-07-15 19:01:38.677 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 total_salary 格式化完成，保留两位小数
2025-07-15 19:01:38.683 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 pension_insurance: 原始样例=[977.64, 1416.96, 1867.56], 原始类型=float64
2025-07-15 19:01:38.684 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 pension_insurance: 样例=[977.64, 1416.96, 1867.56], 类型=float64
2025-07-15 19:01:38.684 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 pension_insurance 格式化完成，保留两位小数
2025-07-15 19:01:38.685 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 2025年岗位工资 不存在于数据框中
2025-07-15 19:01:38.686 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 2025年薪级工资 不存在于数据框中
2025-07-15 19:01:38.687 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 津贴 不存在于数据框中
2025-07-15 19:01:38.688 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 结余津贴 不存在于数据框中
2025-07-15 19:01:38.688 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 2025年基础性绩效 不存在于数据框中
2025-07-15 19:01:38.690 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 卫生费 不存在于数据框中
2025-07-15 19:01:38.691 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 交通补贴 不存在于数据框中
2025-07-15 19:01:38.696 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 物业补贴 不存在于数据框中
2025-07-15 19:01:38.697 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 通讯补贴 不存在于数据框中
2025-07-15 19:01:38.698 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 2025年奖励性绩效预发 不存在于数据框中
2025-07-15 19:01:38.699 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 2025公积金 不存在于数据框中
2025-07-15 19:01:38.700 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 住房补贴 不存在于数据框中
2025-07-15 19:01:38.701 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 车补 不存在于数据框中
2025-07-15 19:01:38.702 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 补发 不存在于数据框中
2025-07-15 19:01:38.702 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 借支 不存在于数据框中
2025-07-15 19:01:38.703 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 应发工资 不存在于数据框中
2025-07-15 19:01:38.704 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 代扣代存养老保险 不存在于数据框中
2025-07-15 19:01:38.705 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:711 | 🔧 [特殊字段] 开始处理表类型 active_employees 的月份、年份字段
2025-07-15 19:01:38.711 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:731 | 🔧 [特殊字段] 处理月份字段 month: 原始样例=['2025-07', '2025-07', '2025-07'], 原始类型=object
2025-07-15 19:01:38.713 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:774 | 🔧 [特殊字段] 处理后月份字段 month: 样例=['07', '07', '07']
2025-07-15 19:01:38.714 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:785 | 🔧 [验证成功] 月份字段 month 格式化完成，所有值为有效月份格式
2025-07-15 19:01:38.715 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:805 | 🔧 [特殊字段] 处理年份字段 year: 原始样例=[2025, 2025, 2025], 原始类型=int64
2025-07-15 19:01:38.716 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:845 | 🔧 [特殊字段] 处理后年份字段 year: 样例=['2025', '2025', '2025']
2025-07-15 19:01:38.716 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:856 | 🔧 [验证成功] 年份字段 year 格式化完成，所有值为有效年份格式
2025-07-15 19:01:38.717 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:866 | 🔧 [特殊字段] 月份、年份字段处理完成
2025-07-15 19:01:38.721 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:291 | 数据格式化完成: 50行, 24列
2025-07-15 19:01:38.727 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3558 | 最大可见行数已更新: 50 -> 50
2025-07-15 19:01:38.728 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:3611 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-15 19:01:38.731 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2235 | 🔧 [修复标识] 字段映射生效，样例: ['employee_id', 'employee_name', 'department'] -> ['工号', '姓名', '部门名称']
2025-07-15 19:01:38.732 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第0行数据工号: 20171604
2025-07-15 19:01:38.736 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第1行数据工号: 20181638
2025-07-15 19:01:38.737 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第2行数据工号: 19930191
2025-07-15 19:01:38.738 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第3行数据工号: 20181669
2025-07-15 19:01:38.739 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第4行数据工号: 20191722
2025-07-15 19:01:38.739 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:457 | 🔧 [排序修复] 可见行数据顺序: ['20171604', '20181638', '19930191', '20181669', '20191722']
2025-07-15 19:01:38.740 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:424 | 表格数据已设置: 50 行, 24 列
2025-07-15 19:01:38.742 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2361 | 🔧 [表格调试] 开始格式化ID字段: 列=工号, 原值=20171604, 类型=<class 'str'>
2025-07-15 19:01:38.743 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2368 | 🔧 [表格调试] ID字段格式化完成: 列=工号, 原值=20171604, 格式化后=20171604
2025-07-15 19:01:38.744 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2361 | 🔧 [表格调试] 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-15 19:01:38.744 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2368 | 🔧 [表格调试] ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-15 19:01:38.770 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2172 | 🔧 [性能分析] 表格数据设置完成: 50 行, 最大可见行数: 50, 总耗时: 42.96ms
2025-07-15 19:01:38.770 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 初始化和状态: 2.08ms (4.8%)
2025-07-15 19:01:38.773 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 字段映射: 3.04ms (7.1%)
2025-07-15 19:01:38.774 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 数据模型更新: 8.86ms (20.6%)
2025-07-15 19:01:38.774 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 表头设置: 0.00ms (0.0%)
2025-07-15 19:01:38.776 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 设置行数: 0.00ms (0.0%)
2025-07-15 19:01:38.776 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 填充可见数据: 28.99ms (67.5%)
2025-07-15 19:01:38.777 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 调整列宽: 0.00ms (0.0%)
2025-07-15 19:01:38.778 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 设置行号: 0.00ms (0.0%)
2025-07-15 19:01:38.779 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2180 | 🔧 [性能分析] 最耗时步骤: 填充可见数据 (28.99ms)
2025-07-15 19:01:38.780 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:1049 | 🔧 [紧急修复] 分页处理完成: 第2页, 50条记录
2025-07-15 19:01:38.786 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3464 | 🔧 [紧急修复] 新架构分页: 第2页
2025-07-15 19:01:38.787 | INFO     | src.services.table_data_service:load_table_data:291 | 加载表格数据: salary_data_2025_07_active_employees, 页码: 2
2025-07-15 19:01:38.827 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:4784 | 🔧 [修复标识] 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-15 19:01:38.830 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3558 | 最大可见行数已更新: 50 -> 50
2025-07-15 19:01:38.830 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:3611 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-15 19:01:38.837 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第0行数据工号: 20171604.0
2025-07-15 19:01:38.838 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第1行数据工号: 20181638.0
2025-07-15 19:01:38.839 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第2行数据工号: 19930191.0
2025-07-15 19:01:38.839 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第3行数据工号: 20181669.0
2025-07-15 19:01:38.840 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第4行数据工号: 20191722.0
2025-07-15 19:01:38.842 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:457 | 🔧 [排序修复] 可见行数据顺序: ['20171604.0', '20181638.0', '19930191.0', '20181669.0', '20191722.0']
2025-07-15 19:01:38.843 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:424 | 表格数据已设置: 50 行, 24 列
2025-07-15 19:01:38.844 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2361 | 🔧 [表格调试] 开始格式化ID字段: 列=工号, 原值=20171604.0, 类型=<class 'str'>
2025-07-15 19:01:38.845 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2368 | 🔧 [表格调试] ID字段格式化完成: 列=工号, 原值=20171604.0, 格式化后=20171604.0
2025-07-15 19:01:38.845 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2361 | 🔧 [表格调试] 开始格式化ID字段: 列=人员类别代码, 原值=1.0, 类型=<class 'str'>
2025-07-15 19:01:38.851 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2368 | 🔧 [表格调试] ID字段格式化完成: 列=人员类别代码, 原值=1.0, 格式化后=1.0
2025-07-15 19:01:38.873 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2172 | 🔧 [性能分析] 表格数据设置完成: 50 行, 最大可见行数: 50, 总耗时: 42.96ms
2025-07-15 19:01:38.874 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 初始化和状态: 1.05ms (2.4%)
2025-07-15 19:01:38.876 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 字段映射: 6.64ms (15.5%)
2025-07-15 19:01:38.877 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 数据模型更新: 5.21ms (12.1%)
2025-07-15 19:01:38.878 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 表头设置: 1.02ms (2.4%)
2025-07-15 19:01:38.878 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 设置行数: 0.00ms (0.0%)
2025-07-15 19:01:38.878 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 填充可见数据: 26.00ms (60.5%)
2025-07-15 19:01:38.879 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 调整列宽: 0.00ms (0.0%)
2025-07-15 19:01:38.880 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 设置行号: 3.05ms (7.1%)
2025-07-15 19:01:38.881 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2180 | 🔧 [性能分析] 最耗时步骤: 填充可见数据 (26.00ms)
2025-07-15 19:01:38.881 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-15 19:01:38.882 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3526 | 🔧 [紧急修复] 新架构分页成功: 第2页, 50行
2025-07-15 19:01:38.888 | INFO     | src.gui.widgets.pagination_widget:set_current_page:330 | 页码切换到: 2
2025-07-15 19:01:38.889 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3060 | 🔧 [分页修复] 数据更新事件设置当前页: 2
2025-07-15 19:01:38.890 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3066 | 🔧 [分页修复] 分页状态: 当前页=2, 总页数=28, 总记录数=1396
2025-07-15 19:01:38.891 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3072 | 🔧 [分页修复] 按钮可用性: 下一页=True, 上一页=True
2025-07-15 19:01:38.931 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:4784 | 🔧 [修复标识] 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-15 19:01:38.934 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3558 | 最大可见行数已更新: 50 -> 50
2025-07-15 19:01:38.935 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:3611 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-15 19:01:38.942 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第0行数据工号: 20171604.0
2025-07-15 19:01:38.943 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第1行数据工号: 20181638.0
2025-07-15 19:01:38.944 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第2行数据工号: 19930191.0
2025-07-15 19:01:38.944 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第3行数据工号: 20181669.0
2025-07-15 19:01:38.945 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第4行数据工号: 20191722.0
2025-07-15 19:01:38.946 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:457 | 🔧 [排序修复] 可见行数据顺序: ['20171604.0', '20181638.0', '19930191.0', '20181669.0', '20191722.0']
2025-07-15 19:01:38.947 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:424 | 表格数据已设置: 50 行, 24 列
2025-07-15 19:01:38.948 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2361 | 🔧 [表格调试] 开始格式化ID字段: 列=工号, 原值=20171604.0, 类型=<class 'str'>
2025-07-15 19:01:38.949 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2368 | 🔧 [表格调试] ID字段格式化完成: 列=工号, 原值=20171604.0, 格式化后=20171604.0
2025-07-15 19:01:38.956 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2361 | 🔧 [表格调试] 开始格式化ID字段: 列=人员类别代码, 原值=1.0, 类型=<class 'str'>
2025-07-15 19:01:38.956 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2368 | 🔧 [表格调试] ID字段格式化完成: 列=人员类别代码, 原值=1.0, 格式化后=1.0
2025-07-15 19:01:38.978 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2172 | 🔧 [性能分析] 表格数据设置完成: 50 行, 最大可见行数: 50, 总耗时: 43.91ms
2025-07-15 19:01:38.979 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 初始化和状态: 2.06ms (4.7%)
2025-07-15 19:01:38.982 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 字段映射: 5.64ms (12.9%)
2025-07-15 19:01:38.983 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 数据模型更新: 5.16ms (11.8%)
2025-07-15 19:01:38.983 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 表头设置: 0.00ms (0.0%)
2025-07-15 19:01:38.984 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 设置行数: 0.00ms (0.0%)
2025-07-15 19:01:38.985 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 填充可见数据: 31.05ms (70.7%)
2025-07-15 19:01:38.986 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 调整列宽: 0.00ms (0.0%)
2025-07-15 19:01:38.987 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 设置行号: 0.00ms (0.0%)
2025-07-15 19:01:38.988 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2180 | 🔧 [性能分析] 最耗时步骤: 填充可见数据 (31.05ms)
2025-07-15 19:01:38.988 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-15 19:01:38.990 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3526 | 🔧 [紧急修复] 新架构分页成功: 第2页, 50行
2025-07-15 19:01:38.996 | INFO     | src.gui.widgets.pagination_widget:set_current_page:330 | 页码切换到: 2
2025-07-15 19:01:50.395 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_changed:5399 | 排序变化: 1 列
2025-07-15 19:01:50.395 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_changed:5411 | 🔧 [调试] 数据重载状态正常，继续处理排序
2025-07-15 19:01:50.395 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_changed:5415 | 🔧 [调试] 开始更新排序指示器
2025-07-15 19:01:50.395 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_sort_indicators:5794 | 🔧 [排序指示器修复] 设置排序指示器: 列6, 顺序ascending
2025-07-15 19:01:50.395 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_changed:5417 | 🔧 [调试] 排序指示器更新完成
2025-07-15 19:01:50.395 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_changed:5423 | 🔧 [调试] 准备调用_save_and_apply_sort_state
2025-07-15 19:01:50.395 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5439 | 🔧 [调试] 开始保存排序状态: 1 列
2025-07-15 19:01:50.395 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5455 | 🔧 [调试] 已获取主窗口: PrototypeMainWindow
2025-07-15 19:01:50.395 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5461 | 🔧 [调试] 表名: salary_data_2025_07_active_employees, 表类型: 
2025-07-15 19:01:50.395 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5468 | 🔧 [调试] 使用简化排序逻辑，直接使用列名
2025-07-15 19:01:50.395 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5473 | 🔧 [调试] 处理排序列: SortColumn(column_index=6, order=<SortOrder.ASCENDING: 'ascending'>, priority=0, column_name='grade_salary_2025')
2025-07-15 19:01:50.395 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5513 | 🔧 [调试] 转换后的排序列: [{'column_name': 'grade_salary_2025', 'order': 'ascending', 'priority': 0, 'column_index': 6}]
2025-07-15 19:01:50.395 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5517 | 🔧 [调试] 排序状态管理器可用
2025-07-15 19:01:50.395 | INFO     | src.core.table_sort_state_manager:save_sort_state:229 | 已保存排序状态: salary_data_2025_07_active_employees (), 1 列
2025-07-15 19:01:50.395 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5527 | 🔧 [调试] 排序状态保存结果: True
2025-07-15 19:01:50.395 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5530 | 🆕 排序状态已保存: salary_data_2025_07_active_employees, 1 列
2025-07-15 19:01:50.395 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_trigger_data_reload:5622 | 🔧 [调试] 开始触发数据重新加载: salary_data_2025_07_active_employees
2025-07-15 19:01:50.411 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_trigger_data_reload:5633 | 🔧 [调试] 分页模式检查: True
2025-07-15 19:01:50.411 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_trigger_data_reload:5643 | 🔧 获取分页组件当前页码: 2
2025-07-15 19:01:50.411 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_trigger_data_reload:5652 | 🔧 排序时保持页码: 2
2025-07-15 19:01:50.411 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_trigger_data_reload:5655 | 🆕 触发分页数据重新加载（应用排序）: salary_data_2025_07_active_employees, 第2页
2025-07-15 19:01:50.411 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:5058 | 使用分页模式加载 salary_data_2025_07_active_employees，第2页，每页50条
2025-07-15 19:01:50.411 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:5067 | 缓存命中: salary_data_2025_07_active_employees 第2页
2025-07-15 19:01:50.411 | WARNING  | src.gui.prototype.prototype_main_window:_apply_unified_field_processing:4580 | 🔧 [缓存失效] 缓存应用失败，重新处理: "None of [Index(['工号', '姓名', '部门名称', '人员类别代码', '人员类别', '2025年岗位工资', '2025年薪级工资', '津贴',\n       '结余津贴', '2025年基础性绩效', '卫生费', '交通补贴', '物业补贴', '住房补贴', '车补', '通讯补贴',\n       '2025年奖励性绩效预发', '补发', '借支', '应发工资', '2025公积金', '代扣代存养老保险', '月份', '年份'],\n      dtype='object')] are in the [columns]"
2025-07-15 19:01:50.411 | INFO     | src.gui.prototype.prototype_main_window:_apply_unified_field_processing:4589 | 🔧 [修复标识] 开始统一字段处理: salary_data_2025_07_active_employees, 原始列数: 28
2025-07-15 19:01:50.426 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:4784 | 🔧 [修复标识] 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-15 19:01:50.426 | INFO     | src.gui.prototype.prototype_main_window:_apply_unified_field_processing:4634 | 🔧 [字段处理] 统一字段处理完成并缓存: 24个字段
2025-07-15 19:01:50.426 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:5131 | 分页数据加载成功（缓存）: 50条数据，第2页，总计1396条
2025-07-15 19:01:50.426 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:5164 | 🔧 [异步分页] 开始应用数据格式化处理
2025-07-15 19:01:50.426 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:238 | 开始格式化工资表数据 - 表类型: salary_data_2025_07_active_employees
2025-07-15 19:01:50.426 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:251 | 原始数据: 50行, 24列
2025-07-15 19:01:50.426 | INFO     | src.modules.data_storage.salary_data_formatter:_extract_table_type_from_name:335 | 🔧 [表名映射] 通过关键词 'active_employees' 匹配到表类型: active_employees
2025-07-15 19:01:50.426 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:255 | 🔧 [表名映射] 输入表名: salary_data_2025_07_active_employees, 提取的表类型: active_employees
2025-07-15 19:01:50.426 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:259 | 使用表类型配置: active_employees, 配置字段: ['id_fields', 'string_fields', 'integer_fields', 'float_fields', 'special_fields']
2025-07-15 19:01:50.426 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:263 | 🔧 [格式化调试] 开始处理ID字段: ['employee_id', 'employee_type_code', '工号', '人员类别代码']
2025-07-15 19:01:50.426 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:268 | 🔧 [格式化调试] 处理前 工号 字段样例: ['20171604.0', '20181638.0', '19930191.0']
2025-07-15 19:01:50.426 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:268 | 🔧 [格式化调试] 处理前 人员类别代码 字段样例: ['1.0', '1.0', '1.0']
2025-07-15 19:01:50.426 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_id_fields:461 | 🔧 [警告] ID字段 employee_id 不存在于数据框中
2025-07-15 19:01:50.426 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_id_fields:461 | 🔧 [警告] ID字段 employee_type_code 不存在于数据框中
2025-07-15 19:01:50.426 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:377 | 🔧 [强化调试] 处理ID字段 工号: 原始样例=['20171604.0', '20181638.0', '19930191.0'], 原始类型=object
2025-07-15 19:01:50.426 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:426 | 🔧 [强化调试] 清理后ID字段 工号: 样例=['20171604', '20181638', '19930191']
2025-07-15 19:01:50.426 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:443 | 🔧 [强化调试] 最终ID字段 工号: 样例=['20171604', '20181638', '19930191']
2025-07-15 19:01:50.426 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:454 | 🔧 [验证成功] ID字段 工号 格式化完成，无小数点问题
2025-07-15 19:01:50.426 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:377 | 🔧 [强化调试] 处理ID字段 人员类别代码: 原始样例=['1.0', '1.0', '1.0'], 原始类型=object
2025-07-15 19:01:50.426 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:426 | 🔧 [强化调试] 清理后ID字段 人员类别代码: 样例=['1', '1', '1']
2025-07-15 19:01:50.426 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:443 | 🔧 [强化调试] 最终ID字段 人员类别代码: 样例=['01', '01', '01']
2025-07-15 19:01:50.426 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:454 | 🔧 [验证成功] ID字段 人员类别代码 格式化完成，无小数点问题
2025-07-15 19:01:50.426 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:274 | 🔧 [格式化调试] 处理后 工号 字段样例: ['20171604', '20181638', '19930191']
2025-07-15 19:01:50.426 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:274 | 🔧 [格式化调试] 处理后 人员类别代码 字段样例: ['01', '01', '01']
2025-07-15 19:01:50.426 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:472 | 🔧 [强化调试] 开始格式化字符串字段: ['employee_id', 'employee_name', 'department', 'employee_type_code', 'employee_type', '工号', '姓名', '部门名称', '人员类别代码', '人员类别']
2025-07-15 19:01:50.426 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 employee_id 不存在于数据框中
2025-07-15 19:01:50.426 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 employee_name 不存在于数据框中
2025-07-15 19:01:50.426 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 department 不存在于数据框中
2025-07-15 19:01:50.426 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 employee_type_code 不存在于数据框中
2025-07-15 19:01:50.442 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 employee_type 不存在于数据框中
2025-07-15 19:01:50.442 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 工号: 原始样例=['20171604', '20181638', '19930191'], 原始类型=object
2025-07-15 19:01:50.442 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:504 | 🔧 [修复] 工号字段 工号 已特殊处理，去除小数点格式
2025-07-15 19:01:50.442 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 工号: 样例=['20171604', '20181638', '19930191'], 类型=object
2025-07-15 19:01:50.442 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 工号 格式化完成，无小数点问题
2025-07-15 19:01:50.442 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 姓名: 原始样例=['周荔', '申子宇', '周浩'], 原始类型=object
2025-07-15 19:01:50.442 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 姓名: 样例=['周荔', '申子宇', '周浩'], 类型=object
2025-07-15 19:01:50.442 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 姓名 格式化完成，无小数点问题
2025-07-15 19:01:50.458 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 部门名称: 原始样例=['资源环境科学与工程学院', '资源环境科学与工程学院', '资源环境科学与工程学院'], 原始类型=object
2025-07-15 19:01:50.458 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 部门名称: 样例=['资源环境科学与工程学院', '资源环境科学与工程学院', '资源环境科学与工程学院'], 类型=object
2025-07-15 19:01:50.458 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 部门名称 格式化完成，无小数点问题
2025-07-15 19:01:50.458 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 人员类别代码: 原始样例=['01', '01', '01'], 原始类型=object
2025-07-15 19:01:50.458 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 人员类别代码: 样例=['01', '01', '01'], 类型=object
2025-07-15 19:01:50.458 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 人员类别代码 格式化完成，无小数点问题
2025-07-15 19:01:50.458 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 人员类别: 原始样例=['教学单位专技人员', '教学单位专技人员', '教学院其它人员'], 原始类型=object
2025-07-15 19:01:50.458 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 人员类别: 样例=['教学单位专技人员', '教学单位专技人员', '教学院其它人员'], 类型=object
2025-07-15 19:01:50.458 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 人员类别 格式化完成，无小数点问题
2025-07-15 19:01:50.458 | INFO     | src.modules.data_storage.salary_data_formatter:_format_integer_fields:604 | 🔧 [强化调试] 开始格式化整型字段: []
2025-07-15 19:01:50.458 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:655 | 🔧 [强化调试] 开始格式化浮点型字段: ['position_salary_2025', 'grade_salary_2025', 'allowance', 'balance_allowance', 'basic_performance_2025', 'health_fee', 'transport_allowance', 'property_allowance', 'communication_allowance', 'performance_bonus_2025', 'provident_fund_2025', 'housing_allowance', 'car_allowance', 'supplement', 'advance', 'total_salary', 'pension_insurance', '2025年岗位工资', '2025年薪级工资', '津贴', '结余津贴', '2025年基础性绩效', '卫生费', '交通补贴', '物业补贴', '通讯补贴', '2025年奖励性绩效预发', '2025公积金', '住房补贴', '车补', '补发', '借支', '应发工资', '代扣代存养老保险']
2025-07-15 19:01:50.458 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 position_salary_2025 不存在于数据框中
2025-07-15 19:01:50.458 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 grade_salary_2025 不存在于数据框中
2025-07-15 19:01:50.458 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 allowance 不存在于数据框中
2025-07-15 19:01:50.458 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 balance_allowance 不存在于数据框中
2025-07-15 19:01:50.458 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 basic_performance_2025 不存在于数据框中
2025-07-15 19:01:50.458 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 health_fee 不存在于数据框中
2025-07-15 19:01:50.458 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 transport_allowance 不存在于数据框中
2025-07-15 19:01:50.458 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 property_allowance 不存在于数据框中
2025-07-15 19:01:50.458 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 communication_allowance 不存在于数据框中
2025-07-15 19:01:50.458 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 performance_bonus_2025 不存在于数据框中
2025-07-15 19:01:50.458 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 provident_fund_2025 不存在于数据框中
2025-07-15 19:01:50.458 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 housing_allowance 不存在于数据框中
2025-07-15 19:01:50.458 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 car_allowance 不存在于数据框中
2025-07-15 19:01:50.458 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 supplement 不存在于数据框中
2025-07-15 19:01:50.458 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 advance 不存在于数据框中
2025-07-15 19:01:50.458 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 total_salary 不存在于数据框中
2025-07-15 19:01:50.458 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 pension_insurance 不存在于数据框中
2025-07-15 19:01:50.458 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025年岗位工资: 原始样例=[1925.0, 3030.0, 3455.0], 原始类型=float64
2025-07-15 19:01:50.458 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025年岗位工资: 样例=[1925.0, 3030.0, 3455.0], 类型=float64
2025-07-15 19:01:50.458 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025年岗位工资 格式化完成，保留两位小数
2025-07-15 19:01:50.473 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025年薪级工资: 原始样例=[1427.0, 1515.0, 3391.0], 原始类型=float64
2025-07-15 19:01:50.473 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025年薪级工资: 样例=[1427.0, 1515.0, 3391.0], 类型=float64
2025-07-15 19:01:50.473 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025年薪级工资 格式化完成，保留两位小数
2025-07-15 19:01:50.473 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 津贴: 原始样例=[0.0, 0.0, 134.0], 原始类型=float64
2025-07-15 19:01:50.473 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 津贴: 样例=[0.0, 0.0, 134.0], 类型=float64
2025-07-15 19:01:50.473 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 津贴 格式化完成，保留两位小数
2025-07-15 19:01:50.473 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 结余津贴: 原始样例=[56.0, 56.0, 56.0], 原始类型=float64
2025-07-15 19:01:50.473 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 结余津贴: 样例=[56.0, 56.0, 56.0], 类型=float64
2025-07-15 19:01:50.473 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 结余津贴 格式化完成，保留两位小数
2025-07-15 19:01:50.489 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025年基础性绩效: 原始样例=[2824.0, 3466.0, 4108.0], 原始类型=float64
2025-07-15 19:01:50.489 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025年基础性绩效: 样例=[2824.0, 3466.0, 4108.0], 类型=float64
2025-07-15 19:01:50.489 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025年基础性绩效 格式化完成，保留两位小数
2025-07-15 19:01:50.489 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 卫生费: 原始样例=[0.0, nan, 0.0], 原始类型=float64
2025-07-15 19:01:50.489 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 卫生费: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-15 19:01:50.489 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 卫生费 格式化完成，保留两位小数
2025-07-15 19:01:50.489 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 交通补贴: 原始样例=[220.0, 220.0, 220.0], 原始类型=float64
2025-07-15 19:01:50.489 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 交通补贴: 样例=[220.0, 220.0, 220.0], 类型=float64
2025-07-15 19:01:50.489 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 交通补贴 格式化完成，保留两位小数
2025-07-15 19:01:50.489 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 物业补贴: 原始样例=[200.0, 240.0, 240.0], 原始类型=float64
2025-07-15 19:01:50.489 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 物业补贴: 样例=[200.0, 240.0, 240.0], 类型=float64
2025-07-15 19:01:50.489 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 物业补贴 格式化完成，保留两位小数
2025-07-15 19:01:50.489 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 通讯补贴: 原始样例=[nan, nan, 50.0], 原始类型=float64
2025-07-15 19:01:50.489 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 通讯补贴: 样例=[0.0, 0.0, 50.0], 类型=float64
2025-07-15 19:01:50.489 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 通讯补贴 格式化完成，保留两位小数
2025-07-15 19:01:50.489 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025年奖励性绩效预发: 原始样例=[1000.0, 0.0, 2400.0], 原始类型=float64
2025-07-15 19:01:50.489 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025年奖励性绩效预发: 样例=[1000.0, 0.0, 2400.0], 类型=float64
2025-07-15 19:01:50.489 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025年奖励性绩效预发 格式化完成，保留两位小数
2025-07-15 19:01:50.489 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025公积金: 原始样例=[1428.0, 2216.0, 2274.0], 原始类型=float64
2025-07-15 19:01:50.489 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025公积金: 样例=[1428.0, 2216.0, 2274.0], 类型=float64
2025-07-15 19:01:50.489 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025公积金 格式化完成，保留两位小数
2025-07-15 19:01:50.489 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 住房补贴: 原始样例=[188.76571428819443, 204.8512760416667, 315.672219058336], 原始类型=float64
2025-07-15 19:01:50.489 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 住房补贴: 样例=[188.77, 204.85, 315.67], 类型=float64
2025-07-15 19:01:50.489 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 住房补贴 格式化完成，保留两位小数
2025-07-15 19:01:50.504 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 车补: 原始样例=[None, None, None], 原始类型=object
2025-07-15 19:01:50.504 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 车补: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-15 19:01:50.504 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 车补 格式化完成，保留两位小数
2025-07-15 19:01:50.504 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 补发: 原始样例=[None, None, None], 原始类型=object
2025-07-15 19:01:50.504 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 补发: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-15 19:01:50.504 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 补发 格式化完成，保留两位小数
2025-07-15 19:01:50.504 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 借支: 原始样例=[nan, 2799.4, nan], 原始类型=float64
2025-07-15 19:01:50.504 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 借支: 样例=[0.0, 2799.4, 0.0], 类型=float64
2025-07-15 19:01:50.504 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 借支 格式化完成，保留两位小数
2025-07-15 19:01:50.504 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 应发工资: 原始样例=[7840.765714288194, 5932.451276041667, 14369.672219058337], 原始类型=float64
2025-07-15 19:01:50.520 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 应发工资: 样例=[7840.77, 5932.45, 14369.67], 类型=float64
2025-07-15 19:01:50.520 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 应发工资 格式化完成，保留两位小数
2025-07-15 19:01:50.520 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 代扣代存养老保险: 原始样例=[977.64, 1416.96, 1867.56], 原始类型=float64
2025-07-15 19:01:50.520 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 代扣代存养老保险: 样例=[977.64, 1416.96, 1867.56], 类型=float64
2025-07-15 19:01:50.520 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 代扣代存养老保险 格式化完成，保留两位小数
2025-07-15 19:01:50.520 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:711 | 🔧 [特殊字段] 开始处理表类型 active_employees 的月份、年份字段
2025-07-15 19:01:50.520 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:866 | 🔧 [特殊字段] 月份、年份字段处理完成
2025-07-15 19:01:50.520 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:291 | 数据格式化完成: 50行, 24列
2025-07-15 19:01:50.520 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:5166 | 🔧 [异步分页] 数据格式化成功: 50行, 24列
2025-07-15 19:01:50.520 | INFO     | src.gui.prototype.prototype_main_window:set_data:642 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-07-15 19:01:50.630 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:133 | 使用已存在的配置文件: state/data/field_mappings.json
2025-07-15 19:01:50.630 | INFO     | src.modules.data_import.config_sync_manager:__init__:70 | 配置同步管理器初始化完成
2025-07-15 19:01:50.630 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:1157 | 🔧 [修复] 应用字段映射: 24个字段
2025-07-15 19:01:50.739 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:133 | 使用已存在的配置文件: state/data/field_mappings.json
2025-07-15 19:01:50.739 | INFO     | src.modules.data_import.config_sync_manager:__init__:70 | 配置同步管理器初始化完成
2025-07-15 19:01:50.739 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:1231 | 🔧 [修复] 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-15 19:01:50.755 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3558 | 最大可见行数已更新: 50 -> 50
2025-07-15 19:01:50.755 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:3611 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-15 19:01:50.755 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2235 | 🔧 [修复标识] 字段映射生效，样例: ['employee_id', 'employee_name', 'department'] -> ['工号', '姓名', '部门名称']
2025-07-15 19:01:50.771 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第0行数据工号: 20171604
2025-07-15 19:01:50.771 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第1行数据工号: 20181638
2025-07-15 19:01:50.771 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第2行数据工号: 19930191
2025-07-15 19:01:50.771 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第3行数据工号: 20181669
2025-07-15 19:01:50.771 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第4行数据工号: 20191722
2025-07-15 19:01:50.771 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:457 | 🔧 [排序修复] 可见行数据顺序: ['20171604', '20181638', '19930191', '20181669', '20191722']
2025-07-15 19:01:50.771 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:424 | 表格数据已设置: 50 行, 24 列
2025-07-15 19:01:50.771 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2361 | 🔧 [表格调试] 开始格式化ID字段: 列=工号, 原值=20171604, 类型=<class 'str'>
2025-07-15 19:01:50.771 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2368 | 🔧 [表格调试] ID字段格式化完成: 列=工号, 原值=20171604, 格式化后=20171604
2025-07-15 19:01:50.771 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2361 | 🔧 [表格调试] 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-15 19:01:50.771 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2368 | 🔧 [表格调试] ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-15 19:01:50.802 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2172 | 🔧 [性能分析] 表格数据设置完成: 50 行, 最大可见行数: 50, 总耗时: 62.75ms
2025-07-15 19:01:50.802 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 初始化和状态: 15.71ms (25.0%)
2025-07-15 19:01:50.802 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 字段映射: 16.68ms (26.6%)
2025-07-15 19:01:50.802 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 数据模型更新: 0.00ms (0.0%)
2025-07-15 19:01:50.802 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 表头设置: 0.00ms (0.0%)
2025-07-15 19:01:50.802 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 设置行数: 0.00ms (0.0%)
2025-07-15 19:01:50.802 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 填充可见数据: 14.71ms (23.4%)
2025-07-15 19:01:50.802 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 调整列宽: 0.00ms (0.0%)
2025-07-15 19:01:50.802 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 设置行号: 15.65ms (24.9%)
2025-07-15 19:01:50.802 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2180 | 🔧 [性能分析] 最耗时步骤: 字段映射 (16.68ms)
2025-07-15 19:01:50.802 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 50
2025-07-15 19:01:50.802 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:975 | 🔧 [紧急修复] 开始分页处理: 第1页, 表名: salary_data_2025_07_active_employees
2025-07-15 19:01:50.802 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:439 | 正在从表 salary_data_2025_07_active_employees 分页获取数据: 第1页, 每页50条
2025-07-15 19:01:50.802 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:462 | 成功从表 salary_data_2025_07_active_employees 获取第1页数据: 50 行，总计1396行
2025-07-15 19:01:50.919 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:133 | 使用已存在的配置文件: state/data/field_mappings.json
2025-07-15 19:01:50.919 | INFO     | src.modules.data_import.config_sync_manager:__init__:70 | 配置同步管理器初始化完成
2025-07-15 19:01:50.919 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:1162 | 🔧 [修复] 没有找到匹配的字段映射
2025-07-15 19:01:50.919 | INFO     | src.gui.prototype.prototype_main_window:_apply_system_field_filtering:1194 | 🔧 [修复] 过滤了 4 个系统字段
2025-07-15 19:01:51.028 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:133 | 使用已存在的配置文件: state/data/field_mappings.json
2025-07-15 19:01:51.028 | INFO     | src.modules.data_import.config_sync_manager:__init__:70 | 配置同步管理器初始化完成
2025-07-15 19:01:51.028 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:1231 | 🔧 [修复] 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-15 19:01:51.028 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:238 | 开始格式化工资表数据 - 表类型: salary_data_2025_07_active_employees
2025-07-15 19:01:51.028 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:251 | 原始数据: 50行, 24列
2025-07-15 19:01:51.028 | INFO     | src.modules.data_storage.salary_data_formatter:_extract_table_type_from_name:335 | 🔧 [表名映射] 通过关键词 'active_employees' 匹配到表类型: active_employees
2025-07-15 19:01:51.028 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:255 | 🔧 [表名映射] 输入表名: salary_data_2025_07_active_employees, 提取的表类型: active_employees
2025-07-15 19:01:51.028 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:259 | 使用表类型配置: active_employees, 配置字段: ['id_fields', 'string_fields', 'integer_fields', 'float_fields', 'special_fields']
2025-07-15 19:01:51.028 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:263 | 🔧 [格式化调试] 开始处理ID字段: ['employee_id', 'employee_type_code', '工号', '人员类别代码']
2025-07-15 19:01:51.028 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:268 | 🔧 [格式化调试] 处理前 employee_id 字段样例: ['19990089.0', '20161565.0', '20191782.0']
2025-07-15 19:01:51.028 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:268 | 🔧 [格式化调试] 处理前 employee_type_code 字段样例: ['1.0', '1.0', '17.0']
2025-07-15 19:01:51.028 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:377 | 🔧 [强化调试] 处理ID字段 employee_id: 原始样例=['19990089.0', '20161565.0', '20191782.0'], 原始类型=object
2025-07-15 19:01:51.028 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:426 | 🔧 [强化调试] 清理后ID字段 employee_id: 样例=['19990089', '20161565', '20191782']
2025-07-15 19:01:51.028 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:443 | 🔧 [强化调试] 最终ID字段 employee_id: 样例=['19990089', '20161565', '20191782']
2025-07-15 19:01:51.028 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:454 | 🔧 [验证成功] ID字段 employee_id 格式化完成，无小数点问题
2025-07-15 19:01:51.028 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:377 | 🔧 [强化调试] 处理ID字段 employee_type_code: 原始样例=['1.0', '1.0', '17.0'], 原始类型=object
2025-07-15 19:01:51.028 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:426 | 🔧 [强化调试] 清理后ID字段 employee_type_code: 样例=['1', '1', '17']
2025-07-15 19:01:51.028 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:443 | 🔧 [强化调试] 最终ID字段 employee_type_code: 样例=['01', '01', '17']
2025-07-15 19:01:51.028 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:454 | 🔧 [验证成功] ID字段 employee_type_code 格式化完成，无小数点问题
2025-07-15 19:01:51.028 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_id_fields:461 | 🔧 [警告] ID字段 工号 不存在于数据框中
2025-07-15 19:01:51.028 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_id_fields:461 | 🔧 [警告] ID字段 人员类别代码 不存在于数据框中
2025-07-15 19:01:51.028 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:274 | 🔧 [格式化调试] 处理后 employee_id 字段样例: ['19990089', '20161565', '20191782']
2025-07-15 19:01:51.028 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:274 | 🔧 [格式化调试] 处理后 employee_type_code 字段样例: ['01', '01', '17']
2025-07-15 19:01:51.028 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:472 | 🔧 [强化调试] 开始格式化字符串字段: ['employee_id', 'employee_name', 'department', 'employee_type_code', 'employee_type', '工号', '姓名', '部门名称', '人员类别代码', '人员类别']
2025-07-15 19:01:51.028 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 employee_id: 原始样例=['19990089', '20161565', '20191782'], 原始类型=object
2025-07-15 19:01:51.044 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:504 | 🔧 [修复] 工号字段 employee_id 已特殊处理，去除小数点格式
2025-07-15 19:01:51.044 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 employee_id: 样例=['19990089', '20161565', '20191782'], 类型=object
2025-07-15 19:01:51.044 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 employee_id 格式化完成，无小数点问题
2025-07-15 19:01:51.044 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 employee_name: 原始样例=['杨胜', '胡四平', '肖啸'], 原始类型=object
2025-07-15 19:01:51.044 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 employee_name: 样例=['杨胜', '胡四平', '肖啸'], 类型=object
2025-07-15 19:01:51.044 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 employee_name 格式化完成，无小数点问题
2025-07-15 19:01:51.044 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 department: 原始样例=['自动化学院', '自动化学院', '自动化学院'], 原始类型=object
2025-07-15 19:01:51.044 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 department: 样例=['自动化学院', '自动化学院', '自动化学院'], 类型=object
2025-07-15 19:01:51.044 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 department 格式化完成，无小数点问题
2025-07-15 19:01:51.044 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 employee_type_code: 原始样例=['01', '01', '17'], 原始类型=object
2025-07-15 19:01:51.065 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 employee_type_code: 样例=['01', '01', '17'], 类型=object
2025-07-15 19:01:51.065 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 employee_type_code 格式化完成，无小数点问题
2025-07-15 19:01:51.065 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 employee_type: 原始样例=['教学院其它人员', '教学单位专技人员', '科研单位人员'], 原始类型=object
2025-07-15 19:01:51.065 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 employee_type: 样例=['教学院其它人员', '教学单位专技人员', '科研单位人员'], 类型=object
2025-07-15 19:01:51.065 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 employee_type 格式化完成，无小数点问题
2025-07-15 19:01:51.065 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 工号 不存在于数据框中
2025-07-15 19:01:51.065 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 姓名 不存在于数据框中
2025-07-15 19:01:51.065 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 部门名称 不存在于数据框中
2025-07-15 19:01:51.065 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 人员类别代码 不存在于数据框中
2025-07-15 19:01:51.065 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 人员类别 不存在于数据框中
2025-07-15 19:01:51.076 | INFO     | src.modules.data_storage.salary_data_formatter:_format_integer_fields:604 | 🔧 [强化调试] 开始格式化整型字段: []
2025-07-15 19:01:51.076 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:655 | 🔧 [强化调试] 开始格式化浮点型字段: ['position_salary_2025', 'grade_salary_2025', 'allowance', 'balance_allowance', 'basic_performance_2025', 'health_fee', 'transport_allowance', 'property_allowance', 'communication_allowance', 'performance_bonus_2025', 'provident_fund_2025', 'housing_allowance', 'car_allowance', 'supplement', 'advance', 'total_salary', 'pension_insurance', '2025年岗位工资', '2025年薪级工资', '津贴', '结余津贴', '2025年基础性绩效', '卫生费', '交通补贴', '物业补贴', '通讯补贴', '2025年奖励性绩效预发', '2025公积金', '住房补贴', '车补', '补发', '借支', '应发工资', '代扣代存养老保险']
2025-07-15 19:01:51.076 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 position_salary_2025: 原始样例=[2880.0, 3030.0, 2185.0], 原始类型=float64
2025-07-15 19:01:51.076 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 position_salary_2025: 样例=[2880.0, 3030.0, 2185.0], 类型=float64
2025-07-15 19:01:51.076 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 position_salary_2025 格式化完成，保留两位小数
2025-07-15 19:01:51.076 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 grade_salary_2025: 原始样例=[2375.0, 1696.0, 1427.0], 原始类型=float64
2025-07-15 19:01:51.076 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 grade_salary_2025: 样例=[2375.0, 1696.0, 1427.0], 类型=float64
2025-07-15 19:01:51.076 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 grade_salary_2025 格式化完成，保留两位小数
2025-07-15 19:01:51.076 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 allowance: 原始样例=[102.0, 0.0, 0.0], 原始类型=float64
2025-07-15 19:01:51.076 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 allowance: 样例=[102.0, 0.0, 0.0], 类型=float64
2025-07-15 19:01:51.076 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 allowance 格式化完成，保留两位小数
2025-07-15 19:01:51.076 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 balance_allowance: 原始样例=[56.0, 56.0, 56.0], 原始类型=float64
2025-07-15 19:01:51.076 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 balance_allowance: 样例=[56.0, 56.0, 56.0], 类型=float64
2025-07-15 19:01:51.076 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 balance_allowance 格式化完成，保留两位小数
2025-07-15 19:01:51.076 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 basic_performance_2025: 原始样例=[3594.0, 3466.0, 2978.0], 原始类型=float64
2025-07-15 19:01:51.076 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 basic_performance_2025: 样例=[3594.0, 3466.0, 2978.0], 类型=float64
2025-07-15 19:01:51.076 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 basic_performance_2025 格式化完成，保留两位小数
2025-07-15 19:01:51.076 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 health_fee: 原始样例=[0.0, 0.0, 0.0], 原始类型=float64
2025-07-15 19:01:51.076 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 health_fee: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-15 19:01:51.076 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 health_fee 格式化完成，保留两位小数
2025-07-15 19:01:51.076 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 transport_allowance: 原始样例=[220.0, 220.0, 220.0], 原始类型=float64
2025-07-15 19:01:51.076 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 transport_allowance: 样例=[220.0, 220.0, 220.0], 类型=float64
2025-07-15 19:01:51.076 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 transport_allowance 格式化完成，保留两位小数
2025-07-15 19:01:51.076 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 property_allowance: 原始样例=[240.0, 240.0, 200.0], 原始类型=float64
2025-07-15 19:01:51.091 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 property_allowance: 样例=[240.0, 240.0, 200.0], 类型=float64
2025-07-15 19:01:51.091 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 property_allowance 格式化完成，保留两位小数
2025-07-15 19:01:51.091 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 communication_allowance: 原始样例=[50.0, nan, nan], 原始类型=float64
2025-07-15 19:01:51.091 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 communication_allowance: 样例=[50.0, 0.0, 0.0], 类型=float64
2025-07-15 19:01:51.091 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 communication_allowance 格式化完成，保留两位小数
2025-07-15 19:01:51.091 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 performance_bonus_2025: 原始样例=[2500.0, 1000.0, 1000.0], 原始类型=float64
2025-07-15 19:01:51.091 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 performance_bonus_2025: 样例=[2500.0, 1000.0, 1000.0], 类型=float64
2025-07-15 19:01:51.091 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 performance_bonus_2025 格式化完成，保留两位小数
2025-07-15 19:01:51.091 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 provident_fund_2025: 原始样例=[2097.0, 1860.0, 1984.0], 原始类型=float64
2025-07-15 19:01:51.106 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 provident_fund_2025: 样例=[2097.0, 1860.0, 1984.0], 类型=float64
2025-07-15 19:01:51.106 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 provident_fund_2025 格式化完成，保留两位小数
2025-07-15 19:01:51.106 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 housing_allowance: 原始样例=[271.9745083294993, 189.0, 174.16354166666667], 原始类型=float64
2025-07-15 19:01:51.106 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 housing_allowance: 样例=[271.97, 189.0, 174.16], 类型=float64
2025-07-15 19:01:51.106 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 housing_allowance 格式化完成，保留两位小数
2025-07-15 19:01:51.106 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 car_allowance: 原始样例=[None, None, None], 原始类型=object
2025-07-15 19:01:51.106 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 car_allowance: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-15 19:01:51.106 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 car_allowance 格式化完成，保留两位小数
2025-07-15 19:01:51.106 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 supplement: 原始样例=[None, None, None], 原始类型=object
2025-07-15 19:01:51.106 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 supplement: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-15 19:01:51.106 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 supplement 格式化完成，保留两位小数
2025-07-15 19:01:51.106 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 advance: 原始样例=[nan, nan, 2000.0], 原始类型=float64
2025-07-15 19:01:51.106 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 advance: 样例=[0.0, 0.0, 2000.0], 类型=float64
2025-07-15 19:01:51.106 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 advance 格式化完成，保留两位小数
2025-07-15 19:01:51.106 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 total_salary: 原始样例=[12288.9745083295, 9897.0, 6240.163541666667], 原始类型=float64
2025-07-15 19:01:51.106 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 total_salary: 样例=[12288.97, 9897.0, 6240.16], 类型=float64
2025-07-15 19:01:51.106 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 total_salary 格式化完成，保留两位小数
2025-07-15 19:01:51.106 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 pension_insurance: 原始样例=[1525.8000000000002, 1140.53, 1113.75], 原始类型=float64
2025-07-15 19:01:51.106 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 pension_insurance: 样例=[1525.8, 1140.53, 1113.75], 类型=float64
2025-07-15 19:01:51.106 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 pension_insurance 格式化完成，保留两位小数
2025-07-15 19:01:51.106 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 2025年岗位工资 不存在于数据框中
2025-07-15 19:01:51.106 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 2025年薪级工资 不存在于数据框中
2025-07-15 19:01:51.106 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 津贴 不存在于数据框中
2025-07-15 19:01:51.106 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 结余津贴 不存在于数据框中
2025-07-15 19:01:51.106 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 2025年基础性绩效 不存在于数据框中
2025-07-15 19:01:51.106 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 卫生费 不存在于数据框中
2025-07-15 19:01:51.106 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 交通补贴 不存在于数据框中
2025-07-15 19:01:51.106 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 物业补贴 不存在于数据框中
2025-07-15 19:01:51.106 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 通讯补贴 不存在于数据框中
2025-07-15 19:01:51.106 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 2025年奖励性绩效预发 不存在于数据框中
2025-07-15 19:01:51.106 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 2025公积金 不存在于数据框中
2025-07-15 19:01:51.122 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 住房补贴 不存在于数据框中
2025-07-15 19:01:51.122 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 车补 不存在于数据框中
2025-07-15 19:01:51.122 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 补发 不存在于数据框中
2025-07-15 19:01:51.122 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 借支 不存在于数据框中
2025-07-15 19:01:51.122 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 应发工资 不存在于数据框中
2025-07-15 19:01:51.122 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 代扣代存养老保险 不存在于数据框中
2025-07-15 19:01:51.122 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:711 | 🔧 [特殊字段] 开始处理表类型 active_employees 的月份、年份字段
2025-07-15 19:01:51.122 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:731 | 🔧 [特殊字段] 处理月份字段 month: 原始样例=['2025-07', '2025-07', '2025-07'], 原始类型=object
2025-07-15 19:01:51.122 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:774 | 🔧 [特殊字段] 处理后月份字段 month: 样例=['07', '07', '07']
2025-07-15 19:01:51.122 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:785 | 🔧 [验证成功] 月份字段 month 格式化完成，所有值为有效月份格式
2025-07-15 19:01:51.122 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:805 | 🔧 [特殊字段] 处理年份字段 year: 原始样例=[2025, 2025, 2025], 原始类型=int64
2025-07-15 19:01:51.138 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:845 | 🔧 [特殊字段] 处理后年份字段 year: 样例=['2025', '2025', '2025']
2025-07-15 19:01:51.138 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:856 | 🔧 [验证成功] 年份字段 year 格式化完成，所有值为有效年份格式
2025-07-15 19:01:51.138 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:866 | 🔧 [特殊字段] 月份、年份字段处理完成
2025-07-15 19:01:51.138 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:291 | 数据格式化完成: 50行, 24列
2025-07-15 19:01:51.171 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3558 | 最大可见行数已更新: 50 -> 50
2025-07-15 19:01:51.171 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:3611 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-15 19:01:51.171 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2235 | 🔧 [修复标识] 字段映射生效，样例: ['employee_id', 'employee_name', 'department'] -> ['工号', '姓名', '部门名称']
2025-07-15 19:01:51.171 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第0行数据工号: 19990089
2025-07-15 19:01:51.171 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第1行数据工号: 20161565
2025-07-15 19:01:51.171 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第2行数据工号: 20191782
2025-07-15 19:01:51.171 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第3行数据工号: 20151515
2025-07-15 19:01:51.171 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第4行数据工号: 20181640
2025-07-15 19:01:51.171 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:457 | 🔧 [排序修复] 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-15 19:01:51.171 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:424 | 表格数据已设置: 50 行, 24 列
2025-07-15 19:01:51.171 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2361 | 🔧 [表格调试] 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-15 19:01:51.171 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2368 | 🔧 [表格调试] ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-15 19:01:51.171 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2361 | 🔧 [表格调试] 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-15 19:01:51.171 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2368 | 🔧 [表格调试] ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-15 19:01:51.185 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2172 | 🔧 [性能分析] 表格数据设置完成: 50 行, 最大可见行数: 50, 总耗时: 47.02ms
2025-07-15 19:01:51.185 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 初始化和状态: 32.82ms (69.8%)
2025-07-15 19:01:51.185 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 字段映射: 0.00ms (0.0%)
2025-07-15 19:01:51.200 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 数据模型更新: 0.00ms (0.0%)
2025-07-15 19:01:51.200 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 表头设置: 0.00ms (0.0%)
2025-07-15 19:01:51.200 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 设置行数: 0.00ms (0.0%)
2025-07-15 19:01:51.200 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 填充可见数据: 14.20ms (30.2%)
2025-07-15 19:01:51.218 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 调整列宽: 0.00ms (0.0%)
2025-07-15 19:01:51.218 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 设置行号: 0.00ms (0.0%)
2025-07-15 19:01:51.218 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2180 | 🔧 [性能分析] 最耗时步骤: 初始化和状态 (32.82ms)
2025-07-15 19:01:51.218 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-15 19:01:51.218 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:1049 | 🔧 [紧急修复] 分页处理完成: 第1页, 50条记录
2025-07-15 19:01:51.218 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3464 | 🔧 [紧急修复] 新架构分页: 第1页
2025-07-15 19:01:51.218 | INFO     | src.services.table_data_service:load_table_data:291 | 加载表格数据: salary_data_2025_07_active_employees, 页码: 1
2025-07-15 19:01:51.218 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:4784 | 🔧 [修复标识] 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-15 19:01:51.218 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3558 | 最大可见行数已更新: 50 -> 50
2025-07-15 19:01:51.218 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:3611 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-15 19:01:51.231 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第0行数据工号: 19990089.0
2025-07-15 19:01:51.231 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第1行数据工号: 20161565.0
2025-07-15 19:01:51.231 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第2行数据工号: 20191782.0
2025-07-15 19:01:51.231 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第3行数据工号: 20151515.0
2025-07-15 19:01:51.231 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第4行数据工号: 20181640.0
2025-07-15 19:01:51.231 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:457 | 🔧 [排序修复] 可见行数据顺序: ['19990089.0', '20161565.0', '20191782.0', '20151515.0', '20181640.0']
2025-07-15 19:01:51.231 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:424 | 表格数据已设置: 50 行, 24 列
2025-07-15 19:01:51.231 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2361 | 🔧 [表格调试] 开始格式化ID字段: 列=工号, 原值=19990089.0, 类型=<class 'str'>
2025-07-15 19:01:51.231 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2368 | 🔧 [表格调试] ID字段格式化完成: 列=工号, 原值=19990089.0, 格式化后=19990089.0
2025-07-15 19:01:51.231 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2361 | 🔧 [表格调试] 开始格式化ID字段: 列=人员类别代码, 原值=1.0, 类型=<class 'str'>
2025-07-15 19:01:51.231 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2368 | 🔧 [表格调试] ID字段格式化完成: 列=人员类别代码, 原值=1.0, 格式化后=1.0
2025-07-15 19:01:51.247 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2172 | 🔧 [性能分析] 表格数据设置完成: 50 行, 最大可见行数: 50, 总耗时: 29.38ms
2025-07-15 19:01:51.247 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 初始化和状态: 0.00ms (0.0%)
2025-07-15 19:01:51.247 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 字段映射: 13.72ms (46.7%)
2025-07-15 19:01:51.247 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 数据模型更新: 0.00ms (0.0%)
2025-07-15 19:01:51.247 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 表头设置: 0.00ms (0.0%)
2025-07-15 19:01:51.247 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 设置行数: 0.00ms (0.0%)
2025-07-15 19:01:51.247 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 填充可见数据: 15.66ms (53.3%)
2025-07-15 19:01:51.247 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 调整列宽: 0.00ms (0.0%)
2025-07-15 19:01:51.247 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 设置行号: 0.00ms (0.0%)
2025-07-15 19:01:51.247 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2180 | 🔧 [性能分析] 最耗时步骤: 填充可见数据 (15.66ms)
2025-07-15 19:01:51.247 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-15 19:01:51.247 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3526 | 🔧 [紧急修复] 新架构分页成功: 第1页, 50行
2025-07-15 19:01:51.247 | INFO     | src.gui.widgets.pagination_widget:set_current_page:330 | 页码切换到: 1
2025-07-15 19:01:51.247 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-15 19:01:51.247 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:975 | 🔧 [紧急修复] 开始分页处理: 第2页, 表名: salary_data_2025_07_active_employees
2025-07-15 19:01:51.247 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:439 | 正在从表 salary_data_2025_07_active_employees 分页获取数据: 第2页, 每页50条
2025-07-15 19:01:51.263 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:462 | 成功从表 salary_data_2025_07_active_employees 获取第2页数据: 50 行，总计1396行
2025-07-15 19:01:51.368 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:133 | 使用已存在的配置文件: state/data/field_mappings.json
2025-07-15 19:01:51.368 | INFO     | src.modules.data_import.config_sync_manager:__init__:70 | 配置同步管理器初始化完成
2025-07-15 19:01:51.368 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:1162 | 🔧 [修复] 没有找到匹配的字段映射
2025-07-15 19:01:51.368 | INFO     | src.gui.prototype.prototype_main_window:_apply_system_field_filtering:1194 | 🔧 [修复] 过滤了 4 个系统字段
2025-07-15 19:01:51.477 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:133 | 使用已存在的配置文件: state/data/field_mappings.json
2025-07-15 19:01:51.477 | INFO     | src.modules.data_import.config_sync_manager:__init__:70 | 配置同步管理器初始化完成
2025-07-15 19:01:51.477 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:1231 | 🔧 [修复] 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-15 19:01:51.477 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:238 | 开始格式化工资表数据 - 表类型: salary_data_2025_07_active_employees
2025-07-15 19:01:51.477 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:251 | 原始数据: 50行, 24列
2025-07-15 19:01:51.477 | INFO     | src.modules.data_storage.salary_data_formatter:_extract_table_type_from_name:335 | 🔧 [表名映射] 通过关键词 'active_employees' 匹配到表类型: active_employees
2025-07-15 19:01:51.477 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:255 | 🔧 [表名映射] 输入表名: salary_data_2025_07_active_employees, 提取的表类型: active_employees
2025-07-15 19:01:51.477 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:259 | 使用表类型配置: active_employees, 配置字段: ['id_fields', 'string_fields', 'integer_fields', 'float_fields', 'special_fields']
2025-07-15 19:01:51.477 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:263 | 🔧 [格式化调试] 开始处理ID字段: ['employee_id', 'employee_type_code', '工号', '人员类别代码']
2025-07-15 19:01:51.477 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:268 | 🔧 [格式化调试] 处理前 employee_id 字段样例: ['20171604.0', '20181638.0', '19930191.0']
2025-07-15 19:01:51.477 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:268 | 🔧 [格式化调试] 处理前 employee_type_code 字段样例: ['1.0', '1.0', '1.0']
2025-07-15 19:01:51.477 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:377 | 🔧 [强化调试] 处理ID字段 employee_id: 原始样例=['20171604.0', '20181638.0', '19930191.0'], 原始类型=object
2025-07-15 19:01:51.477 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:426 | 🔧 [强化调试] 清理后ID字段 employee_id: 样例=['20171604', '20181638', '19930191']
2025-07-15 19:01:51.477 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:443 | 🔧 [强化调试] 最终ID字段 employee_id: 样例=['20171604', '20181638', '19930191']
2025-07-15 19:01:51.477 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:454 | 🔧 [验证成功] ID字段 employee_id 格式化完成，无小数点问题
2025-07-15 19:01:51.477 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:377 | 🔧 [强化调试] 处理ID字段 employee_type_code: 原始样例=['1.0', '1.0', '1.0'], 原始类型=object
2025-07-15 19:01:51.477 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:426 | 🔧 [强化调试] 清理后ID字段 employee_type_code: 样例=['1', '1', '1']
2025-07-15 19:01:51.477 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:443 | 🔧 [强化调试] 最终ID字段 employee_type_code: 样例=['01', '01', '01']
2025-07-15 19:01:51.477 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:454 | 🔧 [验证成功] ID字段 employee_type_code 格式化完成，无小数点问题
2025-07-15 19:01:51.477 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_id_fields:461 | 🔧 [警告] ID字段 工号 不存在于数据框中
2025-07-15 19:01:51.477 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_id_fields:461 | 🔧 [警告] ID字段 人员类别代码 不存在于数据框中
2025-07-15 19:01:51.493 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:274 | 🔧 [格式化调试] 处理后 employee_id 字段样例: ['20171604', '20181638', '19930191']
2025-07-15 19:01:51.493 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:274 | 🔧 [格式化调试] 处理后 employee_type_code 字段样例: ['01', '01', '01']
2025-07-15 19:01:51.493 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:472 | 🔧 [强化调试] 开始格式化字符串字段: ['employee_id', 'employee_name', 'department', 'employee_type_code', 'employee_type', '工号', '姓名', '部门名称', '人员类别代码', '人员类别']
2025-07-15 19:01:51.493 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 employee_id: 原始样例=['20171604', '20181638', '19930191'], 原始类型=object
2025-07-15 19:01:51.493 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:504 | 🔧 [修复] 工号字段 employee_id 已特殊处理，去除小数点格式
2025-07-15 19:01:51.493 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 employee_id: 样例=['20171604', '20181638', '19930191'], 类型=object
2025-07-15 19:01:51.493 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 employee_id 格式化完成，无小数点问题
2025-07-15 19:01:51.493 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 employee_name: 原始样例=['周荔', '申子宇', '周浩'], 原始类型=object
2025-07-15 19:01:51.493 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 employee_name: 样例=['周荔', '申子宇', '周浩'], 类型=object
2025-07-15 19:01:51.508 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 employee_name 格式化完成，无小数点问题
2025-07-15 19:01:51.508 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 department: 原始样例=['资源环境科学与工程学院', '资源环境科学与工程学院', '资源环境科学与工程学院'], 原始类型=object
2025-07-15 19:01:51.508 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 department: 样例=['资源环境科学与工程学院', '资源环境科学与工程学院', '资源环境科学与工程学院'], 类型=object
2025-07-15 19:01:51.508 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 department 格式化完成，无小数点问题
2025-07-15 19:01:51.508 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 employee_type_code: 原始样例=['01', '01', '01'], 原始类型=object
2025-07-15 19:01:51.508 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 employee_type_code: 样例=['01', '01', '01'], 类型=object
2025-07-15 19:01:51.508 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 employee_type_code 格式化完成，无小数点问题
2025-07-15 19:01:51.508 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 employee_type: 原始样例=['教学单位专技人员', '教学单位专技人员', '教学院其它人员'], 原始类型=object
2025-07-15 19:01:51.508 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 employee_type: 样例=['教学单位专技人员', '教学单位专技人员', '教学院其它人员'], 类型=object
2025-07-15 19:01:51.508 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 employee_type 格式化完成，无小数点问题
2025-07-15 19:01:51.508 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 工号 不存在于数据框中
2025-07-15 19:01:51.508 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 姓名 不存在于数据框中
2025-07-15 19:01:51.508 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 部门名称 不存在于数据框中
2025-07-15 19:01:51.508 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 人员类别代码 不存在于数据框中
2025-07-15 19:01:51.508 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 人员类别 不存在于数据框中
2025-07-15 19:01:51.508 | INFO     | src.modules.data_storage.salary_data_formatter:_format_integer_fields:604 | 🔧 [强化调试] 开始格式化整型字段: []
2025-07-15 19:01:51.508 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:655 | 🔧 [强化调试] 开始格式化浮点型字段: ['position_salary_2025', 'grade_salary_2025', 'allowance', 'balance_allowance', 'basic_performance_2025', 'health_fee', 'transport_allowance', 'property_allowance', 'communication_allowance', 'performance_bonus_2025', 'provident_fund_2025', 'housing_allowance', 'car_allowance', 'supplement', 'advance', 'total_salary', 'pension_insurance', '2025年岗位工资', '2025年薪级工资', '津贴', '结余津贴', '2025年基础性绩效', '卫生费', '交通补贴', '物业补贴', '通讯补贴', '2025年奖励性绩效预发', '2025公积金', '住房补贴', '车补', '补发', '借支', '应发工资', '代扣代存养老保险']
2025-07-15 19:01:51.508 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 position_salary_2025: 原始样例=[1925.0, 3030.0, 3455.0], 原始类型=float64
2025-07-15 19:01:51.508 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 position_salary_2025: 样例=[1925.0, 3030.0, 3455.0], 类型=float64
2025-07-15 19:01:51.508 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 position_salary_2025 格式化完成，保留两位小数
2025-07-15 19:01:51.508 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 grade_salary_2025: 原始样例=[1427.0, 1515.0, 3391.0], 原始类型=float64
2025-07-15 19:01:51.508 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 grade_salary_2025: 样例=[1427.0, 1515.0, 3391.0], 类型=float64
2025-07-15 19:01:51.508 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 grade_salary_2025 格式化完成，保留两位小数
2025-07-15 19:01:51.508 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 allowance: 原始样例=[0.0, 0.0, 134.0], 原始类型=float64
2025-07-15 19:01:51.524 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 allowance: 样例=[0.0, 0.0, 134.0], 类型=float64
2025-07-15 19:01:51.524 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 allowance 格式化完成，保留两位小数
2025-07-15 19:01:51.524 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 balance_allowance: 原始样例=[56.0, 56.0, 56.0], 原始类型=float64
2025-07-15 19:01:51.524 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 balance_allowance: 样例=[56.0, 56.0, 56.0], 类型=float64
2025-07-15 19:01:51.524 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 balance_allowance 格式化完成，保留两位小数
2025-07-15 19:01:51.524 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 basic_performance_2025: 原始样例=[2824.0, 3466.0, 4108.0], 原始类型=float64
2025-07-15 19:01:51.524 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 basic_performance_2025: 样例=[2824.0, 3466.0, 4108.0], 类型=float64
2025-07-15 19:01:51.524 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 basic_performance_2025 格式化完成，保留两位小数
2025-07-15 19:01:51.524 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 health_fee: 原始样例=[0.0, nan, 0.0], 原始类型=float64
2025-07-15 19:01:51.524 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 health_fee: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-15 19:01:51.540 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 health_fee 格式化完成，保留两位小数
2025-07-15 19:01:51.540 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 transport_allowance: 原始样例=[220.0, 220.0, 220.0], 原始类型=float64
2025-07-15 19:01:51.540 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 transport_allowance: 样例=[220.0, 220.0, 220.0], 类型=float64
2025-07-15 19:01:51.540 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 transport_allowance 格式化完成，保留两位小数
2025-07-15 19:01:51.540 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 property_allowance: 原始样例=[200.0, 240.0, 240.0], 原始类型=float64
2025-07-15 19:01:51.540 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 property_allowance: 样例=[200.0, 240.0, 240.0], 类型=float64
2025-07-15 19:01:51.540 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 property_allowance 格式化完成，保留两位小数
2025-07-15 19:01:51.540 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 communication_allowance: 原始样例=[nan, nan, 50.0], 原始类型=float64
2025-07-15 19:01:51.540 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 communication_allowance: 样例=[0.0, 0.0, 50.0], 类型=float64
2025-07-15 19:01:51.540 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 communication_allowance 格式化完成，保留两位小数
2025-07-15 19:01:51.540 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 performance_bonus_2025: 原始样例=[1000.0, 0.0, 2400.0], 原始类型=float64
2025-07-15 19:01:51.540 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 performance_bonus_2025: 样例=[1000.0, 0.0, 2400.0], 类型=float64
2025-07-15 19:01:51.540 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 performance_bonus_2025 格式化完成，保留两位小数
2025-07-15 19:01:51.540 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 provident_fund_2025: 原始样例=[1428.0, 2216.0, 2274.0], 原始类型=float64
2025-07-15 19:01:51.540 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 provident_fund_2025: 样例=[1428.0, 2216.0, 2274.0], 类型=float64
2025-07-15 19:01:51.540 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 provident_fund_2025 格式化完成，保留两位小数
2025-07-15 19:01:51.540 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 housing_allowance: 原始样例=[188.76571428819443, 204.8512760416667, 315.672219058336], 原始类型=float64
2025-07-15 19:01:51.540 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 housing_allowance: 样例=[188.77, 204.85, 315.67], 类型=float64
2025-07-15 19:01:51.540 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 housing_allowance 格式化完成，保留两位小数
2025-07-15 19:01:51.540 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 car_allowance: 原始样例=[None, None, None], 原始类型=object
2025-07-15 19:01:51.540 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 car_allowance: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-15 19:01:51.540 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 car_allowance 格式化完成，保留两位小数
2025-07-15 19:01:51.540 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 supplement: 原始样例=[None, None, None], 原始类型=object
2025-07-15 19:01:51.540 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 supplement: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-15 19:01:51.540 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 supplement 格式化完成，保留两位小数
2025-07-15 19:01:51.540 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 advance: 原始样例=[nan, 2799.4, nan], 原始类型=float64
2025-07-15 19:01:51.555 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 advance: 样例=[0.0, 2799.4, 0.0], 类型=float64
2025-07-15 19:01:51.555 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 advance 格式化完成，保留两位小数
2025-07-15 19:01:51.555 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 total_salary: 原始样例=[7840.765714288194, 5932.451276041667, 14369.672219058337], 原始类型=float64
2025-07-15 19:01:51.555 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 total_salary: 样例=[7840.77, 5932.45, 14369.67], 类型=float64
2025-07-15 19:01:51.555 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 total_salary 格式化完成，保留两位小数
2025-07-15 19:01:51.555 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 pension_insurance: 原始样例=[977.64, 1416.96, 1867.56], 原始类型=float64
2025-07-15 19:01:51.555 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 pension_insurance: 样例=[977.64, 1416.96, 1867.56], 类型=float64
2025-07-15 19:01:51.555 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 pension_insurance 格式化完成，保留两位小数
2025-07-15 19:01:51.555 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 2025年岗位工资 不存在于数据框中
2025-07-15 19:01:51.555 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 2025年薪级工资 不存在于数据框中
2025-07-15 19:01:51.571 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 津贴 不存在于数据框中
2025-07-15 19:01:51.571 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 结余津贴 不存在于数据框中
2025-07-15 19:01:51.571 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 2025年基础性绩效 不存在于数据框中
2025-07-15 19:01:51.571 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 卫生费 不存在于数据框中
2025-07-15 19:01:51.571 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 交通补贴 不存在于数据框中
2025-07-15 19:01:51.571 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 物业补贴 不存在于数据框中
2025-07-15 19:01:51.571 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 通讯补贴 不存在于数据框中
2025-07-15 19:01:51.571 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 2025年奖励性绩效预发 不存在于数据框中
2025-07-15 19:01:51.571 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 2025公积金 不存在于数据框中
2025-07-15 19:01:51.571 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 住房补贴 不存在于数据框中
2025-07-15 19:01:51.571 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 车补 不存在于数据框中
2025-07-15 19:01:51.571 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 补发 不存在于数据框中
2025-07-15 19:01:51.571 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 借支 不存在于数据框中
2025-07-15 19:01:51.571 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 应发工资 不存在于数据框中
2025-07-15 19:01:51.571 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 代扣代存养老保险 不存在于数据框中
2025-07-15 19:01:51.571 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:711 | 🔧 [特殊字段] 开始处理表类型 active_employees 的月份、年份字段
2025-07-15 19:01:51.571 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:731 | 🔧 [特殊字段] 处理月份字段 month: 原始样例=['2025-07', '2025-07', '2025-07'], 原始类型=object
2025-07-15 19:01:51.571 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:774 | 🔧 [特殊字段] 处理后月份字段 month: 样例=['07', '07', '07']
2025-07-15 19:01:51.571 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:785 | 🔧 [验证成功] 月份字段 month 格式化完成，所有值为有效月份格式
2025-07-15 19:01:51.571 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:805 | 🔧 [特殊字段] 处理年份字段 year: 原始样例=[2025, 2025, 2025], 原始类型=int64
2025-07-15 19:01:51.571 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:845 | 🔧 [特殊字段] 处理后年份字段 year: 样例=['2025', '2025', '2025']
2025-07-15 19:01:51.571 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:856 | 🔧 [验证成功] 年份字段 year 格式化完成，所有值为有效年份格式
2025-07-15 19:01:51.571 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:866 | 🔧 [特殊字段] 月份、年份字段处理完成
2025-07-15 19:01:51.571 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:291 | 数据格式化完成: 50行, 24列
2025-07-15 19:01:51.571 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3558 | 最大可见行数已更新: 50 -> 50
2025-07-15 19:01:51.571 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:3611 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-15 19:01:51.587 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2235 | 🔧 [修复标识] 字段映射生效，样例: ['employee_id', 'employee_name', 'department'] -> ['工号', '姓名', '部门名称']
2025-07-15 19:01:51.587 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第0行数据工号: 20171604
2025-07-15 19:01:51.587 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第1行数据工号: 20181638
2025-07-15 19:01:51.587 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第2行数据工号: 19930191
2025-07-15 19:01:51.587 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第3行数据工号: 20181669
2025-07-15 19:01:51.587 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第4行数据工号: 20191722
2025-07-15 19:01:51.587 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:457 | 🔧 [排序修复] 可见行数据顺序: ['20171604', '20181638', '19930191', '20181669', '20191722']
2025-07-15 19:01:51.587 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:424 | 表格数据已设置: 50 行, 24 列
2025-07-15 19:01:51.587 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2361 | 🔧 [表格调试] 开始格式化ID字段: 列=工号, 原值=20171604, 类型=<class 'str'>
2025-07-15 19:01:51.587 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2368 | 🔧 [表格调试] ID字段格式化完成: 列=工号, 原值=20171604, 格式化后=20171604
2025-07-15 19:01:51.587 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2361 | 🔧 [表格调试] 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-15 19:01:51.587 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2368 | 🔧 [表格调试] ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-15 19:01:51.618 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2172 | 🔧 [性能分析] 表格数据设置完成: 50 行, 最大可见行数: 50, 总耗时: 47.01ms
2025-07-15 19:01:51.618 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 初始化和状态: 0.00ms (0.0%)
2025-07-15 19:01:51.618 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 字段映射: 15.70ms (33.4%)
2025-07-15 19:01:51.618 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 数据模型更新: 0.00ms (0.0%)
2025-07-15 19:01:51.618 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 表头设置: 0.00ms (0.0%)
2025-07-15 19:01:51.618 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 设置行数: 0.00ms (0.0%)
2025-07-15 19:01:51.618 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 填充可见数据: 31.32ms (66.6%)
2025-07-15 19:01:51.618 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 调整列宽: 0.00ms (0.0%)
2025-07-15 19:01:51.618 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 设置行号: 0.00ms (0.0%)
2025-07-15 19:01:51.618 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2180 | 🔧 [性能分析] 最耗时步骤: 填充可见数据 (31.32ms)
2025-07-15 19:01:51.618 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:1049 | 🔧 [紧急修复] 分页处理完成: 第2页, 50条记录
2025-07-15 19:01:51.618 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3464 | 🔧 [紧急修复] 新架构分页: 第2页
2025-07-15 19:01:51.618 | INFO     | src.services.table_data_service:load_table_data:291 | 加载表格数据: salary_data_2025_07_active_employees, 页码: 2
2025-07-15 19:01:51.635 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:4784 | 🔧 [修复标识] 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-15 19:01:51.635 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3558 | 最大可见行数已更新: 50 -> 50
2025-07-15 19:01:51.635 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:3611 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-15 19:01:51.635 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第0行数据工号: 20171604.0
2025-07-15 19:01:51.635 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第1行数据工号: 20181638.0
2025-07-15 19:01:51.635 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第2行数据工号: 19930191.0
2025-07-15 19:01:51.635 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第3行数据工号: 20181669.0
2025-07-15 19:01:51.635 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第4行数据工号: 20191722.0
2025-07-15 19:01:51.635 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:457 | 🔧 [排序修复] 可见行数据顺序: ['20171604.0', '20181638.0', '19930191.0', '20181669.0', '20191722.0']
2025-07-15 19:01:51.649 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:424 | 表格数据已设置: 50 行, 24 列
2025-07-15 19:01:51.649 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2361 | 🔧 [表格调试] 开始格式化ID字段: 列=工号, 原值=20171604.0, 类型=<class 'str'>
2025-07-15 19:01:51.649 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2368 | 🔧 [表格调试] ID字段格式化完成: 列=工号, 原值=20171604.0, 格式化后=20171604.0
2025-07-15 19:01:51.649 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2361 | 🔧 [表格调试] 开始格式化ID字段: 列=人员类别代码, 原值=1.0, 类型=<class 'str'>
2025-07-15 19:01:51.649 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2368 | 🔧 [表格调试] ID字段格式化完成: 列=人员类别代码, 原值=1.0, 格式化后=1.0
2025-07-15 19:01:51.665 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2172 | 🔧 [性能分析] 表格数据设置完成: 50 行, 最大可见行数: 50, 总耗时: 29.41ms
2025-07-15 19:01:51.665 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 初始化和状态: 0.00ms (0.0%)
2025-07-15 19:01:51.665 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 字段映射: 0.00ms (0.0%)
2025-07-15 19:01:51.665 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 数据模型更新: 13.72ms (46.7%)
2025-07-15 19:01:51.665 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 表头设置: 0.00ms (0.0%)
2025-07-15 19:01:51.665 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 设置行数: 0.00ms (0.0%)
2025-07-15 19:01:51.665 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 填充可见数据: 15.69ms (53.3%)
2025-07-15 19:01:51.665 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 调整列宽: 0.00ms (0.0%)
2025-07-15 19:01:51.665 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 设置行号: 0.00ms (0.0%)
2025-07-15 19:01:51.665 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2180 | 🔧 [性能分析] 最耗时步骤: 填充可见数据 (15.69ms)
2025-07-15 19:01:51.665 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-15 19:01:51.665 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3526 | 🔧 [紧急修复] 新架构分页成功: 第2页, 50行
2025-07-15 19:01:51.665 | INFO     | src.gui.widgets.pagination_widget:set_current_page:330 | 页码切换到: 2
2025-07-15 19:01:51.665 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:439 | 正在从表 salary_data_2025_07_active_employees 分页获取数据: 第3页, 每页50条
2025-07-15 19:01:51.665 | INFO     | src.gui.multi_column_sort_manager:add_sort_column:274 | 添加排序列: 6(grade_salary_2025) -> ascending, 当前排序列数: 1
2025-07-15 19:01:51.665 | INFO     | src.gui.multi_column_sort_manager:_on_sort_indicator_changed:211 | 排序指示器变化: 列6(grade_salary_2025) -> ascending
2025-07-15 19:01:51.665 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_header_sort_changed:5887 | 🔧 [表格排序] 表头排序变化: 列6, 顺序ASC
2025-07-15 19:01:51.665 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_changed:5399 | 排序变化: 1 列
2025-07-15 19:01:51.665 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_changed:5411 | 🔧 [调试] 数据重载状态正常，继续处理排序
2025-07-15 19:01:51.665 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_changed:5415 | 🔧 [调试] 开始更新排序指示器
2025-07-15 19:01:51.665 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_sort_indicators:5794 | 🔧 [排序指示器修复] 设置排序指示器: 列6, 顺序ascending
2025-07-15 19:01:51.683 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:462 | 成功从表 salary_data_2025_07_active_employees 获取第3页数据: 50 行，总计1396行
2025-07-15 19:01:51.683 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_changed:5417 | 🔧 [调试] 排序指示器更新完成
2025-07-15 19:01:51.696 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_changed:5423 | 🔧 [调试] 准备调用_save_and_apply_sort_state
2025-07-15 19:01:51.696 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5439 | 🔧 [调试] 开始保存排序状态: 1 列
2025-07-15 19:01:51.696 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5455 | 🔧 [调试] 已获取主窗口: PrototypeMainWindow
2025-07-15 19:01:51.696 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5461 | 🔧 [调试] 表名: salary_data_2025_07_active_employees, 表类型: 
2025-07-15 19:01:51.696 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5468 | 🔧 [调试] 使用简化排序逻辑，直接使用列名
2025-07-15 19:01:51.696 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5473 | 🔧 [调试] 处理排序列: SortColumn(column_index=6, order=<SortOrder.ASCENDING: 'ascending'>, priority=0, column_name='grade_salary_2025')
2025-07-15 19:01:51.696 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5513 | 🔧 [调试] 转换后的排序列: [{'column_name': 'grade_salary_2025', 'order': 'ascending', 'priority': 0, 'column_index': 6}]
2025-07-15 19:01:51.696 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5517 | 🔧 [调试] 排序状态管理器可用
2025-07-15 19:01:51.696 | INFO     | src.core.table_sort_state_manager:save_sort_state:229 | 已保存排序状态: salary_data_2025_07_active_employees (), 1 列
2025-07-15 19:01:51.696 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5527 | 🔧 [调试] 排序状态保存结果: True
2025-07-15 19:01:51.696 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_and_apply_sort_state:5530 | 🆕 排序状态已保存: salary_data_2025_07_active_employees, 1 列
2025-07-15 19:01:51.696 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_trigger_data_reload:5622 | 🔧 [调试] 开始触发数据重新加载: salary_data_2025_07_active_employees
2025-07-15 19:01:51.696 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_trigger_data_reload:5633 | 🔧 [调试] 分页模式检查: True
2025-07-15 19:01:51.696 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_trigger_data_reload:5643 | 🔧 获取分页组件当前页码: 2
2025-07-15 19:01:51.696 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_trigger_data_reload:5652 | 🔧 排序时保持页码: 2
2025-07-15 19:01:51.696 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_trigger_data_reload:5655 | 🆕 触发分页数据重新加载（应用排序）: salary_data_2025_07_active_employees, 第2页
2025-07-15 19:01:51.696 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:5058 | 使用分页模式加载 salary_data_2025_07_active_employees，第2页，每页50条
2025-07-15 19:01:51.696 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:5067 | 缓存命中: salary_data_2025_07_active_employees 第2页
2025-07-15 19:01:51.696 | WARNING  | src.gui.prototype.prototype_main_window:_apply_unified_field_processing:4580 | 🔧 [缓存失效] 缓存应用失败，重新处理: "None of [Index(['工号', '姓名', '部门名称', '人员类别代码', '人员类别', '2025年岗位工资', '2025年薪级工资', '津贴',\n       '结余津贴', '2025年基础性绩效', '卫生费', '交通补贴', '物业补贴', '住房补贴', '车补', '通讯补贴',\n       '2025年奖励性绩效预发', '补发', '借支', '应发工资', '2025公积金', '代扣代存养老保险', '月份', '年份'],\n      dtype='object')] are in the [columns]"
2025-07-15 19:01:51.696 | INFO     | src.gui.prototype.prototype_main_window:_apply_unified_field_processing:4589 | 🔧 [修复标识] 开始统一字段处理: salary_data_2025_07_active_employees, 原始列数: 28
2025-07-15 19:01:51.712 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:4784 | 🔧 [修复标识] 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-15 19:01:51.712 | INFO     | src.gui.prototype.prototype_main_window:_apply_unified_field_processing:4634 | 🔧 [字段处理] 统一字段处理完成并缓存: 24个字段
2025-07-15 19:01:51.712 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:5131 | 分页数据加载成功（缓存）: 50条数据，第2页，总计1396条
2025-07-15 19:01:51.712 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:5164 | 🔧 [异步分页] 开始应用数据格式化处理
2025-07-15 19:01:51.712 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:238 | 开始格式化工资表数据 - 表类型: salary_data_2025_07_active_employees
2025-07-15 19:01:51.712 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:251 | 原始数据: 50行, 24列
2025-07-15 19:01:51.712 | INFO     | src.modules.data_storage.salary_data_formatter:_extract_table_type_from_name:335 | 🔧 [表名映射] 通过关键词 'active_employees' 匹配到表类型: active_employees
2025-07-15 19:01:51.712 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:255 | 🔧 [表名映射] 输入表名: salary_data_2025_07_active_employees, 提取的表类型: active_employees
2025-07-15 19:01:51.712 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:259 | 使用表类型配置: active_employees, 配置字段: ['id_fields', 'string_fields', 'integer_fields', 'float_fields', 'special_fields']
2025-07-15 19:01:51.712 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:263 | 🔧 [格式化调试] 开始处理ID字段: ['employee_id', 'employee_type_code', '工号', '人员类别代码']
2025-07-15 19:01:51.712 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:268 | 🔧 [格式化调试] 处理前 工号 字段样例: ['20171604.0', '20181638.0', '19930191.0']
2025-07-15 19:01:51.712 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:268 | 🔧 [格式化调试] 处理前 人员类别代码 字段样例: ['1.0', '1.0', '1.0']
2025-07-15 19:01:51.712 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_id_fields:461 | 🔧 [警告] ID字段 employee_id 不存在于数据框中
2025-07-15 19:01:51.712 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_id_fields:461 | 🔧 [警告] ID字段 employee_type_code 不存在于数据框中
2025-07-15 19:01:51.712 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:377 | 🔧 [强化调试] 处理ID字段 工号: 原始样例=['20171604.0', '20181638.0', '19930191.0'], 原始类型=object
2025-07-15 19:01:51.712 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:426 | 🔧 [强化调试] 清理后ID字段 工号: 样例=['20171604', '20181638', '19930191']
2025-07-15 19:01:51.712 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:443 | 🔧 [强化调试] 最终ID字段 工号: 样例=['20171604', '20181638', '19930191']
2025-07-15 19:01:51.728 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:454 | 🔧 [验证成功] ID字段 工号 格式化完成，无小数点问题
2025-07-15 19:01:51.728 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:377 | 🔧 [强化调试] 处理ID字段 人员类别代码: 原始样例=['1.0', '1.0', '1.0'], 原始类型=object
2025-07-15 19:01:51.728 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:426 | 🔧 [强化调试] 清理后ID字段 人员类别代码: 样例=['1', '1', '1']
2025-07-15 19:01:51.728 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:443 | 🔧 [强化调试] 最终ID字段 人员类别代码: 样例=['01', '01', '01']
2025-07-15 19:01:51.728 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:454 | 🔧 [验证成功] ID字段 人员类别代码 格式化完成，无小数点问题
2025-07-15 19:01:51.728 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:274 | 🔧 [格式化调试] 处理后 工号 字段样例: ['20171604', '20181638', '19930191']
2025-07-15 19:01:51.728 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:274 | 🔧 [格式化调试] 处理后 人员类别代码 字段样例: ['01', '01', '01']
2025-07-15 19:01:51.728 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:472 | 🔧 [强化调试] 开始格式化字符串字段: ['employee_id', 'employee_name', 'department', 'employee_type_code', 'employee_type', '工号', '姓名', '部门名称', '人员类别代码', '人员类别']
2025-07-15 19:01:51.728 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 employee_id 不存在于数据框中
2025-07-15 19:01:51.728 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 employee_name 不存在于数据框中
2025-07-15 19:01:51.728 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 department 不存在于数据框中
2025-07-15 19:01:51.728 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 employee_type_code 不存在于数据框中
2025-07-15 19:01:51.728 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 employee_type 不存在于数据框中
2025-07-15 19:01:51.743 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 工号: 原始样例=['20171604', '20181638', '19930191'], 原始类型=object
2025-07-15 19:01:51.743 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:504 | 🔧 [修复] 工号字段 工号 已特殊处理，去除小数点格式
2025-07-15 19:01:51.743 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 工号: 样例=['20171604', '20181638', '19930191'], 类型=object
2025-07-15 19:01:51.743 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 工号 格式化完成，无小数点问题
2025-07-15 19:01:51.743 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 姓名: 原始样例=['周荔', '申子宇', '周浩'], 原始类型=object
2025-07-15 19:01:51.743 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 姓名: 样例=['周荔', '申子宇', '周浩'], 类型=object
2025-07-15 19:01:51.743 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 姓名 格式化完成，无小数点问题
2025-07-15 19:01:51.743 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 部门名称: 原始样例=['资源环境科学与工程学院', '资源环境科学与工程学院', '资源环境科学与工程学院'], 原始类型=object
2025-07-15 19:01:51.743 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 部门名称: 样例=['资源环境科学与工程学院', '资源环境科学与工程学院', '资源环境科学与工程学院'], 类型=object
2025-07-15 19:01:51.743 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 部门名称 格式化完成，无小数点问题
2025-07-15 19:01:51.743 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 人员类别代码: 原始样例=['01', '01', '01'], 原始类型=object
2025-07-15 19:01:51.743 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 人员类别代码: 样例=['01', '01', '01'], 类型=object
2025-07-15 19:01:51.743 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 人员类别代码 格式化完成，无小数点问题
2025-07-15 19:01:51.743 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 人员类别: 原始样例=['教学单位专技人员', '教学单位专技人员', '教学院其它人员'], 原始类型=object
2025-07-15 19:01:51.743 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 人员类别: 样例=['教学单位专技人员', '教学单位专技人员', '教学院其它人员'], 类型=object
2025-07-15 19:01:51.743 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 人员类别 格式化完成，无小数点问题
2025-07-15 19:01:51.743 | INFO     | src.modules.data_storage.salary_data_formatter:_format_integer_fields:604 | 🔧 [强化调试] 开始格式化整型字段: []
2025-07-15 19:01:51.743 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:655 | 🔧 [强化调试] 开始格式化浮点型字段: ['position_salary_2025', 'grade_salary_2025', 'allowance', 'balance_allowance', 'basic_performance_2025', 'health_fee', 'transport_allowance', 'property_allowance', 'communication_allowance', 'performance_bonus_2025', 'provident_fund_2025', 'housing_allowance', 'car_allowance', 'supplement', 'advance', 'total_salary', 'pension_insurance', '2025年岗位工资', '2025年薪级工资', '津贴', '结余津贴', '2025年基础性绩效', '卫生费', '交通补贴', '物业补贴', '通讯补贴', '2025年奖励性绩效预发', '2025公积金', '住房补贴', '车补', '补发', '借支', '应发工资', '代扣代存养老保险']
2025-07-15 19:01:51.743 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 position_salary_2025 不存在于数据框中
2025-07-15 19:01:51.743 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 grade_salary_2025 不存在于数据框中
2025-07-15 19:01:51.743 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 allowance 不存在于数据框中
2025-07-15 19:01:51.743 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 balance_allowance 不存在于数据框中
2025-07-15 19:01:51.743 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 basic_performance_2025 不存在于数据框中
2025-07-15 19:01:51.759 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 health_fee 不存在于数据框中
2025-07-15 19:01:51.759 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 transport_allowance 不存在于数据框中
2025-07-15 19:01:51.759 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 property_allowance 不存在于数据框中
2025-07-15 19:01:51.759 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 communication_allowance 不存在于数据框中
2025-07-15 19:01:51.759 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 performance_bonus_2025 不存在于数据框中
2025-07-15 19:01:51.759 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 provident_fund_2025 不存在于数据框中
2025-07-15 19:01:51.759 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 housing_allowance 不存在于数据框中
2025-07-15 19:01:51.759 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 car_allowance 不存在于数据框中
2025-07-15 19:01:51.759 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 supplement 不存在于数据框中
2025-07-15 19:01:51.759 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 advance 不存在于数据框中
2025-07-15 19:01:51.759 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 total_salary 不存在于数据框中
2025-07-15 19:01:51.759 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 pension_insurance 不存在于数据框中
2025-07-15 19:01:51.759 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025年岗位工资: 原始样例=[1925.0, 3030.0, 3455.0], 原始类型=float64
2025-07-15 19:01:51.759 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025年岗位工资: 样例=[1925.0, 3030.0, 3455.0], 类型=float64
2025-07-15 19:01:51.759 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025年岗位工资 格式化完成，保留两位小数
2025-07-15 19:01:51.774 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025年薪级工资: 原始样例=[1427.0, 1515.0, 3391.0], 原始类型=float64
2025-07-15 19:01:51.774 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025年薪级工资: 样例=[1427.0, 1515.0, 3391.0], 类型=float64
2025-07-15 19:01:51.774 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025年薪级工资 格式化完成，保留两位小数
2025-07-15 19:01:51.774 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 津贴: 原始样例=[0.0, 0.0, 134.0], 原始类型=float64
2025-07-15 19:01:51.774 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 津贴: 样例=[0.0, 0.0, 134.0], 类型=float64
2025-07-15 19:01:51.774 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 津贴 格式化完成，保留两位小数
2025-07-15 19:01:51.774 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 结余津贴: 原始样例=[56.0, 56.0, 56.0], 原始类型=float64
2025-07-15 19:01:51.774 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 结余津贴: 样例=[56.0, 56.0, 56.0], 类型=float64
2025-07-15 19:01:51.774 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 结余津贴 格式化完成，保留两位小数
2025-07-15 19:01:51.774 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025年基础性绩效: 原始样例=[2824.0, 3466.0, 4108.0], 原始类型=float64
2025-07-15 19:01:51.774 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025年基础性绩效: 样例=[2824.0, 3466.0, 4108.0], 类型=float64
2025-07-15 19:01:51.774 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025年基础性绩效 格式化完成，保留两位小数
2025-07-15 19:01:51.774 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 卫生费: 原始样例=[0.0, nan, 0.0], 原始类型=float64
2025-07-15 19:01:51.774 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 卫生费: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-15 19:01:51.774 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 卫生费 格式化完成，保留两位小数
2025-07-15 19:01:51.774 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 交通补贴: 原始样例=[220.0, 220.0, 220.0], 原始类型=float64
2025-07-15 19:01:51.774 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 交通补贴: 样例=[220.0, 220.0, 220.0], 类型=float64
2025-07-15 19:01:51.774 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 交通补贴 格式化完成，保留两位小数
2025-07-15 19:01:51.774 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 物业补贴: 原始样例=[200.0, 240.0, 240.0], 原始类型=float64
2025-07-15 19:01:51.774 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 物业补贴: 样例=[200.0, 240.0, 240.0], 类型=float64
2025-07-15 19:01:51.774 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 物业补贴 格式化完成，保留两位小数
2025-07-15 19:01:51.774 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 通讯补贴: 原始样例=[nan, nan, 50.0], 原始类型=float64
2025-07-15 19:01:51.774 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 通讯补贴: 样例=[0.0, 0.0, 50.0], 类型=float64
2025-07-15 19:01:51.774 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 通讯补贴 格式化完成，保留两位小数
2025-07-15 19:01:51.774 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025年奖励性绩效预发: 原始样例=[1000.0, 0.0, 2400.0], 原始类型=float64
2025-07-15 19:01:51.790 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025年奖励性绩效预发: 样例=[1000.0, 0.0, 2400.0], 类型=float64
2025-07-15 19:01:51.790 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025年奖励性绩效预发 格式化完成，保留两位小数
2025-07-15 19:01:51.790 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025公积金: 原始样例=[1428.0, 2216.0, 2274.0], 原始类型=float64
2025-07-15 19:01:51.790 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025公积金: 样例=[1428.0, 2216.0, 2274.0], 类型=float64
2025-07-15 19:01:51.790 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025公积金 格式化完成，保留两位小数
2025-07-15 19:01:51.790 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 住房补贴: 原始样例=[188.76571428819443, 204.8512760416667, 315.672219058336], 原始类型=float64
2025-07-15 19:01:51.790 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 住房补贴: 样例=[188.77, 204.85, 315.67], 类型=float64
2025-07-15 19:01:51.790 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 住房补贴 格式化完成，保留两位小数
2025-07-15 19:01:51.790 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 车补: 原始样例=[None, None, None], 原始类型=object
2025-07-15 19:01:51.790 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 车补: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-15 19:01:51.790 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 车补 格式化完成，保留两位小数
2025-07-15 19:01:51.806 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 补发: 原始样例=[None, None, None], 原始类型=object
2025-07-15 19:01:51.806 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 补发: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-15 19:01:51.806 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 补发 格式化完成，保留两位小数
2025-07-15 19:01:51.806 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 借支: 原始样例=[nan, 2799.4, nan], 原始类型=float64
2025-07-15 19:01:51.806 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 借支: 样例=[0.0, 2799.4, 0.0], 类型=float64
2025-07-15 19:01:51.806 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 借支 格式化完成，保留两位小数
2025-07-15 19:01:51.806 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 应发工资: 原始样例=[7840.765714288194, 5932.451276041667, 14369.672219058337], 原始类型=float64
2025-07-15 19:01:51.806 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 应发工资: 样例=[7840.77, 5932.45, 14369.67], 类型=float64
2025-07-15 19:01:51.806 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 应发工资 格式化完成，保留两位小数
2025-07-15 19:01:51.806 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 代扣代存养老保险: 原始样例=[977.64, 1416.96, 1867.56], 原始类型=float64
2025-07-15 19:01:51.806 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 代扣代存养老保险: 样例=[977.64, 1416.96, 1867.56], 类型=float64
2025-07-15 19:01:51.806 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 代扣代存养老保险 格式化完成，保留两位小数
2025-07-15 19:01:51.806 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:711 | 🔧 [特殊字段] 开始处理表类型 active_employees 的月份、年份字段
2025-07-15 19:01:51.806 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:866 | 🔧 [特殊字段] 月份、年份字段处理完成
2025-07-15 19:01:51.806 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:291 | 数据格式化完成: 50行, 24列
2025-07-15 19:01:51.806 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:5166 | 🔧 [异步分页] 数据格式化成功: 50行, 24列
2025-07-15 19:01:51.806 | INFO     | src.gui.prototype.prototype_main_window:set_data:642 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-07-15 19:01:51.920 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:133 | 使用已存在的配置文件: state/data/field_mappings.json
2025-07-15 19:01:51.920 | INFO     | src.modules.data_import.config_sync_manager:__init__:70 | 配置同步管理器初始化完成
2025-07-15 19:01:51.920 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:1157 | 🔧 [修复] 应用字段映射: 24个字段
2025-07-15 19:01:52.029 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:133 | 使用已存在的配置文件: state/data/field_mappings.json
2025-07-15 19:01:52.029 | INFO     | src.modules.data_import.config_sync_manager:__init__:70 | 配置同步管理器初始化完成
2025-07-15 19:01:52.029 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:1231 | 🔧 [修复] 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-15 19:01:52.044 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3558 | 最大可见行数已更新: 50 -> 50
2025-07-15 19:01:52.044 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:3611 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-15 19:01:52.062 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2235 | 🔧 [修复标识] 字段映射生效，样例: ['employee_id', 'employee_name', 'department'] -> ['工号', '姓名', '部门名称']
2025-07-15 19:01:52.062 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第0行数据工号: 20171604
2025-07-15 19:01:52.062 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第1行数据工号: 20181638
2025-07-15 19:01:52.062 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第2行数据工号: 19930191
2025-07-15 19:01:52.062 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第3行数据工号: 20181669
2025-07-15 19:01:52.062 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第4行数据工号: 20191722
2025-07-15 19:01:52.062 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:457 | 🔧 [排序修复] 可见行数据顺序: ['20171604', '20181638', '19930191', '20181669', '20191722']
2025-07-15 19:01:52.062 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:424 | 表格数据已设置: 50 行, 24 列
2025-07-15 19:01:52.062 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2361 | 🔧 [表格调试] 开始格式化ID字段: 列=工号, 原值=20171604, 类型=<class 'str'>
2025-07-15 19:01:52.062 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2368 | 🔧 [表格调试] ID字段格式化完成: 列=工号, 原值=20171604, 格式化后=20171604
2025-07-15 19:01:52.062 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2361 | 🔧 [表格调试] 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-15 19:01:52.062 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2368 | 🔧 [表格调试] ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-15 19:01:52.154 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2172 | 🔧 [性能分析] 表格数据设置完成: 50 行, 最大可见行数: 50, 总耗时: 125.30ms
2025-07-15 19:01:52.154 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 初始化和状态: 15.71ms (12.5%)
2025-07-15 19:01:52.154 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 字段映射: 17.63ms (14.1%)
2025-07-15 19:01:52.154 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 数据模型更新: 0.00ms (0.0%)
2025-07-15 19:01:52.154 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 表头设置: 0.00ms (0.0%)
2025-07-15 19:01:52.154 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 设置行数: 0.00ms (0.0%)
2025-07-15 19:01:52.154 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 填充可见数据: 91.97ms (73.4%)
2025-07-15 19:01:52.154 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 调整列宽: 0.00ms (0.0%)
2025-07-15 19:01:52.154 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 设置行号: 0.00ms (0.0%)
2025-07-15 19:01:52.154 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2180 | 🔧 [性能分析] 最耗时步骤: 填充可见数据 (91.97ms)
2025-07-15 19:01:52.154 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 50
2025-07-15 19:01:52.154 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:975 | 🔧 [紧急修复] 开始分页处理: 第1页, 表名: salary_data_2025_07_active_employees
2025-07-15 19:01:52.154 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:439 | 正在从表 salary_data_2025_07_active_employees 分页获取数据: 第1页, 每页50条
2025-07-15 19:01:52.170 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:462 | 成功从表 salary_data_2025_07_active_employees 获取第1页数据: 50 行，总计1396行
2025-07-15 19:01:52.274 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:133 | 使用已存在的配置文件: state/data/field_mappings.json
2025-07-15 19:01:52.274 | INFO     | src.modules.data_import.config_sync_manager:__init__:70 | 配置同步管理器初始化完成
2025-07-15 19:01:52.274 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:1162 | 🔧 [修复] 没有找到匹配的字段映射
2025-07-15 19:01:52.274 | INFO     | src.gui.prototype.prototype_main_window:_apply_system_field_filtering:1194 | 🔧 [修复] 过滤了 4 个系统字段
2025-07-15 19:01:52.383 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:133 | 使用已存在的配置文件: state/data/field_mappings.json
2025-07-15 19:01:52.383 | INFO     | src.modules.data_import.config_sync_manager:__init__:70 | 配置同步管理器初始化完成
2025-07-15 19:01:52.383 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:1231 | 🔧 [修复] 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-15 19:01:52.383 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:238 | 开始格式化工资表数据 - 表类型: salary_data_2025_07_active_employees
2025-07-15 19:01:52.383 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:251 | 原始数据: 50行, 24列
2025-07-15 19:01:52.383 | INFO     | src.modules.data_storage.salary_data_formatter:_extract_table_type_from_name:335 | 🔧 [表名映射] 通过关键词 'active_employees' 匹配到表类型: active_employees
2025-07-15 19:01:52.383 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:255 | 🔧 [表名映射] 输入表名: salary_data_2025_07_active_employees, 提取的表类型: active_employees
2025-07-15 19:01:52.383 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:259 | 使用表类型配置: active_employees, 配置字段: ['id_fields', 'string_fields', 'integer_fields', 'float_fields', 'special_fields']
2025-07-15 19:01:52.383 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:263 | 🔧 [格式化调试] 开始处理ID字段: ['employee_id', 'employee_type_code', '工号', '人员类别代码']
2025-07-15 19:01:52.383 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:268 | 🔧 [格式化调试] 处理前 employee_id 字段样例: ['19990089.0', '20161565.0', '20191782.0']
2025-07-15 19:01:52.383 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:268 | 🔧 [格式化调试] 处理前 employee_type_code 字段样例: ['1.0', '1.0', '17.0']
2025-07-15 19:01:52.383 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:377 | 🔧 [强化调试] 处理ID字段 employee_id: 原始样例=['19990089.0', '20161565.0', '20191782.0'], 原始类型=object
2025-07-15 19:01:52.383 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:426 | 🔧 [强化调试] 清理后ID字段 employee_id: 样例=['19990089', '20161565', '20191782']
2025-07-15 19:01:52.383 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:443 | 🔧 [强化调试] 最终ID字段 employee_id: 样例=['19990089', '20161565', '20191782']
2025-07-15 19:01:52.383 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:454 | 🔧 [验证成功] ID字段 employee_id 格式化完成，无小数点问题
2025-07-15 19:01:52.383 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:377 | 🔧 [强化调试] 处理ID字段 employee_type_code: 原始样例=['1.0', '1.0', '17.0'], 原始类型=object
2025-07-15 19:01:52.383 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:426 | 🔧 [强化调试] 清理后ID字段 employee_type_code: 样例=['1', '1', '17']
2025-07-15 19:01:52.383 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:443 | 🔧 [强化调试] 最终ID字段 employee_type_code: 样例=['01', '01', '17']
2025-07-15 19:01:52.383 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:454 | 🔧 [验证成功] ID字段 employee_type_code 格式化完成，无小数点问题
2025-07-15 19:01:52.383 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_id_fields:461 | 🔧 [警告] ID字段 工号 不存在于数据框中
2025-07-15 19:01:52.383 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_id_fields:461 | 🔧 [警告] ID字段 人员类别代码 不存在于数据框中
2025-07-15 19:01:52.383 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:274 | 🔧 [格式化调试] 处理后 employee_id 字段样例: ['19990089', '20161565', '20191782']
2025-07-15 19:01:52.383 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:274 | 🔧 [格式化调试] 处理后 employee_type_code 字段样例: ['01', '01', '17']
2025-07-15 19:01:52.399 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:472 | 🔧 [强化调试] 开始格式化字符串字段: ['employee_id', 'employee_name', 'department', 'employee_type_code', 'employee_type', '工号', '姓名', '部门名称', '人员类别代码', '人员类别']
2025-07-15 19:01:52.399 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 employee_id: 原始样例=['19990089', '20161565', '20191782'], 原始类型=object
2025-07-15 19:01:52.399 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:504 | 🔧 [修复] 工号字段 employee_id 已特殊处理，去除小数点格式
2025-07-15 19:01:52.399 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 employee_id: 样例=['19990089', '20161565', '20191782'], 类型=object
2025-07-15 19:01:52.399 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 employee_id 格式化完成，无小数点问题
2025-07-15 19:01:52.399 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 employee_name: 原始样例=['杨胜', '胡四平', '肖啸'], 原始类型=object
2025-07-15 19:01:52.399 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 employee_name: 样例=['杨胜', '胡四平', '肖啸'], 类型=object
2025-07-15 19:01:52.399 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 employee_name 格式化完成，无小数点问题
2025-07-15 19:01:52.399 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 department: 原始样例=['自动化学院', '自动化学院', '自动化学院'], 原始类型=object
2025-07-15 19:01:52.414 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 department: 样例=['自动化学院', '自动化学院', '自动化学院'], 类型=object
2025-07-15 19:01:52.414 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 department 格式化完成，无小数点问题
2025-07-15 19:01:52.414 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 employee_type_code: 原始样例=['01', '01', '17'], 原始类型=object
2025-07-15 19:01:52.414 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 employee_type_code: 样例=['01', '01', '17'], 类型=object
2025-07-15 19:01:52.414 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 employee_type_code 格式化完成，无小数点问题
2025-07-15 19:01:52.414 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 employee_type: 原始样例=['教学院其它人员', '教学单位专技人员', '科研单位人员'], 原始类型=object
2025-07-15 19:01:52.414 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 employee_type: 样例=['教学院其它人员', '教学单位专技人员', '科研单位人员'], 类型=object
2025-07-15 19:01:52.414 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 employee_type 格式化完成，无小数点问题
2025-07-15 19:01:52.414 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 工号 不存在于数据框中
2025-07-15 19:01:52.414 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 姓名 不存在于数据框中
2025-07-15 19:01:52.414 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 部门名称 不存在于数据框中
2025-07-15 19:01:52.414 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 人员类别代码 不存在于数据框中
2025-07-15 19:01:52.414 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 人员类别 不存在于数据框中
2025-07-15 19:01:52.414 | INFO     | src.modules.data_storage.salary_data_formatter:_format_integer_fields:604 | 🔧 [强化调试] 开始格式化整型字段: []
2025-07-15 19:01:52.414 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:655 | 🔧 [强化调试] 开始格式化浮点型字段: ['position_salary_2025', 'grade_salary_2025', 'allowance', 'balance_allowance', 'basic_performance_2025', 'health_fee', 'transport_allowance', 'property_allowance', 'communication_allowance', 'performance_bonus_2025', 'provident_fund_2025', 'housing_allowance', 'car_allowance', 'supplement', 'advance', 'total_salary', 'pension_insurance', '2025年岗位工资', '2025年薪级工资', '津贴', '结余津贴', '2025年基础性绩效', '卫生费', '交通补贴', '物业补贴', '通讯补贴', '2025年奖励性绩效预发', '2025公积金', '住房补贴', '车补', '补发', '借支', '应发工资', '代扣代存养老保险']
2025-07-15 19:01:52.414 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 position_salary_2025: 原始样例=[2880.0, 3030.0, 2185.0], 原始类型=float64
2025-07-15 19:01:52.414 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 position_salary_2025: 样例=[2880.0, 3030.0, 2185.0], 类型=float64
2025-07-15 19:01:52.414 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 position_salary_2025 格式化完成，保留两位小数
2025-07-15 19:01:52.414 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 grade_salary_2025: 原始样例=[2375.0, 1696.0, 1427.0], 原始类型=float64
2025-07-15 19:01:52.414 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 grade_salary_2025: 样例=[2375.0, 1696.0, 1427.0], 类型=float64
2025-07-15 19:01:52.414 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 grade_salary_2025 格式化完成，保留两位小数
2025-07-15 19:01:52.414 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 allowance: 原始样例=[102.0, 0.0, 0.0], 原始类型=float64
2025-07-15 19:01:52.414 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 allowance: 样例=[102.0, 0.0, 0.0], 类型=float64
2025-07-15 19:01:52.414 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 allowance 格式化完成，保留两位小数
2025-07-15 19:01:52.430 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 balance_allowance: 原始样例=[56.0, 56.0, 56.0], 原始类型=float64
2025-07-15 19:01:52.430 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 balance_allowance: 样例=[56.0, 56.0, 56.0], 类型=float64
2025-07-15 19:01:52.430 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 balance_allowance 格式化完成，保留两位小数
2025-07-15 19:01:52.430 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 basic_performance_2025: 原始样例=[3594.0, 3466.0, 2978.0], 原始类型=float64
2025-07-15 19:01:52.430 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 basic_performance_2025: 样例=[3594.0, 3466.0, 2978.0], 类型=float64
2025-07-15 19:01:52.430 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 basic_performance_2025 格式化完成，保留两位小数
2025-07-15 19:01:52.430 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 health_fee: 原始样例=[0.0, 0.0, 0.0], 原始类型=float64
2025-07-15 19:01:52.430 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 health_fee: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-15 19:01:52.430 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 health_fee 格式化完成，保留两位小数
2025-07-15 19:01:52.446 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 transport_allowance: 原始样例=[220.0, 220.0, 220.0], 原始类型=float64
2025-07-15 19:01:52.446 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 transport_allowance: 样例=[220.0, 220.0, 220.0], 类型=float64
2025-07-15 19:01:52.446 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 transport_allowance 格式化完成，保留两位小数
2025-07-15 19:01:52.446 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 property_allowance: 原始样例=[240.0, 240.0, 200.0], 原始类型=float64
2025-07-15 19:01:52.446 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 property_allowance: 样例=[240.0, 240.0, 200.0], 类型=float64
2025-07-15 19:01:52.446 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 property_allowance 格式化完成，保留两位小数
2025-07-15 19:01:52.446 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 communication_allowance: 原始样例=[50.0, nan, nan], 原始类型=float64
2025-07-15 19:01:52.446 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 communication_allowance: 样例=[50.0, 0.0, 0.0], 类型=float64
2025-07-15 19:01:52.446 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 communication_allowance 格式化完成，保留两位小数
2025-07-15 19:01:52.446 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 performance_bonus_2025: 原始样例=[2500.0, 1000.0, 1000.0], 原始类型=float64
2025-07-15 19:01:52.446 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 performance_bonus_2025: 样例=[2500.0, 1000.0, 1000.0], 类型=float64
2025-07-15 19:01:52.446 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 performance_bonus_2025 格式化完成，保留两位小数
2025-07-15 19:01:52.446 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 provident_fund_2025: 原始样例=[2097.0, 1860.0, 1984.0], 原始类型=float64
2025-07-15 19:01:52.446 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 provident_fund_2025: 样例=[2097.0, 1860.0, 1984.0], 类型=float64
2025-07-15 19:01:52.446 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 provident_fund_2025 格式化完成，保留两位小数
2025-07-15 19:01:52.446 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 housing_allowance: 原始样例=[271.9745083294993, 189.0, 174.16354166666667], 原始类型=float64
2025-07-15 19:01:52.446 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 housing_allowance: 样例=[271.97, 189.0, 174.16], 类型=float64
2025-07-15 19:01:52.446 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 housing_allowance 格式化完成，保留两位小数
2025-07-15 19:01:52.446 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 car_allowance: 原始样例=[None, None, None], 原始类型=object
2025-07-15 19:01:52.446 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 car_allowance: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-15 19:01:52.446 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 car_allowance 格式化完成，保留两位小数
2025-07-15 19:01:52.446 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 supplement: 原始样例=[None, None, None], 原始类型=object
2025-07-15 19:01:52.446 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 supplement: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-15 19:01:52.446 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 supplement 格式化完成，保留两位小数
2025-07-15 19:01:52.446 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 advance: 原始样例=[nan, nan, 2000.0], 原始类型=float64
2025-07-15 19:01:52.446 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 advance: 样例=[0.0, 0.0, 2000.0], 类型=float64
2025-07-15 19:01:52.461 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 advance 格式化完成，保留两位小数
2025-07-15 19:01:52.461 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 total_salary: 原始样例=[12288.9745083295, 9897.0, 6240.163541666667], 原始类型=float64
2025-07-15 19:01:52.461 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 total_salary: 样例=[12288.97, 9897.0, 6240.16], 类型=float64
2025-07-15 19:01:52.461 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 total_salary 格式化完成，保留两位小数
2025-07-15 19:01:52.461 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 pension_insurance: 原始样例=[1525.8000000000002, 1140.53, 1113.75], 原始类型=float64
2025-07-15 19:01:52.461 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 pension_insurance: 样例=[1525.8, 1140.53, 1113.75], 类型=float64
2025-07-15 19:01:52.461 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 pension_insurance 格式化完成，保留两位小数
2025-07-15 19:01:52.461 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 2025年岗位工资 不存在于数据框中
2025-07-15 19:01:52.461 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 2025年薪级工资 不存在于数据框中
2025-07-15 19:01:52.461 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 津贴 不存在于数据框中
2025-07-15 19:01:52.461 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 结余津贴 不存在于数据框中
2025-07-15 19:01:52.461 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 2025年基础性绩效 不存在于数据框中
2025-07-15 19:01:52.477 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 卫生费 不存在于数据框中
2025-07-15 19:01:52.477 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 交通补贴 不存在于数据框中
2025-07-15 19:01:52.477 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 物业补贴 不存在于数据框中
2025-07-15 19:01:52.477 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 通讯补贴 不存在于数据框中
2025-07-15 19:01:52.477 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 2025年奖励性绩效预发 不存在于数据框中
2025-07-15 19:01:52.477 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 2025公积金 不存在于数据框中
2025-07-15 19:01:52.477 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 住房补贴 不存在于数据框中
2025-07-15 19:01:52.477 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 车补 不存在于数据框中
2025-07-15 19:01:52.477 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 补发 不存在于数据框中
2025-07-15 19:01:52.477 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 借支 不存在于数据框中
2025-07-15 19:01:52.477 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 应发工资 不存在于数据框中
2025-07-15 19:01:52.477 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 代扣代存养老保险 不存在于数据框中
2025-07-15 19:01:52.477 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:711 | 🔧 [特殊字段] 开始处理表类型 active_employees 的月份、年份字段
2025-07-15 19:01:52.477 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:731 | 🔧 [特殊字段] 处理月份字段 month: 原始样例=['2025-07', '2025-07', '2025-07'], 原始类型=object
2025-07-15 19:01:52.477 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:774 | 🔧 [特殊字段] 处理后月份字段 month: 样例=['07', '07', '07']
2025-07-15 19:01:52.477 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:785 | 🔧 [验证成功] 月份字段 month 格式化完成，所有值为有效月份格式
2025-07-15 19:01:52.477 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:805 | 🔧 [特殊字段] 处理年份字段 year: 原始样例=[2025, 2025, 2025], 原始类型=int64
2025-07-15 19:01:52.477 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:845 | 🔧 [特殊字段] 处理后年份字段 year: 样例=['2025', '2025', '2025']
2025-07-15 19:01:52.477 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:856 | 🔧 [验证成功] 年份字段 year 格式化完成，所有值为有效年份格式
2025-07-15 19:01:52.477 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:866 | 🔧 [特殊字段] 月份、年份字段处理完成
2025-07-15 19:01:52.477 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:291 | 数据格式化完成: 50行, 24列
2025-07-15 19:01:52.509 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3558 | 最大可见行数已更新: 50 -> 50
2025-07-15 19:01:52.509 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:3611 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-15 19:01:52.509 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2235 | 🔧 [修复标识] 字段映射生效，样例: ['employee_id', 'employee_name', 'department'] -> ['工号', '姓名', '部门名称']
2025-07-15 19:01:52.509 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第0行数据工号: 19990089
2025-07-15 19:01:52.509 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第1行数据工号: 20161565
2025-07-15 19:01:52.509 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第2行数据工号: 20191782
2025-07-15 19:01:52.509 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第3行数据工号: 20151515
2025-07-15 19:01:52.509 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第4行数据工号: 20181640
2025-07-15 19:01:52.509 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:457 | 🔧 [排序修复] 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-15 19:01:52.509 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:424 | 表格数据已设置: 50 行, 24 列
2025-07-15 19:01:52.509 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2361 | 🔧 [表格调试] 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-15 19:01:52.509 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2368 | 🔧 [表格调试] ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-15 19:01:52.509 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2361 | 🔧 [表格调试] 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-15 19:01:52.509 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2368 | 🔧 [表格调试] ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-15 19:01:52.564 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2172 | 🔧 [性能分析] 表格数据设置完成: 50 行, 最大可见行数: 50, 总耗时: 86.61ms
2025-07-15 19:01:52.571 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 初始化和状态: 32.33ms (37.3%)
2025-07-15 19:01:52.571 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 字段映射: 0.00ms (0.0%)
2025-07-15 19:01:52.571 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 数据模型更新: 0.00ms (0.0%)
2025-07-15 19:01:52.571 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 表头设置: 0.00ms (0.0%)
2025-07-15 19:01:52.571 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 设置行数: 0.00ms (0.0%)
2025-07-15 19:01:52.571 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 填充可见数据: 54.28ms (62.7%)
2025-07-15 19:01:52.571 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 调整列宽: 0.00ms (0.0%)
2025-07-15 19:01:52.571 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 设置行号: 0.00ms (0.0%)
2025-07-15 19:01:52.571 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2180 | 🔧 [性能分析] 最耗时步骤: 填充可见数据 (54.28ms)
2025-07-15 19:01:52.571 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-15 19:01:52.571 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:1049 | 🔧 [紧急修复] 分页处理完成: 第1页, 50条记录
2025-07-15 19:01:52.571 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3464 | 🔧 [紧急修复] 新架构分页: 第1页
2025-07-15 19:01:52.571 | INFO     | src.services.table_data_service:load_table_data:291 | 加载表格数据: salary_data_2025_07_active_employees, 页码: 1
2025-07-15 19:01:52.586 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:4784 | 🔧 [修复标识] 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-15 19:01:52.586 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3558 | 最大可见行数已更新: 50 -> 50
2025-07-15 19:01:52.586 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:3611 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-15 19:01:52.586 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第0行数据工号: 19990089.0
2025-07-15 19:01:52.586 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第1行数据工号: 20161565.0
2025-07-15 19:01:52.586 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第2行数据工号: 20191782.0
2025-07-15 19:01:52.586 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第3行数据工号: 20151515.0
2025-07-15 19:01:52.586 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第4行数据工号: 20181640.0
2025-07-15 19:01:52.586 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:457 | 🔧 [排序修复] 可见行数据顺序: ['19990089.0', '20161565.0', '20191782.0', '20151515.0', '20181640.0']
2025-07-15 19:01:52.586 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:424 | 表格数据已设置: 50 行, 24 列
2025-07-15 19:01:52.602 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2361 | 🔧 [表格调试] 开始格式化ID字段: 列=工号, 原值=19990089.0, 类型=<class 'str'>
2025-07-15 19:01:52.602 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2368 | 🔧 [表格调试] ID字段格式化完成: 列=工号, 原值=19990089.0, 格式化后=19990089.0
2025-07-15 19:01:52.602 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2361 | 🔧 [表格调试] 开始格式化ID字段: 列=人员类别代码, 原值=1.0, 类型=<class 'str'>
2025-07-15 19:01:52.602 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2368 | 🔧 [表格调试] ID字段格式化完成: 列=人员类别代码, 原值=1.0, 格式化后=1.0
2025-07-15 19:01:52.618 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2172 | 🔧 [性能分析] 表格数据设置完成: 50 行, 最大可见行数: 50, 总耗时: 31.33ms
2025-07-15 19:01:52.618 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 初始化和状态: 0.00ms (0.0%)
2025-07-15 19:01:52.618 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 字段映射: 0.00ms (0.0%)
2025-07-15 19:01:52.618 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 数据模型更新: 0.00ms (0.0%)
2025-07-15 19:01:52.618 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 表头设置: 0.00ms (0.0%)
2025-07-15 19:01:52.618 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 设置行数: 0.00ms (0.0%)
2025-07-15 19:01:52.618 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 填充可见数据: 31.33ms (100.0%)
2025-07-15 19:01:52.618 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 调整列宽: 0.00ms (0.0%)
2025-07-15 19:01:52.618 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 设置行号: 0.00ms (0.0%)
2025-07-15 19:01:52.618 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2180 | 🔧 [性能分析] 最耗时步骤: 填充可见数据 (31.33ms)
2025-07-15 19:01:52.618 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-15 19:01:52.618 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3526 | 🔧 [紧急修复] 新架构分页成功: 第1页, 50行
2025-07-15 19:01:52.618 | INFO     | src.gui.widgets.pagination_widget:set_current_page:330 | 页码切换到: 1
2025-07-15 19:01:52.618 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-15 19:01:52.618 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:975 | 🔧 [紧急修复] 开始分页处理: 第2页, 表名: salary_data_2025_07_active_employees
2025-07-15 19:01:52.618 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:439 | 正在从表 salary_data_2025_07_active_employees 分页获取数据: 第2页, 每页50条
2025-07-15 19:01:52.633 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:462 | 成功从表 salary_data_2025_07_active_employees 获取第2页数据: 50 行，总计1396行
2025-07-15 19:01:52.744 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:133 | 使用已存在的配置文件: state/data/field_mappings.json
2025-07-15 19:01:52.744 | INFO     | src.modules.data_import.config_sync_manager:__init__:70 | 配置同步管理器初始化完成
2025-07-15 19:01:52.744 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:1162 | 🔧 [修复] 没有找到匹配的字段映射
2025-07-15 19:01:52.744 | INFO     | src.gui.prototype.prototype_main_window:_apply_system_field_filtering:1194 | 🔧 [修复] 过滤了 4 个系统字段
2025-07-15 19:01:52.852 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:133 | 使用已存在的配置文件: state/data/field_mappings.json
2025-07-15 19:01:52.852 | INFO     | src.modules.data_import.config_sync_manager:__init__:70 | 配置同步管理器初始化完成
2025-07-15 19:01:52.852 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:1231 | 🔧 [修复] 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-15 19:01:52.852 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:238 | 开始格式化工资表数据 - 表类型: salary_data_2025_07_active_employees
2025-07-15 19:01:52.852 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:251 | 原始数据: 50行, 24列
2025-07-15 19:01:52.852 | INFO     | src.modules.data_storage.salary_data_formatter:_extract_table_type_from_name:335 | 🔧 [表名映射] 通过关键词 'active_employees' 匹配到表类型: active_employees
2025-07-15 19:01:52.852 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:255 | 🔧 [表名映射] 输入表名: salary_data_2025_07_active_employees, 提取的表类型: active_employees
2025-07-15 19:01:52.852 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:259 | 使用表类型配置: active_employees, 配置字段: ['id_fields', 'string_fields', 'integer_fields', 'float_fields', 'special_fields']
2025-07-15 19:01:52.852 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:263 | 🔧 [格式化调试] 开始处理ID字段: ['employee_id', 'employee_type_code', '工号', '人员类别代码']
2025-07-15 19:01:52.852 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:268 | 🔧 [格式化调试] 处理前 employee_id 字段样例: ['20171604.0', '20181638.0', '19930191.0']
2025-07-15 19:01:52.852 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:268 | 🔧 [格式化调试] 处理前 employee_type_code 字段样例: ['1.0', '1.0', '1.0']
2025-07-15 19:01:52.852 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:377 | 🔧 [强化调试] 处理ID字段 employee_id: 原始样例=['20171604.0', '20181638.0', '19930191.0'], 原始类型=object
2025-07-15 19:01:52.852 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:426 | 🔧 [强化调试] 清理后ID字段 employee_id: 样例=['20171604', '20181638', '19930191']
2025-07-15 19:01:52.852 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:443 | 🔧 [强化调试] 最终ID字段 employee_id: 样例=['20171604', '20181638', '19930191']
2025-07-15 19:01:52.852 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:454 | 🔧 [验证成功] ID字段 employee_id 格式化完成，无小数点问题
2025-07-15 19:01:52.852 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:377 | 🔧 [强化调试] 处理ID字段 employee_type_code: 原始样例=['1.0', '1.0', '1.0'], 原始类型=object
2025-07-15 19:01:52.852 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:426 | 🔧 [强化调试] 清理后ID字段 employee_type_code: 样例=['1', '1', '1']
2025-07-15 19:01:52.852 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:443 | 🔧 [强化调试] 最终ID字段 employee_type_code: 样例=['01', '01', '01']
2025-07-15 19:01:52.852 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:454 | 🔧 [验证成功] ID字段 employee_type_code 格式化完成，无小数点问题
2025-07-15 19:01:52.852 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_id_fields:461 | 🔧 [警告] ID字段 工号 不存在于数据框中
2025-07-15 19:01:52.852 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_id_fields:461 | 🔧 [警告] ID字段 人员类别代码 不存在于数据框中
2025-07-15 19:01:52.852 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:274 | 🔧 [格式化调试] 处理后 employee_id 字段样例: ['20171604', '20181638', '19930191']
2025-07-15 19:01:52.868 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:274 | 🔧 [格式化调试] 处理后 employee_type_code 字段样例: ['01', '01', '01']
2025-07-15 19:01:52.868 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:472 | 🔧 [强化调试] 开始格式化字符串字段: ['employee_id', 'employee_name', 'department', 'employee_type_code', 'employee_type', '工号', '姓名', '部门名称', '人员类别代码', '人员类别']
2025-07-15 19:01:52.868 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 employee_id: 原始样例=['20171604', '20181638', '19930191'], 原始类型=object
2025-07-15 19:01:52.868 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:504 | 🔧 [修复] 工号字段 employee_id 已特殊处理，去除小数点格式
2025-07-15 19:01:52.868 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 employee_id: 样例=['20171604', '20181638', '19930191'], 类型=object
2025-07-15 19:01:52.868 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 employee_id 格式化完成，无小数点问题
2025-07-15 19:01:52.868 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 employee_name: 原始样例=['周荔', '申子宇', '周浩'], 原始类型=object
2025-07-15 19:01:52.868 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 employee_name: 样例=['周荔', '申子宇', '周浩'], 类型=object
2025-07-15 19:01:52.868 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 employee_name 格式化完成，无小数点问题
2025-07-15 19:01:52.883 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 department: 原始样例=['资源环境科学与工程学院', '资源环境科学与工程学院', '资源环境科学与工程学院'], 原始类型=object
2025-07-15 19:01:52.883 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 department: 样例=['资源环境科学与工程学院', '资源环境科学与工程学院', '资源环境科学与工程学院'], 类型=object
2025-07-15 19:01:52.883 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 department 格式化完成，无小数点问题
2025-07-15 19:01:52.883 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 employee_type_code: 原始样例=['01', '01', '01'], 原始类型=object
2025-07-15 19:01:52.883 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 employee_type_code: 样例=['01', '01', '01'], 类型=object
2025-07-15 19:01:52.883 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 employee_type_code 格式化完成，无小数点问题
2025-07-15 19:01:52.883 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 employee_type: 原始样例=['教学单位专技人员', '教学单位专技人员', '教学院其它人员'], 原始类型=object
2025-07-15 19:01:52.883 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 employee_type: 样例=['教学单位专技人员', '教学单位专技人员', '教学院其它人员'], 类型=object
2025-07-15 19:01:52.883 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 employee_type 格式化完成，无小数点问题
2025-07-15 19:01:52.883 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 工号 不存在于数据框中
2025-07-15 19:01:52.883 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 姓名 不存在于数据框中
2025-07-15 19:01:52.883 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 部门名称 不存在于数据框中
2025-07-15 19:01:52.883 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 人员类别代码 不存在于数据框中
2025-07-15 19:01:52.883 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 人员类别 不存在于数据框中
2025-07-15 19:01:52.883 | INFO     | src.modules.data_storage.salary_data_formatter:_format_integer_fields:604 | 🔧 [强化调试] 开始格式化整型字段: []
2025-07-15 19:01:52.883 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:655 | 🔧 [强化调试] 开始格式化浮点型字段: ['position_salary_2025', 'grade_salary_2025', 'allowance', 'balance_allowance', 'basic_performance_2025', 'health_fee', 'transport_allowance', 'property_allowance', 'communication_allowance', 'performance_bonus_2025', 'provident_fund_2025', 'housing_allowance', 'car_allowance', 'supplement', 'advance', 'total_salary', 'pension_insurance', '2025年岗位工资', '2025年薪级工资', '津贴', '结余津贴', '2025年基础性绩效', '卫生费', '交通补贴', '物业补贴', '通讯补贴', '2025年奖励性绩效预发', '2025公积金', '住房补贴', '车补', '补发', '借支', '应发工资', '代扣代存养老保险']
2025-07-15 19:01:52.883 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 position_salary_2025: 原始样例=[1925.0, 3030.0, 3455.0], 原始类型=float64
2025-07-15 19:01:52.883 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 position_salary_2025: 样例=[1925.0, 3030.0, 3455.0], 类型=float64
2025-07-15 19:01:52.883 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 position_salary_2025 格式化完成，保留两位小数
2025-07-15 19:01:52.883 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 grade_salary_2025: 原始样例=[1427.0, 1515.0, 3391.0], 原始类型=float64
2025-07-15 19:01:52.883 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 grade_salary_2025: 样例=[1427.0, 1515.0, 3391.0], 类型=float64
2025-07-15 19:01:52.883 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 grade_salary_2025 格式化完成，保留两位小数
2025-07-15 19:01:52.883 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 allowance: 原始样例=[0.0, 0.0, 134.0], 原始类型=float64
2025-07-15 19:01:52.899 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 allowance: 样例=[0.0, 0.0, 134.0], 类型=float64
2025-07-15 19:01:52.899 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 allowance 格式化完成，保留两位小数
2025-07-15 19:01:52.899 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 balance_allowance: 原始样例=[56.0, 56.0, 56.0], 原始类型=float64
2025-07-15 19:01:52.899 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 balance_allowance: 样例=[56.0, 56.0, 56.0], 类型=float64
2025-07-15 19:01:52.899 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 balance_allowance 格式化完成，保留两位小数
2025-07-15 19:01:52.899 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 basic_performance_2025: 原始样例=[2824.0, 3466.0, 4108.0], 原始类型=float64
2025-07-15 19:01:52.899 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 basic_performance_2025: 样例=[2824.0, 3466.0, 4108.0], 类型=float64
2025-07-15 19:01:52.899 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 basic_performance_2025 格式化完成，保留两位小数
2025-07-15 19:01:52.899 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 health_fee: 原始样例=[0.0, nan, 0.0], 原始类型=float64
2025-07-15 19:01:52.915 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 health_fee: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-15 19:01:52.915 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 health_fee 格式化完成，保留两位小数
2025-07-15 19:01:52.915 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 transport_allowance: 原始样例=[220.0, 220.0, 220.0], 原始类型=float64
2025-07-15 19:01:52.915 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 transport_allowance: 样例=[220.0, 220.0, 220.0], 类型=float64
2025-07-15 19:01:52.915 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 transport_allowance 格式化完成，保留两位小数
2025-07-15 19:01:52.915 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 property_allowance: 原始样例=[200.0, 240.0, 240.0], 原始类型=float64
2025-07-15 19:01:52.915 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 property_allowance: 样例=[200.0, 240.0, 240.0], 类型=float64
2025-07-15 19:01:52.915 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 property_allowance 格式化完成，保留两位小数
2025-07-15 19:01:52.915 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 communication_allowance: 原始样例=[nan, nan, 50.0], 原始类型=float64
2025-07-15 19:01:52.915 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 communication_allowance: 样例=[0.0, 0.0, 50.0], 类型=float64
2025-07-15 19:01:52.915 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 communication_allowance 格式化完成，保留两位小数
2025-07-15 19:01:52.915 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 performance_bonus_2025: 原始样例=[1000.0, 0.0, 2400.0], 原始类型=float64
2025-07-15 19:01:52.915 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 performance_bonus_2025: 样例=[1000.0, 0.0, 2400.0], 类型=float64
2025-07-15 19:01:52.915 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 performance_bonus_2025 格式化完成，保留两位小数
2025-07-15 19:01:52.915 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 provident_fund_2025: 原始样例=[1428.0, 2216.0, 2274.0], 原始类型=float64
2025-07-15 19:01:52.915 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 provident_fund_2025: 样例=[1428.0, 2216.0, 2274.0], 类型=float64
2025-07-15 19:01:52.915 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 provident_fund_2025 格式化完成，保留两位小数
2025-07-15 19:01:52.915 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 housing_allowance: 原始样例=[188.76571428819443, 204.8512760416667, 315.672219058336], 原始类型=float64
2025-07-15 19:01:52.915 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 housing_allowance: 样例=[188.77, 204.85, 315.67], 类型=float64
2025-07-15 19:01:52.915 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 housing_allowance 格式化完成，保留两位小数
2025-07-15 19:01:52.915 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 car_allowance: 原始样例=[None, None, None], 原始类型=object
2025-07-15 19:01:52.915 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 car_allowance: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-15 19:01:52.915 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 car_allowance 格式化完成，保留两位小数
2025-07-15 19:01:52.915 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 supplement: 原始样例=[None, None, None], 原始类型=object
2025-07-15 19:01:52.915 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 supplement: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-15 19:01:52.915 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 supplement 格式化完成，保留两位小数
2025-07-15 19:01:52.915 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 advance: 原始样例=[nan, 2799.4, nan], 原始类型=float64
2025-07-15 19:01:52.930 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 advance: 样例=[0.0, 2799.4, 0.0], 类型=float64
2025-07-15 19:01:52.930 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 advance 格式化完成，保留两位小数
2025-07-15 19:01:52.930 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 total_salary: 原始样例=[7840.765714288194, 5932.451276041667, 14369.672219058337], 原始类型=float64
2025-07-15 19:01:52.930 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 total_salary: 样例=[7840.77, 5932.45, 14369.67], 类型=float64
2025-07-15 19:01:52.930 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 total_salary 格式化完成，保留两位小数
2025-07-15 19:01:52.930 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 pension_insurance: 原始样例=[977.64, 1416.96, 1867.56], 原始类型=float64
2025-07-15 19:01:52.930 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 pension_insurance: 样例=[977.64, 1416.96, 1867.56], 类型=float64
2025-07-15 19:01:52.930 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 pension_insurance 格式化完成，保留两位小数
2025-07-15 19:01:52.930 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 2025年岗位工资 不存在于数据框中
2025-07-15 19:01:52.930 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 2025年薪级工资 不存在于数据框中
2025-07-15 19:01:52.930 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 津贴 不存在于数据框中
2025-07-15 19:01:52.930 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 结余津贴 不存在于数据框中
2025-07-15 19:01:52.930 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 2025年基础性绩效 不存在于数据框中
2025-07-15 19:01:52.930 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 卫生费 不存在于数据框中
2025-07-15 19:01:52.930 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 交通补贴 不存在于数据框中
2025-07-15 19:01:52.930 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 物业补贴 不存在于数据框中
2025-07-15 19:01:52.930 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 通讯补贴 不存在于数据框中
2025-07-15 19:01:52.930 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 2025年奖励性绩效预发 不存在于数据框中
2025-07-15 19:01:52.930 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 2025公积金 不存在于数据框中
2025-07-15 19:01:52.930 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 住房补贴 不存在于数据框中
2025-07-15 19:01:52.930 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 车补 不存在于数据框中
2025-07-15 19:01:52.930 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 补发 不存在于数据框中
2025-07-15 19:01:52.930 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 借支 不存在于数据框中
2025-07-15 19:01:52.930 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 应发工资 不存在于数据框中
2025-07-15 19:01:52.950 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 代扣代存养老保险 不存在于数据框中
2025-07-15 19:01:52.950 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:711 | 🔧 [特殊字段] 开始处理表类型 active_employees 的月份、年份字段
2025-07-15 19:01:52.950 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:731 | 🔧 [特殊字段] 处理月份字段 month: 原始样例=['2025-07', '2025-07', '2025-07'], 原始类型=object
2025-07-15 19:01:52.950 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:774 | 🔧 [特殊字段] 处理后月份字段 month: 样例=['07', '07', '07']
2025-07-15 19:01:52.950 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:785 | 🔧 [验证成功] 月份字段 month 格式化完成，所有值为有效月份格式
2025-07-15 19:01:52.950 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:805 | 🔧 [特殊字段] 处理年份字段 year: 原始样例=[2025, 2025, 2025], 原始类型=int64
2025-07-15 19:01:52.950 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:845 | 🔧 [特殊字段] 处理后年份字段 year: 样例=['2025', '2025', '2025']
2025-07-15 19:01:52.950 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:856 | 🔧 [验证成功] 年份字段 year 格式化完成，所有值为有效年份格式
2025-07-15 19:01:52.950 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:866 | 🔧 [特殊字段] 月份、年份字段处理完成
2025-07-15 19:01:52.965 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:291 | 数据格式化完成: 50行, 24列
2025-07-15 19:01:52.965 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3558 | 最大可见行数已更新: 50 -> 50
2025-07-15 19:01:52.965 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:3611 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-15 19:01:52.965 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2235 | 🔧 [修复标识] 字段映射生效，样例: ['employee_id', 'employee_name', 'department'] -> ['工号', '姓名', '部门名称']
2025-07-15 19:01:52.965 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第0行数据工号: 20171604
2025-07-15 19:01:52.965 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第1行数据工号: 20181638
2025-07-15 19:01:52.965 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第2行数据工号: 19930191
2025-07-15 19:01:52.965 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第3行数据工号: 20181669
2025-07-15 19:01:52.965 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第4行数据工号: 20191722
2025-07-15 19:01:52.965 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:457 | 🔧 [排序修复] 可见行数据顺序: ['20171604', '20181638', '19930191', '20181669', '20191722']
2025-07-15 19:01:52.965 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:424 | 表格数据已设置: 50 行, 24 列
2025-07-15 19:01:52.965 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2361 | 🔧 [表格调试] 开始格式化ID字段: 列=工号, 原值=20171604, 类型=<class 'str'>
2025-07-15 19:01:52.965 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2368 | 🔧 [表格调试] ID字段格式化完成: 列=工号, 原值=20171604, 格式化后=20171604
2025-07-15 19:01:52.965 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2361 | 🔧 [表格调试] 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-15 19:01:52.965 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2368 | 🔧 [表格调试] ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-15 19:01:52.993 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2172 | 🔧 [性能分析] 表格数据设置完成: 50 行, 最大可见行数: 50, 总耗时: 28.41ms
2025-07-15 19:01:52.993 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 初始化和状态: 0.00ms (0.0%)
2025-07-15 19:01:52.993 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 字段映射: 0.00ms (0.0%)
2025-07-15 19:01:52.993 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 数据模型更新: 0.00ms (0.0%)
2025-07-15 19:01:52.993 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 表头设置: 0.00ms (0.0%)
2025-07-15 19:01:52.993 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 设置行数: 0.00ms (0.0%)
2025-07-15 19:01:52.993 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 填充可见数据: 28.41ms (100.0%)
2025-07-15 19:01:52.993 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 调整列宽: 0.00ms (0.0%)
2025-07-15 19:01:52.993 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 设置行号: 0.00ms (0.0%)
2025-07-15 19:01:52.993 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2180 | 🔧 [性能分析] 最耗时步骤: 填充可见数据 (28.41ms)
2025-07-15 19:01:52.993 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:1049 | 🔧 [紧急修复] 分页处理完成: 第2页, 50条记录
2025-07-15 19:01:52.993 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3464 | 🔧 [紧急修复] 新架构分页: 第2页
2025-07-15 19:01:52.993 | INFO     | src.services.table_data_service:load_table_data:291 | 加载表格数据: salary_data_2025_07_active_employees, 页码: 2
2025-07-15 19:01:52.993 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:4784 | 🔧 [修复标识] 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-15 19:01:53.009 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3558 | 最大可见行数已更新: 50 -> 50
2025-07-15 19:01:53.009 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:3611 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-15 19:01:53.009 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第0行数据工号: 20171604.0
2025-07-15 19:01:53.009 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第1行数据工号: 20181638.0
2025-07-15 19:01:53.009 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第2行数据工号: 19930191.0
2025-07-15 19:01:53.009 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第3行数据工号: 20181669.0
2025-07-15 19:01:53.009 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第4行数据工号: 20191722.0
2025-07-15 19:01:53.009 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:457 | 🔧 [排序修复] 可见行数据顺序: ['20171604.0', '20181638.0', '19930191.0', '20181669.0', '20191722.0']
2025-07-15 19:01:53.009 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:424 | 表格数据已设置: 50 行, 24 列
2025-07-15 19:01:53.009 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2361 | 🔧 [表格调试] 开始格式化ID字段: 列=工号, 原值=20171604.0, 类型=<class 'str'>
2025-07-15 19:01:53.009 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2368 | 🔧 [表格调试] ID字段格式化完成: 列=工号, 原值=20171604.0, 格式化后=20171604.0
2025-07-15 19:01:53.009 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2361 | 🔧 [表格调试] 开始格式化ID字段: 列=人员类别代码, 原值=1.0, 类型=<class 'str'>
2025-07-15 19:01:53.009 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2368 | 🔧 [表格调试] ID字段格式化完成: 列=人员类别代码, 原值=1.0, 格式化后=1.0
2025-07-15 19:01:53.040 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2172 | 🔧 [性能分析] 表格数据设置完成: 50 行, 最大可见行数: 50, 总耗时: 30.34ms
2025-07-15 19:01:53.040 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 初始化和状态: 0.00ms (0.0%)
2025-07-15 19:01:53.040 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 字段映射: 0.00ms (0.0%)
2025-07-15 19:01:53.040 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 数据模型更新: 0.00ms (0.0%)
2025-07-15 19:01:53.040 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 表头设置: 0.00ms (0.0%)
2025-07-15 19:01:53.040 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 设置行数: 0.00ms (0.0%)
2025-07-15 19:01:53.040 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 填充可见数据: 30.34ms (100.0%)
2025-07-15 19:01:53.040 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 调整列宽: 0.00ms (0.0%)
2025-07-15 19:01:53.040 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 设置行号: 0.00ms (0.0%)
2025-07-15 19:01:53.040 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2180 | 🔧 [性能分析] 最耗时步骤: 填充可见数据 (30.34ms)
2025-07-15 19:01:53.040 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-15 19:01:53.040 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3526 | 🔧 [紧急修复] 新架构分页成功: 第2页, 50行
2025-07-15 19:01:53.040 | INFO     | src.gui.widgets.pagination_widget:set_current_page:330 | 页码切换到: 2
2025-07-15 19:01:53.040 | INFO     | src.gui.multi_column_sort_manager:add_sort_column:274 | 添加排序列: 6(grade_salary_2025) -> ascending, 当前排序列数: 1
2025-07-15 19:01:53.040 | INFO     | src.gui.multi_column_sort_manager:_on_sort_indicator_changed:211 | 排序指示器变化: 列6(grade_salary_2025) -> ascending
2025-07-15 19:01:53.055 | INFO     | src.gui.prototype.prototype_main_window:_on_sort_indicator_changed:3148 | 🔧 [全局排序] 排序请求: 列6, 顺序ASC
2025-07-15 19:01:53.055 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:3315 | 🔧 [排序] 字段转换: 2025年薪级工资 -> grade_salary_2025
2025-07-15 19:01:53.055 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:3334 | 🔧 [排序] 已发布排序请求事件: salary_data_2025_07_active_employees, 1列
2025-07-15 19:01:53.055 | INFO     | src.services.table_data_service:_handle_sort_request:135 | 处理排序请求: salary_data_2025_07_active_employees
2025-07-15 19:01:53.055 | INFO     | src.gui.prototype.prototype_main_window:_on_sort_indicator_changed:3181 | 🔧 [新架构排序] 排序完成: 2025年薪级工资 ascending, 保持第1页
2025-07-15 19:01:53.055 | INFO     | src.core.unified_data_request_manager:request_table_data:177 | 开始处理数据请求: salary_data_2025_07_active_employees, 类型: sort_change
2025-07-15 19:01:53.055 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:634 | 🔧 [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_07_active_employees' 中（类型: REAL）
2025-07-15 19:01:53.055 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:487 | 正在从表 salary_data_2025_07_active_employees 分页获取数据（支持排序）: 第1页, 每页50条, 排序=1列
2025-07-15 19:01:53.055 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:516 | 🔧 [排序修复] 准备排序列: grade_salary_2025
2025-07-15 19:01:53.055 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:634 | 🔧 [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_07_active_employees' 中（类型: REAL）
2025-07-15 19:01:53.055 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:523 | 🔧 [排序修复] 列 'grade_salary_2025' 验证通过，开始排序
2025-07-15 19:01:53.055 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:532 | 🔧 [排序修复] 数值列排序: grade_salary_2025 ASC
2025-07-15 19:01:53.055 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:539 | 🔧 [排序修复] 添加排序子句: CAST("grade_salary_2025" AS REAL) ASC
2025-07-15 19:01:53.055 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:547 | 🔧 [排序调试] 执行的完整SQL查询: SELECT * FROM "salary_data_2025_07_active_employees" ORDER BY CAST("grade_salary_2025" AS REAL) ASC LIMIT 50 OFFSET 0
2025-07-15 19:01:53.071 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:559 | 🔧 [排序调试] 查询结果中 grade_salary_2025 的前10个值: [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 596.0]
2025-07-15 19:01:53.071 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:561 | 成功从表 salary_data_2025_07_active_employees 获取第1页数据（含排序）: 50 行，总计1396行
2025-07-15 19:01:53.071 | INFO     | src.core.unified_data_request_manager:request_table_data:226 | 数据请求处理完成: 28字段, 50行, 耗时15.7ms
2025-07-15 19:01:53.071 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3004 | 🆕 接收到新架构数据更新事件: salary_data_2025_07_active_employees
2025-07-15 19:01:53.071 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3029 | 数据内容: 50行 x 28列
2025-07-15 19:01:53.071 | INFO     | src.gui.prototype.prototype_main_window:set_data:642 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-07-15 19:01:53.184 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:133 | 使用已存在的配置文件: state/data/field_mappings.json
2025-07-15 19:01:53.184 | INFO     | src.modules.data_import.config_sync_manager:__init__:70 | 配置同步管理器初始化完成
2025-07-15 19:01:53.184 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:1162 | 🔧 [修复] 没有找到匹配的字段映射
2025-07-15 19:01:53.184 | INFO     | src.gui.prototype.prototype_main_window:_apply_system_field_filtering:1194 | 🔧 [修复] 过滤了 4 个系统字段
2025-07-15 19:01:53.292 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:133 | 使用已存在的配置文件: state/data/field_mappings.json
2025-07-15 19:01:53.292 | INFO     | src.modules.data_import.config_sync_manager:__init__:70 | 配置同步管理器初始化完成
2025-07-15 19:01:53.292 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:1231 | 🔧 [修复] 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-15 19:01:53.313 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3558 | 最大可见行数已更新: 50 -> 50
2025-07-15 19:01:53.313 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:3611 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-15 19:01:53.313 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2235 | 🔧 [修复标识] 字段映射生效，样例: ['employee_id', 'employee_name', 'department'] -> ['工号', '姓名', '部门名称']
2025-07-15 19:01:53.313 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第0行数据工号: 19961347.0
2025-07-15 19:01:53.313 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第1行数据工号: 20251003.0
2025-07-15 19:01:53.313 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第2行数据工号: 20251006.0
2025-07-15 19:01:53.313 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第3行数据工号: 20251007.0
2025-07-15 19:01:53.313 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第4行数据工号: 20251008.0
2025-07-15 19:01:53.313 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:457 | 🔧 [排序修复] 可见行数据顺序: ['19961347.0', '20251003.0', '20251006.0', '20251007.0', '20251008.0']
2025-07-15 19:01:53.326 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:424 | 表格数据已设置: 50 行, 24 列
2025-07-15 19:01:53.326 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2361 | 🔧 [表格调试] 开始格式化ID字段: 列=工号, 原值=19961347.0, 类型=<class 'str'>
2025-07-15 19:01:53.326 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2368 | 🔧 [表格调试] ID字段格式化完成: 列=工号, 原值=19961347.0, 格式化后=19961347.0
2025-07-15 19:01:53.326 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2361 | 🔧 [表格调试] 开始格式化ID字段: 列=人员类别代码, 原值=17.0, 类型=<class 'str'>
2025-07-15 19:01:53.326 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2368 | 🔧 [表格调试] ID字段格式化完成: 列=人员类别代码, 原值=17.0, 格式化后=17.0
2025-07-15 19:01:53.339 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2172 | 🔧 [性能分析] 表格数据设置完成: 50 行, 最大可见行数: 50, 总耗时: 47.15ms
2025-07-15 19:01:53.339 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 初始化和状态: 21.11ms (44.8%)
2025-07-15 19:01:53.339 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 字段映射: 0.00ms (0.0%)
2025-07-15 19:01:53.339 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 数据模型更新: 13.30ms (28.2%)
2025-07-15 19:01:53.339 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 表头设置: 0.00ms (0.0%)
2025-07-15 19:01:53.339 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 设置行数: 0.00ms (0.0%)
2025-07-15 19:01:53.339 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 填充可见数据: 12.74ms (27.0%)
2025-07-15 19:01:53.355 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 调整列宽: 0.00ms (0.0%)
2025-07-15 19:01:53.355 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2175 | 🔧 [性能分析] - 设置行号: 0.00ms (0.0%)
2025-07-15 19:01:53.355 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2180 | 🔧 [性能分析] 最耗时步骤: 初始化和状态 (21.11ms)
2025-07-15 19:01:53.355 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 50
2025-07-15 19:01:53.355 | INFO     | src.gui.widgets.pagination_widget:set_current_page:330 | 页码切换到: 1
2025-07-15 19:01:53.355 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-15 19:01:53.355 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3055 | 🔧 [分页修复] 数据更新事件设置总记录数: 1396
2025-07-15 19:01:53.355 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3066 | 🔧 [分页修复] 分页状态: 当前页=1, 总页数=28, 总记录数=1396
2025-07-15 19:01:53.355 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3072 | 🔧 [分页修复] 按钮可用性: 下一页=True, 上一页=False
